# MCP Integration Tests

This directory contains integration tests for verifying that MCP tools return responses in the standard MCP format when called through the MCP server.

## Files

### `mcp_integration_tests_tools.py`
The main integration test file that contains:
- **`test_quick_search_tool()`** - Tests the `quick_search` tool with input 'agfunder'
- **`MCPIntegrationTester`** - Class for testing MCP server endpoints
- **MCP format verification** - Validates responses comply with the standard format

### `run_integration_test.py`
A runner script that:
- Checks dependencies (requests library)
- Verifies MCP server connectivity
- Runs the integration tests with proper error handling

### `test_mcp_response_format.py`
Unit tests for the MCP response wrapper utilities (standalone testing).

### `demo_mcp_format.py`
Demonstration script showing old vs new response formats.

## Prerequisites

1. **MCP Server Running**: The MCP server must be running on port 9000
2. **Python Dependencies**: `requests` library for HTTP calls
3. **Tool Availability**: The `quick_search` tool must be available on the server

## Running the Tests

### Option 1: Using the Runner Script (Recommended)
```bash
cd gaia/gaia_sprites/gaia_sprite_mcp
python run_integration_test.py
```

The runner script will:
- ✅ Check if `requests` library is installed
- ✅ Verify MCP server connectivity on port 9000
- ✅ Run the integration test
- ✅ Provide troubleshooting tips if issues occur

### Option 2: Direct Test Execution
```bash
cd gaia/gaia_sprites/gaia_sprite_mcp
python mcp_integration_tests_tools.py
```

## Starting the MCP Server

If the server is not running, start it with:
```bash
cd /path/to/sprite/mcp/server
python sprite_mcp_server.py --transport http --port 9000
```

## Test Details

### What the Test Does

1. **Health Check**: Verifies the MCP server is running and healthy
2. **Tool Discovery**: Lists available tools and confirms `quick_search` exists
3. **Tool Execution**: Calls `quick_search` with arguments `{"query": "agfunder"}`
4. **Format Verification**: Validates the response adheres to standard MCP format

### MCP Format Requirements

The test verifies that responses have this structure:
```json
{
  "success": true,
  "result": {
    "content": [
      {
        "type": "text",
        "text": "{\"tool\": \"quick_search\", \"query\": \"agfunder\", ...}"
      }
    ],
    "structuredContent": {
      "tool": "quick_search",
      "query": "agfunder",
      "results": [...],
      "stats": {...}
    }
  }
}
```

### Validation Checks

The test performs these specific validations:

✅ **Response Structure**:
- Response is a dictionary
- Has `success: true` field
- Contains `result` field

✅ **MCP Format**:
- `result` contains `content` array
- `result` contains `structuredContent` object
- `content[0]` has `type: "text"`
- `content[0]` has valid JSON in `text` field

✅ **Data Integrity**:
- `structuredContent` is not empty
- Text content can be parsed as JSON
- Response contains expected tool data

## Expected Output

### Successful Test Run
```
🚀 Starting MCP Integration Test for quick_search tool
============================================================

1️⃣ Checking server health...
✅ Server is healthy: {'status': 'healthy', 'sprites_available': True, 'tools_count': 45}

2️⃣ Listing available tools...
📋 Available tools: 45
✅ quick_search tool is available

3️⃣ Calling quick_search tool with input 'agfunder'...
✅ Tool call completed successfully

📊 Response Summary:
   - Response type: <class 'dict'>
   - Response keys: ['success', 'result']

4️⃣ Verifying MCP format compliance...

🔍 Verifying MCP format for quick_search response...
✅ Text content is valid JSON
✅ Response adheres to standard MCP format!
   - Has 'content' array with 1 item(s)
   - Has 'structuredContent' with 4 field(s)
   - Text content is valid JSON

🎉 SUCCESS: quick_search tool returns valid MCP format!

📋 Sample Response Data:
   - Tool: quick_search
   - Query: agfunder
   - Results count: 25
   - First result keys: ['name', 'category', 'funding']

============================================================
✅ ALL TESTS PASSED!
✅ MCP format compliance verified!
```

## Troubleshooting

### Server Connection Issues
```
❌ Cannot connect to server: Connection refused
```
**Solution**: Start the MCP server on port 9000

### Missing Dependencies
```
❌ requests library not found
```
**Solution**: Install with `pip install requests`

### Tool Not Found
```
❌ quick_search tool not found. Available tools: [...]
```
**Solution**: Check server configuration and ensure sprites are loaded

### Format Validation Failures
```
❌ Missing 'content' field in MCP response
```
**Solution**: Check that the MCP response wrapper is properly implemented

## Integration with CI/CD

These tests can be integrated into CI/CD pipelines:

```bash
# Start MCP server in background
python sprite_mcp_server.py --transport http --port 9000 &
SERVER_PID=$!

# Wait for server to start
sleep 5

# Run integration tests
python run_integration_test.py

# Clean up
kill $SERVER_PID
```

## Extending the Tests

To add more tool tests, extend the `mcp_integration_tests_tools.py` file:

```python
def test_another_tool():
    """Test another tool with MCP format verification."""
    tester = MCPIntegrationTester()
    response = tester.call_tool("another_tool", {"param": "value"})
    return tester.verify_mcp_format(response, "another_tool")
```

## Related Files

- `mcp_response_wrapper.py` - MCP response formatting utilities
- `sprite_mcp_server_base.py` - Base class with MCP wrapper integration
- `sprite_mcp_server.py` - Main MCP server implementation
