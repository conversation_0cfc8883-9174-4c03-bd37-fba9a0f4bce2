#!/usr/bin/env python3
"""
Simple MCP Integration Test

Tests the MCP server to verify that tools return the standard MCP format.
Uses the existing MCPClientLib for proper MCP protocol handling.
"""

import asyncio
import json
import sys
import os

# Add the project root to Python path
sys.path.insert(0, '/home/<USER>/django-projects/agbase_admin')

from gaia.gaia_ceto.proto_mcp_http.mcp_http_clientlib import MCPClientLib

class SimpleMCPTester:
    """Simple MCP integration tester using MCPClientLib."""
    
    def __init__(self, server_url: str = "http://localhost:9000/mcp"):
        self.server_url = server_url
        self.client = None
    
    async def __aenter__(self):
        """Async context manager entry."""
        self.client = MCPClientLib(debug_callback=self._debug_callback)
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        if self.client:
            try:
                await self.client.cleanup()
            except Exception as e:
                print(f"⚠️  Cleanup warning: {e}")
    
    def _debug_callback(self, level: str, message: str, data=None):
        """Debug callback for MCP client."""
        if level == "error":
            print(f"🔴 {message}")
        elif level == "info":
            print(f"ℹ️  {message}")
    
    async def connect(self) -> bool:
        """Connect to the MCP server."""
        print(f"🔌 Connecting to MCP server at {self.server_url}")
        success = await self.client.connect_to_server(self.server_url)
        
        if success:
            print("✅ Connected successfully!")
            return True
        else:
            print("❌ Failed to connect to server")
            return False
    
    async def list_tools(self):
        """List available tools."""
        tools = self.client.available_tools
        print(f"\n📋 Available tools: {len(tools)}")
        for tool in tools:
            print(f"  • {tool.get('name', 'unknown')}: {tool.get('description', 'No description')}")
        return tools
    
    async def test_tool(self, tool_name: str, tool_args: dict) -> bool:
        """Test a specific tool and verify MCP format."""
        print(f"\n🧪 Testing tool: {tool_name}")
        print(f"   Arguments: {tool_args}")
        
        try:
            # Call the tool
            result = await self.client.call_tool(
                tool_name=tool_name,
                tool_input=tool_args,
                tool_call_id="integration_test"
            )
            
            print(f"⏱️  Execution time: {result.execution_time:.2f}s")
            
            if not result.success:
                print(f"❌ Tool call failed: {result.error}")
                return False
            
            print("✅ Tool call successful!")
            
            # Verify MCP format
            return self.verify_mcp_format(result.content, tool_name)
            
        except Exception as e:
            print(f"❌ Exception during tool call: {e}")
            return False
    
    def verify_mcp_format(self, content, tool_name: str) -> bool:
        """Verify that the content follows MCP format."""
        print(f"\n🔍 Verifying MCP format for {tool_name}...")

        try:
            # The content should be a list of content objects
            if not isinstance(content, list):
                print(f"❌ Content is not a list: {type(content)}")
                return False

            if len(content) == 0:
                print("❌ Content list is empty")
                return False

            # Check first content object - it might be an MCP type or dict
            first_content = content[0]

            # Handle MCP types (like TextContent)
            if hasattr(first_content, 'type') and hasattr(first_content, 'text'):
                # This is an MCP TextContent object
                content_type = first_content.type
                text_content = first_content.text
                print(f"✅ Found MCP TextContent object with type: {content_type}")
            elif isinstance(first_content, dict):
                # This is a plain dictionary
                if "type" not in first_content:
                    print("❌ Missing 'type' field in content")
                    return False

                content_type = first_content["type"]

                if "text" not in first_content:
                    print("❌ Missing 'text' field in content")
                    return False

                text_content = first_content["text"]
                print(f"✅ Found dictionary content with type: {content_type}")
            else:
                print(f"❌ First content item is neither MCP type nor dict: {type(first_content)}")
                return False

            # Verify content type is 'text'
            if content_type != "text":
                print(f"❌ Content type is not 'text': {content_type}")
                return False

            # Try to parse the text as JSON
            try:
                parsed_data = json.loads(text_content)
                print("✅ Text content is valid JSON")
                print(f"   Sample data: {str(parsed_data)[:100]}...")

                # Check if it has the expected MCP structure
                if isinstance(parsed_data, dict):
                    if "content" in parsed_data and "structuredContent" in parsed_data:
                        print("✅ Found nested MCP format with content and structuredContent")
                    else:
                        print("ℹ️  Simple JSON format (not nested MCP structure)")

            except json.JSONDecodeError:
                print("⚠️  Text content is not JSON (this may be okay)")
                print(f"   Text content: {text_content[:100]}...")

            print("✅ MCP format verification passed!")
            return True

        except Exception as e:
            print(f"❌ Error during format verification: {e}")
            return False

async def main():
    """Main test function."""
    print("🚀 Simple MCP Integration Test")
    print("=" * 50)
    
    async with SimpleMCPTester() as tester:
        # Connect to server
        if not await tester.connect():
            print("\n❌ Cannot connect to server. Make sure it's running on port 9000.")
            return False
        
        # List available tools
        tools = await tester.list_tools()
        if not tools:
            print("\n❌ No tools available on server.")
            return False
        
        # Test available tools
        test_results = []
        
        # Try to find and test quick_search or similar tools
        target_tools = ["quick_search", "sprite_l1_quick_search", "echostring", "simple_test_tool"]
        
        for target_tool in target_tools:
            tool_found = any(tool.get('name') == target_tool for tool in tools)
            if tool_found:
                print(f"\n🎯 Found target tool: {target_tool}")
                
                # Prepare arguments based on tool type
                if target_tool == "echostring":
                    args = {"message": "test message"}
                elif "search" in target_tool:
                    args = {"query": "agfunder"}
                else:
                    args = {"query": "test"}
                
                # Test the tool
                success = await tester.test_tool(target_tool, args)
                test_results.append((target_tool, success))
                break
        else:
            # No target tools found, test the first available tool
            first_tool = tools[0]
            tool_name = first_tool.get('name', 'unknown')
            print(f"\n🎯 Testing first available tool: {tool_name}")
            
            # Use generic arguments
            args = {"message": "test"} if "echo" in tool_name.lower() else {"query": "test"}
            success = await tester.test_tool(tool_name, args)
            test_results.append((tool_name, success))
        
        # Summary
        print("\n" + "=" * 50)
        print("📊 Test Results Summary:")
        
        all_passed = True
        for tool_name, success in test_results:
            status = "✅ PASS" if success else "❌ FAIL"
            print(f"   {tool_name}: {status}")
            if not success:
                all_passed = False
        
        if all_passed:
            print("\n🎉 All tests passed! MCP format compliance verified.")
        else:
            print("\n❌ Some tests failed. Check MCP format implementation.")
        
        return all_passed

if __name__ == "__main__":
    try:
        result = asyncio.run(main())
        sys.exit(0 if result else 1)
    except KeyboardInterrupt:
        print("\n⚠️  Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        sys.exit(1)

