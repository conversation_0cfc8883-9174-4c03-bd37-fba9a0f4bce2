#!/usr/bin/env python3
"""
Simple Test MCP Server for Integration Testing

This is a minimal MCP server for testing the MCP response format.
"""

from mcp.server.fastmcp import Fast<PERSON><PERSON>, Context
import asyncio
import argparse
import uvicorn
import sys

# Initialize FastMCP server
mcp = FastMCP("simple_test_server")

@mcp.tool()
async def echostring(message: str) -> str:
    """Echo a message back to test the server."""
    return f"Echo: {message}"

@mcp.tool()
async def quick_search(query: str) -> dict:
    """A simple test tool that simulates quick search with MCP format."""
    # Return data in the expected MCP format structure
    return {
        "content": [
            {
                "type": "text",
                "text": f'{{"query": "{query}", "results": ["Result 1 for {query}", "Result 2 for {query}"], "status": "success"}}'
            }
        ],
        "structuredContent": {
            "query": query,
            "results": [
                f"Result 1 for {query}",
                f"Result 2 for {query}"
            ],
            "status": "success",
            "metadata": {
                "tool": "quick_search",
                "timestamp": "2024-01-01T00:00:00Z"
            }
        }
    }

@mcp.tool()
async def server_info() -> dict:
    """Get server information."""
    return {
        "name": "Simple Test MCP Server",
        "version": "1.0.0",
        "tools": ["echostring", "quick_search", "server_info"],
        "status": "running"
    }

if __name__ == "__main__":
    # Initialize and run the server
    parser = argparse.ArgumentParser()
    parser.add_argument("--port", type=int, default=9000, help="HTTP port")
    args = parser.parse_args()

    print(f"Starting Simple Test MCP Server on port {args.port}...", file=sys.stderr)
    
    # Create the app using streamable_http_app for HTTP streaming
    app = mcp.streamable_http_app()

    uvicorn.run(app, host="0.0.0.0", port=args.port, log_level="info")
