#!/usr/bin/env python3
"""
Test script to verify MCP response format compliance.

This script tests the MCP response wrapper utility to ensure all sprite tools
return responses in the standard MCP format with both content and structuredContent.
"""

import json
import sys
import os

# Add parent directory to path for imports
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

from mcp_response_wrapper import (
    wrap_mcp_response,
    wrap_sprite_tool_response,
    create_mcp_tool_wrapper
)


def test_basic_response_wrapping():
    """Test basic response wrapping functionality."""
    print("=== Testing Basic Response Wrapping ===")
    
    # Test with dictionary response
    test_data = {
        "tool": "agsearch",
        "query": "vertical farming",
        "results": [
            {"name": "Company A", "category": "AgTech"},
            {"name": "Company B", "category": "FoodTech"}
        ],
        "stats": {"hits": 2}
    }
    
    wrapped = wrap_mcp_response(test_data)
    
    print("Input data:")
    print(json.dumps(test_data, indent=2))
    print("\nWrapped MCP response:")
    print(json.dumps(wrapped, indent=2))
    
    # Verify structure
    assert "content" in wrapped, "Missing 'content' field"
    assert isinstance(wrapped["content"], list), "'content' should be a list"
    assert len(wrapped["content"]) > 0, "'content' should not be empty"
    assert wrapped["content"][0]["type"] == "text", "First content item should be type 'text'"
    assert "structuredContent" in wrapped, "Missing 'structuredContent' field"
    
    print("✅ Basic response wrapping test passed!")
    return True


def test_error_response_wrapping():
    """Test error response wrapping."""
    print("\n=== Testing Error Response Wrapping ===")
    
    error_data = {
        "error": "Database connection failed",
        "tool": "agsearch"
    }
    
    wrapped = wrap_sprite_tool_response(error_data, "agsearch")
    
    print("Error data:")
    print(json.dumps(error_data, indent=2))
    print("\nWrapped error response:")
    print(json.dumps(wrapped, indent=2))
    
    # Verify error structure
    assert "content" in wrapped, "Missing 'content' field"
    assert "structuredContent" in wrapped, "Missing 'structuredContent' field"
    assert wrapped["structuredContent"]["success"] == False, "Error should have success=False"
    assert "error" in wrapped["structuredContent"], "Error should be in structuredContent"
    
    print("✅ Error response wrapping test passed!")
    return True


def test_string_response_wrapping():
    """Test string response wrapping."""
    print("\n=== Testing String Response Wrapping ===")
    
    string_data = "Simple string response from tool"
    wrapped = wrap_mcp_response(string_data)
    
    print("String data:")
    print(repr(string_data))
    print("\nWrapped string response:")
    print(json.dumps(wrapped, indent=2))
    
    # Verify string structure
    assert "content" in wrapped, "Missing 'content' field"
    assert wrapped["content"][0]["text"] == string_data, "Text content should match input"
    assert "structuredContent" in wrapped, "Missing 'structuredContent' field"
    
    print("✅ String response wrapping test passed!")
    return True


def test_json_string_response():
    """Test JSON string response wrapping."""
    print("\n=== Testing JSON String Response ===")
    
    json_string = '{"temperature": 22.5, "conditions": "Partly cloudy", "humidity": 65}'
    wrapped = wrap_mcp_response(json_string)
    
    print("JSON string:")
    print(json_string)
    print("\nWrapped JSON string response:")
    print(json.dumps(wrapped, indent=2))
    
    # Verify JSON string structure
    assert "content" in wrapped, "Missing 'content' field"
    assert "structuredContent" in wrapped, "Missing 'structuredContent' field"
    
    # The structured content should be the parsed JSON
    expected_structured = json.loads(json_string)
    assert wrapped["structuredContent"] == expected_structured, "Structured content should match parsed JSON"
    
    print("✅ JSON string response wrapping test passed!")
    return True


def test_tool_wrapper_function():
    """Test the tool wrapper function."""
    print("\n=== Testing Tool Wrapper Function ===")
    
    # Create a mock sprite function
    def mock_sprite_function(query: str, limit: int = 10):
        """Mock sprite function for testing."""
        return {
            "tool": "mock_tool",
            "query": query,
            "limit": limit,
            "results": [f"result_{i}" for i in range(limit)],
            "success": True
        }
    
    # Wrap the function
    wrapped_func = create_mcp_tool_wrapper(mock_sprite_function, "mock_tool")
    
    # Call the wrapped function
    result = wrapped_func("test query", limit=3)
    
    print("Mock function result:")
    print(json.dumps(result, indent=2))
    
    # Verify wrapped function result
    assert "content" in result, "Missing 'content' field"
    assert "structuredContent" in result, "Missing 'structuredContent' field"
    assert result["structuredContent"]["success"] == True, "Should have success=True"
    assert result["structuredContent"]["query"] == "test query", "Query should be preserved"
    
    print("✅ Tool wrapper function test passed!")
    return True


def test_standard_mcp_format_compliance():
    """Test compliance with the standard MCP format shown in the example."""
    print("\n=== Testing Standard MCP Format Compliance ===")
    
    # Test data similar to the weather example
    test_data = {
        "temperature": 22.5,
        "conditions": "Partly cloudy", 
        "humidity": 65
    }
    
    wrapped = wrap_mcp_response(test_data)
    
    print("Test data:")
    print(json.dumps(test_data, indent=2))
    print("\nMCP formatted response:")
    print(json.dumps(wrapped, indent=2))
    
    # Verify it matches the expected structure
    expected_structure = {
        "content": [
            {
                "type": "text",
                "text": json.dumps(test_data, indent=2, ensure_ascii=False)
            }
        ],
        "structuredContent": test_data
    }
    
    assert wrapped["content"][0]["type"] == "text", "Content type should be 'text'"
    assert wrapped["structuredContent"] == test_data, "Structured content should match input"
    
    # Verify the text content is valid JSON
    parsed_text = json.loads(wrapped["content"][0]["text"])
    assert parsed_text == test_data, "Text content should be parseable JSON matching input"
    
    print("✅ Standard MCP format compliance test passed!")
    return True


def run_all_tests():
    """Run all tests and report results."""
    print("Starting MCP Response Format Tests...\n")
    
    tests = [
        test_basic_response_wrapping,
        test_error_response_wrapping,
        test_string_response_wrapping,
        test_json_string_response,
        test_tool_wrapper_function,
        test_standard_mcp_format_compliance
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                failed += 1
                print(f"❌ {test.__name__} failed!")
        except Exception as e:
            failed += 1
            print(f"❌ {test.__name__} failed with exception: {e}")
    
    print(f"\n=== Test Results ===")
    print(f"✅ Passed: {passed}")
    print(f"❌ Failed: {failed}")
    print(f"Total: {passed + failed}")
    
    if failed == 0:
        print("\n🎉 All tests passed! MCP response format is working correctly.")
        return True
    else:
        print(f"\n⚠️  {failed} test(s) failed. Please check the implementation.")
        return False


if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
