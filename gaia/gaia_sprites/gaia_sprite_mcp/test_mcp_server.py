#!/usr/bin/env python3
"""
Simple Test MCP Server for Integration Testing

This is a minimal MCP server that includes sprite tools for testing
the MCP response format without external dependencies.
"""

from mcp.server.fastmcp import FastMCP, Context
import asyncio
import argparse
import uvicorn
import sys
import os

# Add the project root to Python path
sys.path.insert(0, '/home/<USER>/django-projects/agbase_admin')

# Initialize FastMCP server
mcp = FastMCP("test_mcp_server")

@mcp.tool()
async def echostring(message: str) -> str:
    """Echo a message back to test the server."""
    return f"Echo: {message}"

@mcp.tool()
async def simple_test_tool(query: str) -> dict:
    """A simple test tool that returns structured data."""
    return {
        "query": query,
        "result": f"Test result for: {query}",
        "status": "success",
        "data": {
            "items": [
                {"name": f"Item 1 for {query}", "value": 100},
                {"name": f"Item 2 for {query}", "value": 200}
            ]
        }
    }

# Try to import sprite tools if available
try:
    # Import sprite tools
    from gaia.gaia_sprites.gaia_sprite_mcp.sprite_mcp_server_base import SpriteMCPServerBase
    from gaia.gaia_sprites.gaia_sprite_mcp.mcp_response_wrapper import wrap_sprite_tool_response
    
    # Create a sprite server instance to get access to sprite tools
    sprite_server = SpriteMCPServerBase("test_sprite_server")
    
    # Check if sprites are available
    if sprite_server.sprites_available:
        print("✅ Sprites available - registering sprite tools", file=sys.stderr)
        
        # Import sprite level 1 tools
        from gaia.gaia_sprites import sprite_l1
        
        # Create wrapped versions of sprite tools
        @mcp.tool()
        async def sprite_l1_quick_search(query: str) -> dict:
            """Quick search using sprite level 1."""
            try:
                # Call the original sprite tool
                result = sprite_l1.quick_search(query)
                # Wrap in MCP format
                return wrap_sprite_tool_response(result)
            except Exception as e:
                return wrap_sprite_tool_response({
                    "error": str(e),
                    "query": query,
                    "status": "failed"
                })
        
        print("✅ Registered sprite_l1_quick_search tool", file=sys.stderr)
    else:
        print("⚠️ Sprites not available - using simple tools only", file=sys.stderr)
        
except ImportError as e:
    print(f"⚠️ Could not import sprite tools: {e}", file=sys.stderr)
    print("⚠️ Using simple tools only", file=sys.stderr)

@mcp.tool()
async def server_info() -> dict:
    """Get server information."""
    return {
        "name": "Test MCP Server",
        "version": "1.0.0",
        "tools": ["echostring", "simple_test_tool", "server_info"],
        "status": "running"
    }

if __name__ == "__main__":
    # Initialize and run the server
    parser = argparse.ArgumentParser()
    parser.add_argument("--port", type=int, default=9000, help="HTTP port")
    args = parser.parse_args()

    print(f"Starting Test MCP Server on port {args.port}...", file=sys.stderr)
    
    # Create the app using streamable_http_app for HTTP streaming
    app = mcp.streamable_http_app()

    uvicorn.run(app, host="0.0.0.0", port=args.port, log_level="info")
