#!/usr/bin/env python3
"""
MCP Response Wrapper Utility

This module provides utilities to wrap sprite tool responses in the standard MCP format
that includes both text content and structured content.

Standard MCP Tool Response Format:
{
  "content": [
    {
      "type": "text", 
      "text": "{\"key\": \"value\"}"
    }
  ],
  "structuredContent": {
    "key": "value"
  }
}
"""

import json
import logging
from typing import Any, Dict, List, Union

logger = logging.getLogger(__name__)


def wrap_mcp_response(data: Any) -> Dict[str, Any]:
    """
    Wrap any data in the standard MCP tool response format.
    
    Args:
        data: The data to wrap (dict, list, string, etc.)
        
    Returns:
        Dict containing 'content' array and optional 'structuredContent'
    """
    try:
        # Convert data to JSON string for the text content
        if isinstance(data, (dict, list)):
            json_text = json.dumps(data, indent=2, ensure_ascii=False)
            structured_content = data
        elif isinstance(data, str):
            # Try to parse as JSON first
            try:
                parsed_data = json.loads(data)
                json_text = json.dumps(parsed_data, indent=2, ensure_ascii=False)
                structured_content = parsed_data
            except json.JSONDecodeError:
                # If not JSON, treat as plain text
                json_text = data
                structured_content = {"text": data}
        else:
            # For other types, convert to string
            json_text = str(data)
            structured_content = {"value": data}
            
        # Create the standard MCP response format
        response = {
            "content": [
                {
                    "type": "text",
                    "text": json_text
                }
            ]
        }
        
        # Add structured content if we have meaningful structured data
        if structured_content:
            response["structuredContent"] = structured_content
            
        return response
        
    except Exception as e:
        logger.error(f"Error wrapping MCP response: {e}")
        # Return a safe fallback response
        return {
            "content": [
                {
                    "type": "text", 
                    "text": f"Error formatting response: {str(e)}"
                }
            ],
            "structuredContent": {
                "error": str(e),
                "original_data": str(data)
            }
        }


def wrap_sprite_tool_response(tool_result: Any, tool_name: str = None) -> Dict[str, Any]:
    """
    Specifically wrap sprite tool responses in MCP format.
    
    This function handles the common sprite tool response patterns and ensures
    they conform to the MCP standard.
    
    Args:
        tool_result: The result from a sprite tool function
        tool_name: Optional tool name for enhanced error reporting
        
    Returns:
        Dict containing properly formatted MCP response
    """
    try:
        # Handle error responses
        if isinstance(tool_result, dict) and "error" in tool_result:
            error_response = {
                "content": [
                    {
                        "type": "text",
                        "text": json.dumps({
                            "error": tool_result["error"],
                            "tool": tool_name or tool_result.get("tool", "unknown")
                        }, indent=2)
                    }
                ],
                "structuredContent": {
                    "error": tool_result["error"],
                    "tool": tool_name or tool_result.get("tool", "unknown"),
                    "success": False
                }
            }
            return error_response
            
        # Handle successful responses
        if isinstance(tool_result, dict):
            # Add success indicator if not present
            if "success" not in tool_result:
                tool_result = dict(tool_result)  # Make a copy
                tool_result["success"] = True
                
        return wrap_mcp_response(tool_result)
        
    except Exception as e:
        logger.error(f"Error wrapping sprite tool response for {tool_name}: {e}")
        return {
            "content": [
                {
                    "type": "text",
                    "text": json.dumps({
                        "error": f"Failed to format tool response: {str(e)}",
                        "tool": tool_name or "unknown"
                    }, indent=2)
                }
            ],
            "structuredContent": {
                "error": f"Failed to format tool response: {str(e)}",
                "tool": tool_name or "unknown",
                "success": False
            }
        }


def create_mcp_tool_wrapper(original_func, tool_name: str = None):
    """
    Create a wrapper function that automatically formats responses in MCP format.
    
    Args:
        original_func: The original sprite tool function
        tool_name: Optional tool name for error reporting
        
    Returns:
        Wrapped function that returns MCP-formatted responses
    """
    def wrapped_func(*args, **kwargs):
        try:
            # Call the original function
            result = original_func(*args, **kwargs)
            
            # Wrap the result in MCP format
            return wrap_sprite_tool_response(result, tool_name or original_func.__name__)
            
        except Exception as e:
            logger.error(f"Error in wrapped tool {tool_name or original_func.__name__}: {e}")
            return {
                "content": [
                    {
                        "type": "text",
                        "text": json.dumps({
                            "error": str(e),
                            "tool": tool_name or original_func.__name__
                        }, indent=2)
                    }
                ],
                "structuredContent": {
                    "error": str(e),
                    "tool": tool_name or original_func.__name__,
                    "success": False
                }
            }
    
    # Preserve function metadata
    wrapped_func.__name__ = original_func.__name__
    wrapped_func.__doc__ = original_func.__doc__
    wrapped_func.__annotations__ = getattr(original_func, '__annotations__', {})
    
    return wrapped_func


# Example usage and testing
if __name__ == "__main__":
    # Test with various data types
    test_data = [
        {"tool": "agsearch", "results": [{"name": "Company A"}], "success": True},
        {"error": "Database connection failed", "tool": "agsearch"},
        "Simple string response",
        '{"json": "string"}',
        ["list", "of", "items"],
        42
    ]
    
    print("Testing MCP response wrapper:")
    for i, data in enumerate(test_data):
        print(f"\n--- Test {i+1} ---")
        print(f"Input: {data}")
        wrapped = wrap_mcp_response(data)
        print(f"Output: {json.dumps(wrapped, indent=2)}")
