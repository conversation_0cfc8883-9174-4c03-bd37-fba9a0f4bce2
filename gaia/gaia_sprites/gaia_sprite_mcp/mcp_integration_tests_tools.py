#!/usr/bin/env python3
"""
MCP Integration Tests for Tools

This file contains integration tests that verify MCP tools return responses
in the standard MCP format when called through the MCP server running on port 9000.

The tests assume the MCP server is already running and accessible.
"""

import json
import requests
import sys
from typing import Dict, Any, Optional


class MCPIntegrationTester:
    """Integration tester for MCP server tools."""
    
    def __init__(self, server_url: str = "http://localhost:9000/mcp"):
        """
        Initialize the MCP integration tester.
        
        Args:
            server_url: Base URL of the MCP server (default: http://localhost:9000)
        """
        self.server_url = server_url
        self.session = requests.Session()
        self.session.headers.update({
            'Content-Type': 'application/json',
            'Accept': 'application/json, text/event-stream',
            'User-Agent': 'MCP-Integration-Tester/1.0'
        })
    
    def check_server_health(self) -> bool:
        """Check if the MCP server is running and healthy."""
        try:
            # Try to initialize an MCP session to check if server is responding
            init_request = {
                "jsonrpc": "2.0",
                "id": 1,
                "method": "initialize",
                "params": {
                    "protocolVersion": "2024-11-05",
                    "capabilities": {},
                    "clientInfo": {
                        "name": "integration-test",
                        "version": "1.0.0"
                    }
                }
            }

            response = self.session.post(self.server_url, json=init_request, timeout=10)
            if response.status_code == 200:
                result = response.json()
                if "result" in result:
                    print(f"✅ Server is healthy: MCP protocol responding")
                    return True
                else:
                    print(f"❌ Server responded but no result: {result}")
                    return False
            else:
                print(f"❌ Server health check failed: {response.status_code}")
                return False
        except requests.exceptions.RequestException as e:
            print(f"❌ Cannot connect to server: {e}")
            return False
    
    def list_available_tools(self) -> Optional[Dict[str, Any]]:
        """List all available tools on the MCP server."""
        try:
            # Use MCP protocol to list tools
            list_tools_request = {
                "jsonrpc": "2.0",
                "id": 2,
                "method": "tools/list",
                "params": {}
            }

            response = self.session.post(self.server_url, json=list_tools_request, timeout=10)
            if response.status_code == 200:
                result = response.json()
                if "result" in result and "tools" in result["result"]:
                    tools = result["result"]["tools"]
                    print(f"📋 Available tools: {len(tools)}")
                    return {"tools": tools}
                else:
                    print(f"❌ Unexpected tools response: {result}")
                    return None
            else:
                print(f"❌ Failed to list tools: {response.status_code}")
                return None
        except requests.exceptions.RequestException as e:
            print(f"❌ Error listing tools: {e}")
            return None
    
    def call_tool(self, tool_name: str, arguments: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        Call a specific tool with given arguments.

        Args:
            tool_name: Name of the tool to call
            arguments: Arguments to pass to the tool

        Returns:
            Tool response or None if failed
        """
        try:
            # Use MCP protocol to call tool
            call_tool_request = {
                "jsonrpc": "2.0",
                "id": 3,
                "method": "tools/call",
                "params": {
                    "name": tool_name,
                    "arguments": arguments
                }
            }

            response = self.session.post(self.server_url, json=call_tool_request, timeout=30)

            if response.status_code == 200:
                result = response.json()
                if "result" in result:
                    # Wrap the result to match expected format
                    return {
                        "success": True,
                        "result": result["result"]
                    }
                elif "error" in result:
                    return {
                        "success": False,
                        "error": result["error"]
                    }
                else:
                    return {
                        "success": False,
                        "error": f"Unexpected response format: {result}"
                    }
            else:
                print(f"❌ Tool call failed: {response.status_code} - {response.text}")
                return None

        except requests.exceptions.RequestException as e:
            print(f"❌ Error calling tool {tool_name}: {e}")
            return None
    
    def verify_mcp_format(self, response: Dict[str, Any], tool_name: str) -> bool:
        """
        Verify that a response adheres to the standard MCP format.
        
        Args:
            response: The response to verify
            tool_name: Name of the tool that generated the response
            
        Returns:
            True if response is valid MCP format, False otherwise
        """
        print(f"\n🔍 Verifying MCP format for {tool_name} response...")
        
        # Check if response has the expected top-level structure
        if not isinstance(response, dict):
            print("❌ Response is not a dictionary")
            return False
        
        # Check for success field
        if not response.get("success", False):
            print(f"❌ Tool call was not successful: {response.get('error', 'Unknown error')}")
            return False
        
        # Get the actual result
        result = response.get("result")
        if result is None:
            print("❌ No 'result' field in response")
            return False
        
        # Verify MCP format structure
        if not isinstance(result, dict):
            print("❌ Result is not a dictionary")
            return False
        
        # Check for required MCP fields
        if "content" not in result:
            print("❌ Missing 'content' field in MCP response")
            return False
        
        if "structuredContent" not in result:
            print("❌ Missing 'structuredContent' field in MCP response")
            return False
        
        # Verify content structure
        content = result["content"]
        if not isinstance(content, list):
            print("❌ 'content' field is not a list")
            return False
        
        if len(content) == 0:
            print("❌ 'content' list is empty")
            return False
        
        # Check first content item
        first_content = content[0]
        if not isinstance(first_content, dict):
            print("❌ First content item is not a dictionary")
            return False
        
        if first_content.get("type") != "text":
            print(f"❌ First content item type is '{first_content.get('type')}', expected 'text'")
            return False
        
        if "text" not in first_content:
            print("❌ First content item missing 'text' field")
            return False
        
        # Verify text content is valid JSON
        try:
            text_content = first_content["text"]
            json.loads(text_content)  # Just validate, don't store
            print("✅ Text content is valid JSON")
        except json.JSONDecodeError as e:
            print(f"❌ Text content is not valid JSON: {e}")
            return False
        
        # Verify structuredContent exists and is not empty
        structured_content = result["structuredContent"]
        if not isinstance(structured_content, dict):
            print("❌ 'structuredContent' is not a dictionary")
            return False
        
        if len(structured_content) == 0:
            print("❌ 'structuredContent' is empty")
            return False
        
        print("✅ Response adheres to standard MCP format!")
        print(f"   - Has 'content' array with {len(content)} item(s)")
        print(f"   - Has 'structuredContent' with {len(structured_content)} field(s)")
        print(f"   - Text content is valid JSON")
        
        return True


def test_quick_search_tool():
    """
    Test the quick_search tool with 'agfunder' input and verify MCP format compliance.
    
    This is the main test function that:
    1. Connects to the MCP server on port 9000
    2. Calls the quick_search tool with 'agfunder' as input
    3. Verifies the response adheres to the standard MCP format
    """
    print("🚀 Starting MCP Integration Test for quick_search tool")
    print("="*60)
    
    # Initialize tester
    tester = MCPIntegrationTester("http://localhost:9000")
    
    # Check server health
    print("\n1️⃣ Checking server health...")
    if not tester.check_server_health():
        print("❌ Server is not available. Please ensure MCP server is running on port 9000.")
        return False
    
    # List available tools
    print("\n2️⃣ Listing available tools...")
    tools_data = tester.list_available_tools()
    if tools_data is None:
        print("❌ Could not retrieve tools list")
        return False
    
    # Check if quick_search tool is available
    available_tools = [tool.get("name") for tool in tools_data.get("tools", [])]

    # Look for quick_search or similar tools
    target_tools = ["quick_search", "sprite_l1_quick_search", "echostring"]
    found_tool = None

    for tool in target_tools:
        if tool in available_tools:
            found_tool = tool
            break

    if not found_tool:
        print(f"❌ No suitable test tool found. Available tools: {available_tools}")
        print("   Looking for: quick_search, sprite_l1_quick_search, or echostring")
        return False

    print(f"✅ Found test tool: {found_tool}")
    
    # Call the found tool
    print(f"\n3️⃣ Calling {found_tool} tool with input 'agfunder'...")

    # Prepare arguments based on tool type
    if found_tool == "echostring":
        arguments = {"message": "agfunder"}
    else:
        arguments = {"query": "agfunder"}

    response = tester.call_tool(found_tool, arguments)
    
    if response is None:
        print("❌ Tool call failed")
        return False
    
    print("✅ Tool call completed successfully")
    
    # Print response summary
    print(f"\n📊 Response Summary:")
    print(f"   - Response type: {type(response)}")
    print(f"   - Response keys: {list(response.keys()) if isinstance(response, dict) else 'N/A'}")
    
    # Verify MCP format
    print("\n4️⃣ Verifying MCP format compliance...")
    is_valid = tester.verify_mcp_format(response, found_tool)
    
    if is_valid:
        print(f"\n🎉 SUCCESS: {found_tool} tool returns valid MCP format!")
        
        # Print sample of the response for verification
        result = response.get("result", {})
        structured_content = result.get("structuredContent", {})
        
        print(f"\n📋 Sample Response Data:")
        print(f"   - Tool: {structured_content.get('tool', 'N/A')}")
        print(f"   - Query: {structured_content.get('query', 'N/A')}")
        
        if "results" in structured_content:
            results = structured_content["results"]
            if isinstance(results, list) and len(results) > 0:
                print(f"   - Results count: {len(results)}")
                print(f"   - First result keys: {list(results[0].keys()) if isinstance(results[0], dict) else 'N/A'}")
        
        return True
    else:
        print("\n❌ FAILURE: Response does not comply with MCP format")
        print(f"\nFull response for debugging:")
        print(json.dumps(response, indent=2))
        return False


def main():
    """Run the integration test."""
    print("MCP Integration Tests - Tools")
    print("Testing MCP server on port 9000")
    print("="*60)
    
    try:
        success = test_quick_search_tool()
        
        print("\n" + "="*60)
        if success:
            print("✅ ALL TESTS PASSED!")
            print("✅ MCP format compliance verified!")
            sys.exit(0)
        else:
            print("❌ TESTS FAILED!")
            print("❌ MCP format compliance issues detected!")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n\n⚠️ Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n💥 Unexpected error during testing: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
