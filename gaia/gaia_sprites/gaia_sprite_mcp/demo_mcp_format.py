#!/usr/bin/env python3
"""
Demonstration of MCP Response Format for Sprite Tools

This script demonstrates how sprite tools now return responses in the standard MCP format
with both text content and structured content.
"""

import json
from mcp_response_wrapper import wrap_sprite_tool_response, create_mcp_tool_wrapper


def demo_old_vs_new_format():
    """Demonstrate the difference between old and new response formats."""
    print("=== OLD vs NEW Response Format Comparison ===\n")
    
    # Simulate old sprite tool response
    old_response = {
        "tool": "agsearch",
        "query": "vertical farming",
        "results": [
            {"name": "AeroFarms", "category": "Vertical Farming", "funding": "$238M"},
            {"name": "Plenty", "category": "Indoor Farming", "funding": "$541M"}
        ],
        "stats": {"hits": 2, "total_companies": 1500}
    }
    
    print("OLD FORMAT (Raw Dictionary):")
    print(json.dumps(old_response, indent=2))
    
    print("\n" + "="*60 + "\n")
    
    # Convert to new MCP format
    new_response = wrap_sprite_tool_response(old_response, "agsearch")
    
    print("NEW FORMAT (Standard MCP):")
    print(json.dumps(new_response, indent=2))
    
    print("\n" + "="*60 + "\n")
    
    print("KEY DIFFERENCES:")
    print("1. ✅ Has 'content' array with text representation")
    print("2. ✅ Has 'structuredContent' with the actual data")
    print("3. ✅ Follows JSON-RPC MCP standard")
    print("4. ✅ Compatible with all MCP clients")
    print("5. ✅ Supports both human-readable and machine-readable formats")


def demo_error_handling():
    """Demonstrate error response formatting."""
    print("\n\n=== Error Response Formatting ===\n")
    
    # Simulate error response
    error_response = {
        "error": "Database connection timeout after 30 seconds",
        "tool": "agsearch"
    }
    
    print("ERROR INPUT:")
    print(json.dumps(error_response, indent=2))
    
    print("\nMCP FORMATTED ERROR:")
    mcp_error = wrap_sprite_tool_response(error_response, "agsearch")
    print(json.dumps(mcp_error, indent=2))


def demo_tool_wrapper():
    """Demonstrate the tool wrapper functionality."""
    print("\n\n=== Tool Wrapper Demonstration ===\n")
    
    # Create a mock sprite function
    def mock_market_research(query: str, region: str = "global"):
        """Mock market research sprite function."""
        return {
            "tool": "market_research",
            "query": query,
            "region": region,
            "market_size": "$45.2B",
            "cagr": "12.3%",
            "key_players": ["Company A", "Company B", "Company C"],
            "trends": [
                "Increased automation",
                "Sustainable practices",
                "AI integration"
            ]
        }
    
    print("ORIGINAL FUNCTION OUTPUT:")
    original_result = mock_market_research("AgTech market analysis", "North America")
    print(json.dumps(original_result, indent=2))
    
    print("\n" + "-"*50 + "\n")
    
    # Wrap the function
    wrapped_function = create_mcp_tool_wrapper(mock_market_research, "market_research")
    
    print("WRAPPED FUNCTION OUTPUT (MCP Format):")
    wrapped_result = wrapped_function("AgTech market analysis", "North America")
    print(json.dumps(wrapped_result, indent=2))


def demo_implementation_guide():
    """Show how to implement the new format in sprite servers."""
    print("\n\n=== Implementation Guide ===\n")
    
    implementation_code = '''
# OLD WAY (in sprite_mcp_server_base.py):
@self.mcp.tool()
def agsearch(query: str, limit: int = 100) -> dict:
    """Search AgFunder company database"""
    result = sprites['agsearch'](query, per_page=limit)
    return {"tool": "agsearch", "results": result["results"]}

# NEW WAY (with MCP wrapper):
def agsearch_impl(query: str, limit: int = 100) -> dict:
    """Implementation of AgFunder company database search"""
    result = sprites['agsearch'](query, per_page=limit)
    return {"tool": "agsearch", "results": result["results"]}

# Register using the helper method (automatically wraps in MCP format)
self._register_mcp_tool(
    agsearch_impl, 
    "agsearch", 
    "Search AgFunder company database"
)
'''
    
    print("IMPLEMENTATION CHANGES:")
    print(implementation_code)
    
    print("\nBENEFITS:")
    print("1. 🔄 Automatic response formatting")
    print("2. 🛡️  Error handling built-in")
    print("3. 📊 Both text and structured data")
    print("4. 🔧 Easy to migrate existing tools")
    print("5. ✅ MCP standard compliance")


def main():
    """Run all demonstrations."""
    print("🚀 MCP Response Format Demonstration\n")
    
    demo_old_vs_new_format()
    demo_error_handling()
    demo_tool_wrapper()
    demo_implementation_guide()
    
    print("\n" + "="*60)
    print("✨ All sprite tools now return standard MCP format!")
    print("✨ Compatible with all MCP clients and frameworks!")
    print("="*60)


if __name__ == "__main__":
    main()
