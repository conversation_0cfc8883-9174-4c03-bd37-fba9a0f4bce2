#!/usr/bin/env python3
"""
Debug MCP Response Format

Simple script to call the quick_search tool and print the full response
to see exactly what format is being returned.
"""

import asyncio
import json
import sys
import os

# Add the project root to Python path
sys.path.insert(0, '/home/<USER>/django-projects/agbase_admin')

from gaia.gaia_ceto.proto_mcp_http.mcp_http_clientlib import MC<PERSON>lient<PERSON>ib

async def debug_quick_search():
    """Debug the quick_search tool response format."""
    print("🔍 Debug MCP Response Format")
    print("=" * 50)
    
    client = MCPClientLib()
    
    try:
        # Connect to server
        print("🔌 Connecting to MCP server...")
        success = await client.connect_to_server("http://localhost:9000/mcp")
        
        if not success:
            print("❌ Failed to connect to server")
            return
        
        print("✅ Connected successfully!")
        
        # List tools
        tools = client.available_tools
        print(f"\n📋 Available tools: {len(tools)}")
        for tool in tools:
            print(f"  • {tool.get('name', 'unknown')}")
        
        # Call quick_search
        print(f"\n🧪 Calling quick_search with 'agfunder'...")
        result = await client.call_tool(
            tool_name="quick_search",
            tool_input={"query": "agfunder"},
            tool_call_id="debug_test"
        )
        
        print(f"⏱️  Execution time: {result.execution_time:.2f}s")
        print(f"✅ Success: {result.success}")
        
        if result.success:
            print(f"\n📤 Raw Result Content Type: {type(result.content)}")
            print(f"📤 Raw Result Content Length: {len(result.content) if hasattr(result.content, '__len__') else 'N/A'}")
            
            if isinstance(result.content, list) and len(result.content) > 0:
                first_item = result.content[0]
                print(f"📤 First Content Item Type: {type(first_item)}")
                
                if hasattr(first_item, 'type') and hasattr(first_item, 'text'):
                    print(f"📤 Content Type: {first_item.type}")
                    print(f"📤 Text Length: {len(first_item.text)}")
                    
                    # Try to parse the text as JSON
                    try:
                        parsed_json = json.loads(first_item.text)
                        print(f"\n📋 Parsed JSON Structure:")
                        print(f"   Type: {type(parsed_json)}")
                        
                        if isinstance(parsed_json, dict):
                            print(f"   Keys: {list(parsed_json.keys())}")
                            
                            # Check if it has the expected MCP structure
                            if "content" in parsed_json and "structuredContent" in parsed_json:
                                print("🎉 SUCCESS: Found nested MCP format!")
                                print(f"   content type: {type(parsed_json['content'])}")
                                print(f"   structuredContent type: {type(parsed_json['structuredContent'])}")
                                
                                if isinstance(parsed_json['content'], list) and len(parsed_json['content']) > 0:
                                    print(f"   content[0]: {parsed_json['content'][0]}")
                                
                                if isinstance(parsed_json['structuredContent'], dict):
                                    print(f"   structuredContent keys: {list(parsed_json['structuredContent'].keys())}")
                            else:
                                print("❌ ISSUE: Missing 'content' and 'structuredContent' keys")
                                print(f"   Available keys: {list(parsed_json.keys())}")
                        
                        # Pretty print first 500 chars
                        json_str = json.dumps(parsed_json, indent=2)
                        print(f"\n📄 JSON Content (first 500 chars):")
                        print(json_str[:500] + ("..." if len(json_str) > 500 else ""))
                        
                    except json.JSONDecodeError as e:
                        print(f"❌ Failed to parse as JSON: {e}")
                        print(f"📄 Raw text (first 200 chars): {first_item.text[:200]}...")
                
            else:
                print("❌ Unexpected content structure")
                print(f"📄 Raw content: {result.content}")
        else:
            print(f"❌ Tool call failed: {result.error}")
    
    except Exception as e:
        print(f"💥 Exception: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        try:
            await client.cleanup()
        except Exception as e:
            print(f"⚠️  Cleanup warning: {e}")

if __name__ == "__main__":
    try:
        asyncio.run(debug_quick_search())
    except KeyboardInterrupt:
        print("\n⚠️  Interrupted by user")
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
