#!/usr/bin/env python3
"""
Sprite MCP Server Base Class

Base class for Gaia Sprite MCP servers with HTTP streaming protocol support.
Provides common functionality for different server levels.
"""

import sys
import os
import json
import argparse
import asyncio
from typing import Optional, Dict, Any
from abc import ABC, abstractmethod

# Add parent directory to path for sprite imports
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

# Import FastMCP
from mcp.server.fastmcp import FastMCP

# Import MCP response wrapper
try:
    from .mcp_response_wrapper import wrap_sprite_tool_response, create_mcp_tool_wrapper
except ImportError:
    # Fallback for direct execution
    from mcp_response_wrapper import wrap_sprite_tool_response, create_mcp_tool_wrapper

class SpriteMCPServerBase(ABC):
    """Base class for Sprite MCP servers"""
    
    def __init__(self, server_name: str = "sprite-mcp-server"):
        self.server_name = server_name
        self.mcp = FastMCP(server_name)
        self.sprites_available = False
        self.tool_registry = {}
        
        # Initialize sprites and tools
        self._init_sprites()
        self._register_tools()
    
    @abstractmethod
    def _init_sprites(self):
        """Initialize sprite imports - implemented by subclasses"""
        pass
    
    @abstractmethod
    def _register_tools(self):
        """Register MCP tools - implemented by subclasses"""
        pass
    
    def _safe_import_sprites(self):
        """Safely import all basic sprites"""
        try:
            from spr_agf_agsearch import quick_search
            from spr_tamarind import market_research
            from spr_ext_search import web_search, news_search
            from spr_agf_omnisearch import omni_search
            from spr_ext_wiki import wiki_search
            from spr_agf_multimodel import multimodel_query
            from spr_agf_frames import frame_search, frame_list
            from spr_agf_investors import investor_search, investor_stats
            from spr_llm_smart import (
                llm_completion_text, llm_completion_json, llm_analyze_code,
                llm_generate_code, llm_explain_concept, llm_debug_error
            )
            from spr_ext_deep_research import (
                deep_research_simple, deep_research_robust, deep_research_batch,
                deep_research_load, deep_research_list
            )
            from spr_ext_babyfetch_simple import (
                babyfetch_simple, babyfetch_text_only, babyfetch_multiple_simple
            )
            from spr_elf_mem import (
                elf_mem_store, elf_mem_get, elf_mem_delete,
                elf_mem_list_keys, elf_mem_bank_status, elf_mem_list_banks
            )
            from spr_simple_chart import (
                simple_chart_svg, simple_chart_png, quick_chart
            )
            from spr_agf_agsearch_models import (
                list_contexts, search_contexts, fetch_context, create_context,
                list_ratings_for_context, create_rating, delete_rating
            )
            from spr_agf_debugger import (
                debug_database_connection, debug_django_setup,
                debug_sprite_function, debug_mcp_context
            )
            from spr_agf_sql_agent import (
                sql_query, list_databases, quick_sql
            )
            from spr_ext_babyfetch import (
                babyfetch_url
            )

            return {
                'quick_search': quick_search,
                'market_research': market_research,
                'web_search': web_search,
                'news_search': news_search,
                'omni_search': omni_search,
                'wiki_search': wiki_search,
                'multimodel_query': multimodel_query,
                'frame_search': frame_search,
                'frame_list': frame_list,
                'investor_search': investor_search,
                'investor_stats': investor_stats,
                'llm_completion_text': llm_completion_text,
                'llm_completion_json': llm_completion_json,
                'llm_analyze_code': llm_analyze_code,
                'llm_generate_code': llm_generate_code,
                'llm_explain_concept': llm_explain_concept,
                'llm_debug_error': llm_debug_error,
                'deep_research_simple': deep_research_simple,
                'deep_research_robust': deep_research_robust,
                'deep_research_batch': deep_research_batch,
                'deep_research_load': deep_research_load,
                'deep_research_list': deep_research_list,
                'babyfetch_simple': babyfetch_simple,
                'babyfetch_text_only': babyfetch_text_only,
                'babyfetch_multiple_simple': babyfetch_multiple_simple,
                'elf_mem_store': elf_mem_store,
                'elf_mem_get': elf_mem_get,
                'elf_mem_delete': elf_mem_delete,
                'elf_mem_list_keys': elf_mem_list_keys,
                'elf_mem_bank_status': elf_mem_bank_status,
                'elf_mem_list_banks': elf_mem_list_banks,
                'simple_chart_svg': simple_chart_svg,
                'simple_chart_png': simple_chart_png,
                'quick_chart': quick_chart,
                'list_contexts': list_contexts,
                'search_contexts': search_contexts,
                'fetch_context': fetch_context,
                'create_context': create_context,
                'list_ratings_for_context': list_ratings_for_context,
                'create_rating': create_rating,
                'delete_rating': delete_rating,
                'debug_database_connection': debug_database_connection,
                'debug_django_setup': debug_django_setup,
                'debug_sprite_function': debug_sprite_function,
                'debug_mcp_context': debug_mcp_context,
                'sql_query': sql_query,
                'list_databases': list_databases,
                'quick_sql': quick_sql,
                'babyfetch_url': babyfetch_url
            }
        except ImportError as e:
            print(f"Warning: Could not import sprites: {e}", file=sys.stderr)
            return {}

    def debug_tool_wrapper(self, tool_func):
        """Wrapper to debug MCP tool serialization"""
        def wrapper(*args, **kwargs):
            print(f"[MCP DEBUG] Calling {tool_func.__name__} with args: {args}, kwargs: {kwargs}")

            result = tool_func(*args, **kwargs)

            print(f"[MCP DEBUG] {tool_func.__name__} result:")
            print(f"  Type before MCP serialization: {type(result)}")
            print(f"  Content preview: {str(result)[:300]}...")

            # Simulate what MCP does - serialize and deserialize
            import json
            try:
                serialized = json.dumps(result)
                deserialized = json.loads(serialized)
                print(f"  Type after MCP round-trip: {type(deserialized)}")
                print(f"  Serialization successful: {result == deserialized}")
            except Exception as e:
                print(f"  Serialization error: {e}")

            return result
        return wrapper

    def _create_mcp_wrapped_tool(self, sprite_func, tool_name: str, description: str, **kwargs):
        """
        Create an MCP tool that automatically wraps responses in standard MCP format.

        Args:
            sprite_func: The original sprite function to wrap
            tool_name: Name of the tool for MCP registration
            description: Description of the tool
            **kwargs: Additional arguments for the tool function

        Returns:
            The wrapped tool function that returns MCP-formatted responses
        """
        def mcp_tool_func(*args, **tool_kwargs):
            """MCP tool wrapper that formats responses properly"""
            try:
                # Call the original sprite function
                result = sprite_func(*args, **tool_kwargs)

                # Wrap the result in standard MCP format
                return wrap_sprite_tool_response(result, tool_name)

            except Exception as e:
                # Return error in MCP format
                return wrap_sprite_tool_response(
                    {"error": str(e), "tool": tool_name},
                    tool_name
                )

        # Preserve function metadata
        mcp_tool_func.__name__ = tool_name
        mcp_tool_func.__doc__ = description
        mcp_tool_func.__annotations__ = getattr(sprite_func, '__annotations__', {})

        return mcp_tool_func

    def _register_mcp_tool(self, sprite_func, tool_name: str, description: str, **kwargs):
        """
        Helper method to register a sprite function as an MCP tool with proper response formatting.

        Args:
            sprite_func: The sprite function to wrap and register
            tool_name: Name for the MCP tool
            description: Description for the MCP tool
            **kwargs: Additional arguments (currently unused but for future extensibility)
        """
        # Create the MCP-wrapped tool
        mcp_tool = self._create_mcp_wrapped_tool(sprite_func, tool_name, description)

        # Register with FastMCP
        self.mcp.add_tool(mcp_tool)

        # Add to tool registry
        self.tool_registry[tool_name] = {
            "name": tool_name,
            "description": description
        }

    def _register_basic_tools(self, sprites):
        """Register basic sprite tools"""
        if not sprites:
            return
        
        # Example of using the new MCP wrapper for agsearch
        def agsearch_impl(query: str, limit: int = 100) -> dict:
            """Implementation of AgFunder company database search"""
            try:
                result = sprites['agsearch'](query, per_page=limit)
                if result["success"]:
                    return {"tool": "agsearch", "query": query, "limit": limit,
                            "results": result["results"], "stats": {"hits": result["total_hits"]}}
                else:
                    return {"error": result["error"], "tool": "agsearch"}
            except Exception as e:
                return {"error": str(e), "tool": "agsearch"}

        # Register using the helper method (automatically wraps in MCP format)
        self._register_mcp_tool(
            agsearch_impl,
            "agsearch",
            "Search AgFunder company database"
        )
        
        @self.mcp.tool()
        def market_research_tool(query: str = "AgTech") -> dict:
            """Get market research data (TAM/CAGR)"""
            import logging
            logger = logging.getLogger(__name__)

            logger.info("=" * 80)
            logger.info("🔧 SPRITE TOOL CALLED: market_research_tool")
            logger.info(f"   Query: {query}")
            logger.info("   This will trigger: Web search → LLM evaluation of each result → TAM/CAGR extraction")
            logger.info("=" * 80)

            try:
                results, stats = sprites['market_research'](query)
                logger.info("=" * 80)
                logger.info("✅ SPRITE TOOL COMPLETED: market_research_tool")
                logger.info("=" * 80)
                return {"tool": "market_research", "query": query, "results": results, "stats": stats}
            except Exception as e:
                logger.error("=" * 80)
                logger.error(f"❌ SPRITE TOOL FAILED: market_research_tool - {e}")
                logger.error("=" * 80)
                return {"error": str(e), "tool": "market_research"}
        
        @self.mcp.tool()
        def web_search_tool(query: str, sources: list = None, num_results: int = 10) -> dict:
            """Multi-source web search (web, news, scholar, patents)"""
            import logging
            logger = logging.getLogger(__name__)

            logger.info("=" * 80)
            logger.info("🔧 SPRITE TOOL CALLED: web_search_tool")
            logger.info(f"   Query: {query}")
            logger.info(f"   Sources: {sources}")
            logger.info(f"   Num results: {num_results}")
            logger.info("=" * 80)

            try:
                results, stats = sprites['web_search'](query=query, sources=sources, num_results=num_results)
                logger.info("=" * 80)
                logger.info("✅ SPRITE TOOL COMPLETED: web_search_tool")
                logger.info(f"   Found {len(results) if results else 0} results")
                logger.info("=" * 80)
                return {"tool": "web_search", "query": query, "sources": sources, "num_results": num_results, "results": results, "stats": stats}
            except Exception as e:
                logger.error("=" * 80)
                logger.error(f"❌ SPRITE TOOL FAILED: web_search_tool - {e}")
                logger.error("=" * 80)
                return {"error": str(e), "tool": "web_search"}
        
        @self.mcp.tool()
        def news_search_tool(query: str, num_results: int = 10) -> dict:
            """Specialized news search"""
            try:
                results, stats = sprites['news_search'](query=query, num_results=num_results)
                return {"tool": "news_search", "query": query, "num_results": num_results, "results": results, "stats": stats}
            except Exception as e:
                return {"error": str(e), "tool": "news_search"}
        
        @self.mcp.tool()
        def omni_search_tool(query: str, limit: int = 100) -> dict:
            """Internal omni database search"""
            try:
                results, stats = sprites['omni_search'](query, limit=limit)
                return {"tool": "omni_search", "query": query, "limit": limit, "results": results, "stats": stats}
            except Exception as e:
                return {"error": str(e), "tool": "omni_search"}
        
        @self.mcp.tool()
        def wiki_search_tool(query: str, sentences: int = 3) -> dict:
            """Wikipedia search and summaries"""
            import logging
            logger = logging.getLogger(__name__)

            logger.info("=" * 80)
            logger.info("🔧 SPRITE TOOL CALLED: wiki_search_tool")
            logger.info(f"   Query: {query}")
            logger.info(f"   Sentences: {sentences}")
            logger.info("=" * 80)

            try:
                results, stats = sprites['wiki_search'](query, sentences=sentences)
                logger.info("=" * 80)
                logger.info("✅ SPRITE TOOL COMPLETED: wiki_search_tool")
                logger.info(f"   Found {len(results) if results else 0} results")
                logger.info("=" * 80)
                return {"tool": "wiki_search", "query": query, "sentences": sentences, "results": results, "stats": stats}
            except Exception as e:
                logger.error("=" * 80)
                logger.error(f"❌ SPRITE TOOL FAILED: wiki_search_tool - {e}")
                logger.error("=" * 80)
                return {"error": str(e), "tool": "wiki_search"}
        
        @self.mcp.tool()
        def multimodel_query_tool(prompt: str, models: list = None, parallel: bool = True) -> dict:
            """Query multiple LLM models and get aggregated responses"""
            try:
                result = sprites['multimodel_query'](prompt=prompt, models=models, parallel=parallel)
                return {"tool": "multimodel_query", "prompt": prompt, "models": result.get("models", []), "parallel": parallel, "results": result.get("results", {}), "operational": result.get("operational", {})}
            except Exception as e:
                return {"error": str(e), "tool": "multimodel_query"}
        
        @self.mcp.tool()
        def frame_search_tool(query: str, section_slug: str = "agbase", frame_slug: str = "agbase_org__slim", limit: int = 100) -> dict:
            """Search data frames with text query"""
            try:
                results, stats = sprites['frame_search'](query=query, section_slug=section_slug, frame_slug=frame_slug, limit=limit)
                return {"tool": "frame_search", "query": query, "section_slug": section_slug, "frame_slug": frame_slug, "limit": limit, "results": results, "stats": stats}
            except Exception as e:
                return {"error": str(e), "tool": "frame_search"}
        
        @self.mcp.tool()
        def frame_list_tool(root_dir: str = "/var/lib/gaia/GAIA_FS/frames/") -> dict:
            """List available parquet data frames"""
            try:
                frames, stats = sprites['frame_list'](root_dir=root_dir)
                return {"tool": "frame_list", "root_dir": root_dir, "frames": frames, "stats": stats}
            except Exception as e:
                return {"error": str(e), "tool": "frame_list"}
        
        @self.mcp.tool()
        def investor_search_tool(name: str = "", country: str = "", investor_type: str = "venture vc", min_investments: int = 5, limit: int = 100) -> dict:
            """Search for investors with filters"""
            try:
                results, stats = sprites['investor_search'](name=name, country=country, investor_type=investor_type, min_investments=min_investments, limit=limit)
                return {"tool": "investor_search", "name": name, "country": country, "investor_type": investor_type, "min_investments": min_investments, "limit": limit, "results": results, "stats": stats}
            except Exception as e:
                return {"error": str(e), "tool": "investor_search"}
        
        @self.mcp.tool()
        def investor_stats_tool(investor_name: str, country: str = "US") -> dict:
            """Get detailed investor statistics and portfolio analysis"""
            try:
                stats, info = sprites['investor_stats'](investor_name=investor_name, country=country)
                return {"tool": "investor_stats", "investor_name": investor_name, "country": country, "stats": stats, "info": info}
            except Exception as e:
                return {"error": str(e), "tool": "investor_stats"}

        @self.mcp.tool()
        def llm_text_completion(prompt: str, system_prompt: str = None, model: str = None) -> dict:
            """Generate text completion using LLM"""
            try:
                result = sprites['llm_completion_text'](prompt=prompt, system_prompt=system_prompt, model=model)
                return {"tool": "llm_text_completion", "prompt": prompt, "system_prompt": system_prompt, "model": model, "result": result}
            except Exception as e:
                return {"error": str(e), "tool": "llm_text_completion"}

        @self.mcp.tool()
        def llm_json_completion(prompt: str, system_prompt: str = None, schema: dict = None, model: str = None) -> dict:
            """Generate structured JSON completion using LLM"""
            try:
                result = sprites['llm_completion_json'](prompt=prompt, system_prompt=system_prompt, schema=schema, model=model)
                return {"tool": "llm_json_completion", "prompt": prompt, "system_prompt": system_prompt, "schema": schema, "model": model, "result": result}
            except Exception as e:
                return {"error": str(e), "tool": "llm_json_completion"}

        @self.mcp.tool()
        def llm_code_analysis(code: str, language: str = "python", model: str = None) -> dict:
            """Analyze code quality, issues, and suggestions"""
            try:
                result = sprites['llm_analyze_code'](code=code, language=language, model=model)
                return {"tool": "llm_code_analysis", "code": code, "language": language, "model": model, "result": result}
            except Exception as e:
                return {"error": str(e), "tool": "llm_code_analysis"}

        @self.mcp.tool()
        def llm_code_generation(description: str, language: str = "python", style: str = "clean", model: str = None) -> dict:
            """Generate code based on description"""
            try:
                result = sprites['llm_generate_code'](description=description, language=language, style=style, model=model)
                return {"tool": "llm_code_generation", "description": description, "language": language, "style": style, "model": model, "result": result}
            except Exception as e:
                return {"error": str(e), "tool": "llm_code_generation"}

        @self.mcp.tool()
        def llm_concept_explanation(concept: str, level: str = "intermediate", model: str = None) -> dict:
            """Explain programming or technical concepts"""
            try:
                result = sprites['llm_explain_concept'](concept=concept, level=level, model=model)
                return {"tool": "llm_concept_explanation", "concept": concept, "level": level, "model": model, "result": result}
            except Exception as e:
                return {"error": str(e), "tool": "llm_concept_explanation"}

        @self.mcp.tool()
        def llm_error_debugging(error_message: str, code_context: str = "", model: str = None) -> dict:
            """Debug errors and provide solutions"""
            try:
                result = sprites['llm_debug_error'](error_message=error_message, code_context=code_context, model=model)
                return {"tool": "llm_error_debugging", "error_message": error_message, "code_context": code_context, "model": model, "result": result}
            except Exception as e:
                return {"error": str(e), "tool": "llm_error_debugging"}

        @self.mcp.tool()
        def deep_research_simple_tool(query: str, model: str = "o4-mini-deep-research", save_slug: str = None, project: str = "general") -> dict:
            """Perform simple deep research with web search and code interpretation"""
            import logging
            logger = logging.getLogger(__name__)

            logger.info("🔧 SPRITE TOOL CALLED: deep_research_simple_tool")
            logger.info(f"   Query: {query}")
            logger.info(f"   Model: {model}")
            logger.info(f"   Project: {project}")
            logger.info("   This will make MULTIPLE LLM calls for research and analysis")

            try:
                result = sprites['deep_research_simple'](query=query, model=model, save_slug=save_slug, project=project)
                logger.info("✅ SPRITE TOOL COMPLETED: deep_research_simple_tool")
                return {"tool": "deep_research_simple", "query": query, "model": model, "save_slug": save_slug, "project": project, "result": result}
            except Exception as e:
                logger.error(f"❌ SPRITE TOOL FAILED: deep_research_simple_tool - {e}")
                return {"error": str(e), "tool": "deep_research_simple"}

        @self.mcp.tool()
        def deep_research_robust_tool(query: str, criteria: str = "Comprehensive, accurate, well-sourced research", quality_level: str = "medium", execution_mode: str = "parallel") -> dict:
            """Perform robust deep research with multi-critic evaluation"""
            try:
                result = sprites['deep_research_robust'](query=query, criteria=criteria, quality_level=quality_level, execution_mode=execution_mode)
                return {"tool": "deep_research_robust", "query": query, "criteria": criteria, "quality_level": quality_level, "execution_mode": execution_mode, "result": result}
            except Exception as e:
                return {"error": str(e), "tool": "deep_research_robust"}

        @self.mcp.tool()
        def deep_research_batch_tool(queries: dict, model: str = "o4-mini-deep-research", project: str = "batch_research", parallel: bool = True) -> dict:
            """Perform batch deep research on multiple queries"""
            try:
                result = sprites['deep_research_batch'](queries=queries, model=model, project=project, parallel=parallel)
                return {"tool": "deep_research_batch", "queries": queries, "model": model, "project": project, "parallel": parallel, "result": result}
            except Exception as e:
                return {"error": str(e), "tool": "deep_research_batch"}

        @self.mcp.tool()
        def deep_research_load_tool(slug: str, project: str = "general") -> dict:
            """Load previously saved research results"""
            try:
                result = sprites['deep_research_load'](slug=slug, project=project)
                return {"tool": "deep_research_load", "slug": slug, "project": project, "result": result}
            except Exception as e:
                return {"error": str(e), "tool": "deep_research_load"}

        @self.mcp.tool()
        def deep_research_list_tool(project: str = "general") -> dict:
            """List all saved research in a project"""
            try:
                result = sprites['deep_research_list'](project=project)
                return {"tool": "deep_research_list", "project": project, "result": result}
            except Exception as e:
                return {"error": str(e), "tool": "deep_research_list"}

        @self.mcp.tool()
        def babyfetch_simple_tool(url: str, max_text_length: int = 1500) -> dict:
            """Fetch and parse web content from a URL with size limits to prevent hanging"""
            try:
                result = sprites['babyfetch_simple'](url=url, max_text_length=max_text_length)

                # DEBUG: Show what we're returning
                print(f"[DEBUG] babyfetch_simple_tool returning:")
                print(f"  Type: {type(result)}")
                print(f"  Keys: {list(result.keys()) if isinstance(result, dict) else 'Not a dict'}")
                print(f"  Success: {result.get('success') if isinstance(result, dict) else 'N/A'}")

                return result
            except Exception as e:
                error_result = {"success": False, "error": str(e), "tool": "babyfetch_simple"}
                print(f"[DEBUG] babyfetch_simple_tool error: {error_result}")
                return error_result

        @self.mcp.tool()
        def babyfetch_text_only_tool(url: str, max_length: int = 1000) -> dict:
            """Fetch only text content from a URL (most efficient)"""
            try:
                result = sprites['babyfetch_text_only'](url=url, max_length=max_length)
                # Return the result directly without wrapping
                return result
            except Exception as e:
                return {"success": False, "error": str(e), "tool": "babyfetch_text_only"}

        @self.mcp.tool()
        def babyfetch_multiple_simple_tool(urls: list, max_text_length: int = 1000) -> dict:
            """Fetch content from multiple URLs efficiently with size limits"""
            try:
                result = sprites['babyfetch_multiple_simple'](urls=urls, max_text_length=max_text_length)
                return {"tool": "babyfetch_multiple_simple", "urls": urls, "max_text_length": max_text_length, "result": result}
            except Exception as e:
                return {"error": str(e), "tool": "babyfetch_multiple_simple"}

        # ELF Memory Bank Tools
        @self.mcp.tool()
        def elf_mem_store_tool(bank: str, key: str, value: dict) -> dict:
            """Store data in an ELF memory bank with explicit bank naming"""
            try:
                result = sprites['elf_mem_store'](bank=bank, key=key, value=value)
                return result
            except Exception as e:
                return {"success": False, "error": str(e), "tool": "elf_mem_store"}

        @self.mcp.tool()
        def elf_mem_get_tool(bank: str, key: str) -> dict:
            """Retrieve data from an ELF memory bank"""
            try:
                result = sprites['elf_mem_get'](bank=bank, key=key)
                return result
            except Exception as e:
                return {"success": False, "error": str(e), "tool": "elf_mem_get"}

        @self.mcp.tool()
        def elf_mem_delete_tool(bank: str, key: str) -> dict:
            """Delete a key from an ELF memory bank"""
            try:
                result = sprites['elf_mem_delete'](bank=bank, key=key)
                return result
            except Exception as e:
                return {"success": False, "error": str(e), "tool": "elf_mem_delete"}

        @self.mcp.tool()
        def elf_mem_list_keys_tool(bank: str, key_prefix: str = None) -> dict:
            """List keys in an ELF memory bank with optional prefix filtering"""
            try:
                result = sprites['elf_mem_list_keys'](bank=bank, key_prefix=key_prefix)
                return result
            except Exception as e:
                return {"success": False, "error": str(e), "tool": "elf_mem_list_keys"}

        @self.mcp.tool()
        def elf_mem_bank_status_tool(bank: str) -> dict:
            """Get comprehensive status information about an ELF memory bank"""
            try:
                result = sprites['elf_mem_bank_status'](bank=bank)
                return result
            except Exception as e:
                return {"success": False, "error": str(e), "tool": "elf_mem_bank_status"}

        @self.mcp.tool()
        def elf_mem_list_banks_tool() -> dict:
            """List all available ELF memory banks"""
            try:
                result = sprites['elf_mem_list_banks']()
                return result
            except Exception as e:
                return {"success": False, "error": str(e), "tool": "elf_mem_list_banks"}

        # Simple Chart Tools
        @self.mcp.tool()
        def simple_chart_svg_tool(data_table: str, chart_spec: str) -> dict:
            """Generate SVG chart from data table with intelligent chart type selection"""
            import logging
            logger = logging.getLogger(__name__)

            logger.info("=" * 80)
            logger.info("🔧 SPRITE TOOL CALLED: simple_chart_svg_tool")
            logger.info(f"   Data table length: {len(data_table)} chars")
            logger.info(f"   Chart spec: {chart_spec}")
            logger.info("=" * 80)

            try:
                result = sprites['simple_chart_svg'](data_table=data_table, chart_spec=chart_spec)
                logger.info("=" * 80)
                logger.info("✅ SPRITE TOOL COMPLETED: simple_chart_svg_tool")
                logger.info(f"   Success: {result.get('success', False)}")
                logger.info(f"   Chart type: {result.get('chart_type', 'unknown')}")
                logger.info("=" * 80)
                return result
            except Exception as e:
                logger.error("=" * 80)
                logger.error(f"❌ SPRITE TOOL FAILED: simple_chart_svg_tool - {e}")
                logger.error("=" * 80)
                return {"success": False, "svg_content": "", "chart_type": "", "error": str(e)}

        @self.mcp.tool()
        def simple_chart_png_tool(data_table: str, chart_spec: str) -> dict:
            """Generate PNG chart file from data table with intelligent chart type selection"""
            import logging
            logger = logging.getLogger(__name__)

            logger.info("=" * 80)
            logger.info("🔧 SPRITE TOOL CALLED: simple_chart_png_tool")
            logger.info(f"   Data table length: {len(data_table)} chars")
            logger.info(f"   Chart spec: {chart_spec}")
            logger.info("=" * 80)

            try:
                result = sprites['simple_chart_png'](data_table=data_table, chart_spec=chart_spec)
                logger.info("=" * 80)
                logger.info("✅ SPRITE TOOL COMPLETED: simple_chart_png_tool")
                logger.info(f"   Success: {result.get('success', False)}")
                logger.info(f"   Chart type: {result.get('chart_type', 'unknown')}")
                logger.info("=" * 80)
                return result
            except Exception as e:
                logger.error("=" * 80)
                logger.error(f"❌ SPRITE TOOL FAILED: simple_chart_png_tool - {e}")
                logger.error("=" * 80)
                return {"success": False, "png_path": "", "chart_type": "", "error": str(e)}

        @self.mcp.tool()
        def quick_chart_tool(data_table: str, chart_type: str = "auto") -> dict:
            """Quick chart generation with minimal configuration"""
            import logging
            logger = logging.getLogger(__name__)

            logger.info("=" * 80)
            logger.info("🔧 SPRITE TOOL CALLED: quick_chart_tool")
            logger.info(f"   Data table length: {len(data_table)} chars")
            logger.info(f"   Chart type: {chart_type}")
            logger.info("=" * 80)

            try:
                result = sprites['quick_chart'](data_table=data_table, chart_type=chart_type)
                logger.info("=" * 80)
                logger.info("✅ SPRITE TOOL COMPLETED: quick_chart_tool")
                logger.info(f"   Success: {result.get('success', False)}")
                logger.info(f"   Chart type: {result.get('chart_type', 'unknown')}")
                logger.info("=" * 80)
                return result
            except Exception as e:
                logger.error("=" * 80)
                logger.error(f"❌ SPRITE TOOL FAILED: quick_chart_tool - {e}")
                logger.error("=" * 80)
                return {"success": False, "svg_content": "", "chart_type": "", "error": str(e)}

        # AgSearch Models Tools
        @self.mcp.tool()
        def agsearch_list_contexts_tool(limit: int = 100) -> dict:
            """List AsContext records from legacy agsearch database

            Args:
                limit: Maximum number of contexts to return (default: 100)
            """
            print(f"🔍 MCP TOOL DEBUG: agsearch_list_contexts_tool called with limit={limit}")
            print(f"🔍 MCP TOOL DEBUG: sprites available: {'list_contexts' in sprites}")
            print(f"🔍 MCP TOOL DEBUG: sprites keys: {list(sprites.keys())[:10]}")

            try:
                print(f"🔍 MCP TOOL DEBUG: About to call sprites['list_contexts'](limit={limit})")
                result = sprites['list_contexts'](limit=limit)
                print(f"🔍 MCP TOOL DEBUG: sprites['list_contexts'] returned: {len(result) if isinstance(result, list) else type(result)}")
                return {"tool": "agsearch_list_contexts", "limit": limit, "contexts": result}
            except Exception as e:
                print(f"🔍 MCP TOOL DEBUG: Exception in agsearch_list_contexts_tool: {e}")
                import traceback
                traceback.print_exc()
                return {"error": str(e), "tool": "agsearch_list_contexts"}

        @self.mcp.tool()
        def agsearch_search_contexts_tool(name_query: str, limit: int = 50) -> dict:
            """Search AsContext records by name"""
            try:
                result = sprites['search_contexts'](name_query=name_query, limit=limit)
                return {"tool": "agsearch_search_contexts", "name_query": name_query, "limit": limit, "contexts": result}
            except Exception as e:
                return {"error": str(e), "tool": "agsearch_search_contexts"}

        @self.mcp.tool()
        def agsearch_fetch_context_tool(context_id: int) -> dict:
            """Fetch a specific AsContext by ID"""
            try:
                result = sprites['fetch_context'](context_id=context_id)
                return {"tool": "agsearch_fetch_context", "context_id": context_id, "context": result}
            except Exception as e:
                return {"error": str(e), "tool": "agsearch_fetch_context"}

        @self.mcp.tool()
        def agsearch_create_context_tool(name: str, context_type: str = 'draft', keywords: str = None,
                                       comments: str = None, created_by_id: int = None, tagtree_id: int = None) -> dict:
            """Create a new AsContext record

            Args:
                name: Context name (required)
                context_type: Context type - 'draft', 'tag', 'client', 'compete', 'junk' (default: 'draft')
                keywords: Optional keywords text
                comments: Optional comments text
                created_by_id: Optional user ID who created this context
                tagtree_id: Optional TagTree ID if applicable
            """
            try:
                result = sprites['create_context'](
                    name=name, context_type=context_type, keywords=keywords,
                    comments=comments, created_by_id=created_by_id, tagtree_id=tagtree_id
                )
                return {"tool": "agsearch_create_context", "name": name, "context_type": context_type, "context": result}
            except Exception as e:
                return {"error": str(e), "tool": "agsearch_create_context"}

        @self.mcp.tool()
        def agsearch_list_ratings_tool(context_id: int, limit: int = 1000) -> dict:
            """List all AsRating records for a specific context"""
            try:
                result = sprites['list_ratings_for_context'](context_id=context_id, limit=limit)
                return {"tool": "agsearch_list_ratings", "context_id": context_id, "limit": limit, "ratings": result}
            except Exception as e:
                return {"error": str(e), "tool": "agsearch_list_ratings"}

        @self.mcp.tool()
        def agsearch_create_rating_tool(es_id: str, context_id: int, rating: int = 0,
                                      created_by_id: int = None, es_idx: str = None, goid: str = None) -> dict:
            """Create a new AsRating record

            Args:
                es_id: Elasticsearch document ID (required)
                context_id: AsContext ID this rating belongs to (required)
                rating: Rating value - typically -1 (negative), 0 (neutral), or 1 (positive) (default: 0)
                created_by_id: Optional user ID who created this rating
                es_idx: Optional Elasticsearch index name
                goid: Optional additional identifier
            """
            try:
                result = sprites['create_rating'](
                    es_id=es_id, context_id=context_id, rating=rating,
                    created_by_id=created_by_id, es_idx=es_idx, goid=goid
                )
                return {"tool": "agsearch_create_rating", "es_id": es_id, "context_id": context_id, "rating": rating, "rating_record": result}
            except Exception as e:
                return {"error": str(e), "tool": "agsearch_create_rating"}

        @self.mcp.tool()
        def agsearch_delete_rating_tool(rating_id: int) -> dict:
            """Delete an AsRating record by ID"""
            try:
                result = sprites['delete_rating'](rating_id=rating_id)
                return {"tool": "agsearch_delete_rating", "rating_id": rating_id, "success": result}
            except Exception as e:
                return {"error": str(e), "tool": "agsearch_delete_rating"}

        # Debug Tools
        @self.mcp.tool()
        def debug_database_connection_tool(database_name: str = 'legacy') -> dict:
            """Test database connection and query capabilities"""
            try:
                result = sprites['debug_database_connection'](database_name=database_name)
                return {"tool": "debug_database_connection", "database_name": database_name, "result": result}
            except Exception as e:
                return {"error": str(e), "tool": "debug_database_connection"}

        @self.mcp.tool()
        def debug_django_setup_tool() -> dict:
            """Verify Django configuration and app loading"""
            try:
                result = sprites['debug_django_setup']()
                return {"tool": "debug_django_setup", "result": result}
            except Exception as e:
                return {"error": str(e), "tool": "debug_django_setup"}

        @self.mcp.tool()
        def debug_sprite_function_tool(function_name: str = 'list_contexts', limit: int = 3) -> dict:
            """Test specific sprite function execution"""
            try:
                result = sprites['debug_sprite_function'](function_name=function_name, limit=limit)
                return {"tool": "debug_sprite_function", "function_name": function_name, "result": result}
            except Exception as e:
                return {"error": str(e), "tool": "debug_sprite_function"}

        @self.mcp.tool()
        def debug_mcp_context_tool() -> dict:
            """Debug MCP execution context and environment"""
            try:
                result = sprites['debug_mcp_context']()
                return {"tool": "debug_mcp_context", "result": result}
            except Exception as e:
                return {"error": str(e), "tool": "debug_mcp_context"}

        # SQL Agent Tools
        @self.mcp.tool()
        def sql_query_tool(query: str, db: str = "agbase_reportdb", execute: bool = True, format_type: str = "dict", model: str = None, debug: bool = False) -> dict:
            """Run natural language SQL queries against AgFunder databases"""
            import logging
            logger = logging.getLogger(__name__)

            logger.info("=" * 80)
            logger.info("🔧 SPRITE TOOL CALLED: sql_query_tool")
            logger.info(f"   Query: {query}")
            logger.info(f"   Database: {db}")
            logger.info(f"   Execute: {execute}")
            logger.info(f"   Format: {format_type}")
            logger.info("=" * 80)

            try:
                results, metadata = sprites['sql_query'](query=query, db=db, execute=execute, format_type=format_type, model=model, debug=debug)
                logger.info("=" * 80)
                logger.info("✅ SPRITE TOOL COMPLETED: sql_query_tool")
                logger.info(f"   Success: {metadata.get('success', False)}")
                logger.info(f"   Row count: {metadata.get('row_count', 'N/A')}")
                logger.info("=" * 80)
                return {"tool": "sql_query", "query": query, "db": db, "execute": execute, "format_type": format_type, "results": results, "metadata": metadata}
            except Exception as e:
                logger.error("=" * 80)
                logger.error(f"❌ SPRITE TOOL FAILED: sql_query_tool - {e}")
                logger.error("=" * 80)
                return {"error": str(e), "tool": "sql_query"}

        @self.mcp.tool()
        def list_databases_tool() -> dict:
            """List available databases and their descriptions"""
            try:
                result = sprites['list_databases']()
                return {"tool": "list_databases", "databases": result}
            except Exception as e:
                return {"error": str(e), "tool": "list_databases"}

        @self.mcp.tool()
        def quick_sql_tool(query: str, db: str = "agbase_reportdb") -> dict:
            """Ultra-simple one-liner for quick SQL queries"""
            try:
                results = sprites['quick_sql'](query=query, db=db)
                return {"tool": "quick_sql", "query": query, "db": db, "results": results}
            except Exception as e:
                return {"error": str(e), "tool": "quick_sql"}

        # BabyFetch Full Tool
        @self.mcp.tool()
        def babyfetch_url_tool(url: str, max_text_length: int = 2000, include_html: bool = False, max_age: int = None, refresh_cache: bool = False) -> dict:
            """Fetch and parse content from a URL using CachedBabyFetch (full version)"""
            import logging
            logger = logging.getLogger(__name__)

            logger.info("=" * 80)
            logger.info("🔧 SPRITE TOOL CALLED: babyfetch_url_tool")
            logger.info(f"   URL: {url}")
            logger.info(f"   Max text length: {max_text_length}")
            logger.info(f"   Include HTML: {include_html}")
            logger.info(f"   Max age: {max_age}")
            logger.info(f"   Refresh cache: {refresh_cache}")
            logger.info("=" * 80)

            try:
                result = sprites['babyfetch_url'](url=url, max_text_length=max_text_length, include_html=include_html, max_age=max_age, refresh_cache=refresh_cache)
                logger.info("=" * 80)
                logger.info("✅ SPRITE TOOL COMPLETED: babyfetch_url_tool")
                logger.info(f"   Success: {result.get('success', False)}")
                logger.info(f"   Cache hit: {result.get('cache_hit', False)}")
                logger.info("=" * 80)
                return result
            except Exception as e:
                logger.error("=" * 80)
                logger.error(f"❌ SPRITE TOOL FAILED: babyfetch_url_tool - {e}")
                logger.error("=" * 80)
                return {"success": False, "error": str(e), "tool": "babyfetch_url"}

        # Update tool registry (agsearch is now added individually with MCP wrapper)
        self.tool_registry.update({
            "market_research_tool": {"name": "market_research_tool", "description": "Get market research data"},
            "web_search_tool": {"name": "web_search_tool", "description": "Multi-source web search"},
            "news_search_tool": {"name": "news_search_tool", "description": "News search"},
            "omni_search_tool": {"name": "omni_search_tool", "description": "Internal omni search"},
            "wiki_search_tool": {"name": "wiki_search_tool", "description": "Wikipedia search"},
            "multimodel_query_tool": {"name": "multimodel_query_tool", "description": "Multi-LLM queries"},
            "frame_search_tool": {"name": "frame_search_tool", "description": "Data frame search"},
            "frame_list_tool": {"name": "frame_list_tool", "description": "List data frames"},
            "investor_search_tool": {"name": "investor_search_tool", "description": "Investor search"},
            "investor_stats_tool": {"name": "investor_stats_tool", "description": "Investor statistics"},
            "llm_text_completion": {"name": "llm_text_completion", "description": "Generate text using LLM"},
            "llm_json_completion": {"name": "llm_json_completion", "description": "Generate structured JSON using LLM"},
            "llm_code_analysis": {"name": "llm_code_analysis", "description": "Analyze code quality and issues"},
            "llm_code_generation": {"name": "llm_code_generation", "description": "Generate code from description"},
            "llm_concept_explanation": {"name": "llm_concept_explanation", "description": "Explain technical concepts"},
            "llm_error_debugging": {"name": "llm_error_debugging", "description": "Debug errors and provide solutions"},
            "deep_research_simple_tool": {"name": "deep_research_simple_tool", "description": "Simple deep research with web search"},
            "deep_research_robust_tool": {"name": "deep_research_robust_tool", "description": "Robust deep research with multi-critic evaluation"},
            "deep_research_batch_tool": {"name": "deep_research_batch_tool", "description": "Batch deep research on multiple queries"},
            "deep_research_load_tool": {"name": "deep_research_load_tool", "description": "Load saved research results"},
            "deep_research_list_tool": {"name": "deep_research_list_tool", "description": "List saved research in project"},
            "babyfetch_simple_tool": {"name": "babyfetch_simple_tool", "description": "Fetch web content with size limits (prevents hanging)"},
            "babyfetch_text_only_tool": {"name": "babyfetch_text_only_tool", "description": "Fetch only text content from URL (most efficient)"},
            "babyfetch_multiple_simple_tool": {"name": "babyfetch_multiple_simple_tool", "description": "Fetch content from multiple URLs efficiently"},
            "elf_mem_store_tool": {"name": "elf_mem_store_tool", "description": "Store data in ELF memory bank with explicit bank naming"},
            "elf_mem_get_tool": {"name": "elf_mem_get_tool", "description": "Retrieve data from ELF memory bank"},
            "elf_mem_delete_tool": {"name": "elf_mem_delete_tool", "description": "Delete key from ELF memory bank"},
            "elf_mem_list_keys_tool": {"name": "elf_mem_list_keys_tool", "description": "List keys in ELF memory bank with optional prefix filtering"},
            "elf_mem_bank_status_tool": {"name": "elf_mem_bank_status_tool", "description": "Get comprehensive ELF memory bank status information"},
            "elf_mem_list_banks_tool": {"name": "elf_mem_list_banks_tool", "description": "List all available ELF memory banks"},
            "simple_chart_svg_tool": {"name": "simple_chart_svg_tool", "description": "Generate SVG chart from data with intelligent chart type selection"},
            "simple_chart_png_tool": {"name": "simple_chart_png_tool", "description": "Generate PNG chart file from data with intelligent chart type selection"},
            "quick_chart_tool": {"name": "quick_chart_tool", "description": "Quick chart generation with minimal configuration"},
            "agsearch_list_contexts_tool": {"name": "agsearch_list_contexts_tool", "description": "List AsContext records from legacy agsearch database"},
            "agsearch_search_contexts_tool": {"name": "agsearch_search_contexts_tool", "description": "Search AsContext records by name"},
            "agsearch_fetch_context_tool": {"name": "agsearch_fetch_context_tool", "description": "Fetch a specific AsContext by ID"},
            "agsearch_create_context_tool": {"name": "agsearch_create_context_tool", "description": "Create a new AsContext record"},
            "agsearch_list_ratings_tool": {"name": "agsearch_list_ratings_tool", "description": "List all AsRating records for a specific context"},
            "agsearch_create_rating_tool": {"name": "agsearch_create_rating_tool", "description": "Create a new AsRating record"},
            "agsearch_delete_rating_tool": {"name": "agsearch_delete_rating_tool", "description": "Delete an AsRating record by ID"},
            "debug_database_connection_tool": {"name": "debug_database_connection_tool", "description": "Test database connection and query capabilities"},
            "debug_django_setup_tool": {"name": "debug_django_setup_tool", "description": "Verify Django configuration and app loading"},
            "debug_sprite_function_tool": {"name": "debug_sprite_function_tool", "description": "Test specific sprite function execution"},
            "debug_mcp_context_tool": {"name": "debug_mcp_context_tool", "description": "Debug MCP execution context and environment"},
            "sql_query_tool": {"name": "sql_query_tool", "description": "Run natural language SQL queries against AgFunder databases"},
            "list_databases_tool": {"name": "list_databases_tool", "description": "List available databases and their descriptions"},
            "quick_sql_tool": {"name": "quick_sql_tool", "description": "Ultra-simple one-liner for quick SQL queries"},
            "babyfetch_url_tool": {"name": "babyfetch_url_tool", "description": "Fetch and parse content from a URL using CachedBabyFetch (full version)"}
        })
    
    async def run_http_server(self, host: str = "localhost", port: int = 8000):
        """Run the MCP server using HTTP streaming protocol"""
        try:
            import uvicorn
            from fastapi import FastAPI
            from fastapi.middleware.cors import CORSMiddleware
            
            print(f"Starting {self.server_name} on HTTP... (Sprites available: {self.sprites_available})", file=sys.stderr)
            print(f"Server will be available at: http://{host}:{port}", file=sys.stderr)
            
            # Create FastAPI app
            app = FastAPI(title=self.server_name, version="1.0.0")
            
            # Add CORS middleware
            app.add_middleware(
                CORSMiddleware,
                allow_origins=["*"],
                allow_credentials=True,
                allow_methods=["*"],
                allow_headers=["*"],
            )
            
            @app.get("/")
            async def root():
                return {
                    "name": self.server_name,
                    "version": "1.0.0",
                    "sprites_available": self.sprites_available,
                    "tools_count": len(self.tool_registry)
                }
            
            @app.get("/tools")
            async def list_tools():
                """List all available sprite tools"""
                if not self.sprites_available:
                    return {"tools": [], "error": "Sprites not available"}
                return {"tools": list(self.tool_registry.values())}
            
            @app.post("/tools/{tool_name}")
            async def call_tool(tool_name: str, arguments: dict):
                """Call a specific tool with arguments"""
                if not self.sprites_available:
                    return {"error": "Sprites not available"}
                
                if tool_name not in self.tool_registry:
                    return {"error": f"Tool {tool_name} not found"}
                
                try:
                    result = self.mcp.call_tool(tool_name, arguments)
                    return {"result": result, "success": True}
                except Exception as e:
                    return {"error": str(e), "success": False}
            
            @app.get("/health")
            async def health_check():
                """Health check endpoint"""
                return {
                    "status": "healthy",
                    "sprites_available": self.sprites_available,
                    "tools_count": len(self.tool_registry)
                }
            
            # Run with uvicorn
            config = uvicorn.Config(app=app, host=host, port=port, log_level="info")
            server_instance = uvicorn.Server(config)
            await server_instance.serve()
            
        except ImportError as e:
            print(f"HTTP transport dependencies not available: {e}", file=sys.stderr)
            print("Install with: pip install uvicorn fastapi", file=sys.stderr)
            print("Falling back to stdio transport...", file=sys.stderr)
            self.run_stdio_server()
    
    def run_stdio_server(self):
        """Run the MCP server using stdio protocol (legacy)"""
        print(f"Starting {self.server_name} on stdio... (Sprites available: {self.sprites_available})", file=sys.stderr)
        self.mcp.run()
    
    def run_server(self, transport: str = "http", host: str = "localhost", port: int = 8000):
        """Run the server with specified transport"""
        if transport == "http":
            asyncio.run(self.run_http_server(host, port))
        else:
            self.run_stdio_server()
    
    @classmethod
    def main(cls):
        """Main entry point with command line argument parsing"""
        parser = argparse.ArgumentParser(description=f"{cls.__name__}")
        parser.add_argument("--transport", choices=["stdio", "http"], default="http", 
                           help="Transport protocol to use (default: http)")
        parser.add_argument("--host", default="localhost", 
                           help="Host to bind to for HTTP transport (default: localhost)")
        parser.add_argument("--port", type=int, default=8000, 
                           help="Port to bind to for HTTP transport (default: 8000)")
        
        args = parser.parse_args()
        
        # Create server instance and run
        server = cls()
        server.run_server(args.transport, args.host, args.port)
