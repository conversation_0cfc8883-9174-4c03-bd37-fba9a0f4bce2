#!/usr/bin/env python3
"""
Diagnose MCP Server Issues

Check if the server is actually using the MCP wrapper by calling server_info
and looking for diagnostic information.
"""

import asyncio
import json
import sys
import os

# Add the project root to Python path
sys.path.insert(0, '/home/<USER>/django-projects/agbase_admin')

from gaia.gaia_ceto.proto_mcp_http.mcp_http_clientlib import MCPClient<PERSON>ib

async def diagnose_server():
    """Diagnose the MCP server to see if wrapper is working."""
    print("🔍 Diagnosing MCP Server")
    print("=" * 50)
    
    client = MCPClientLib()
    
    try:
        # Connect to server
        print("🔌 Connecting to MCP server...")
        success = await client.connect_to_server("http://localhost:9000/mcp")
        
        if not success:
            print("❌ Failed to connect to server")
            return
        
        print("✅ Connected successfully!")
        
        # Get server info
        print(f"\n📋 Getting server info...")
        result = await client.call_tool(
            tool_name="server_info",
            tool_input={},
            tool_call_id="diagnose_test"
        )
        
        if result.success:
            print("✅ Server info retrieved successfully")
            if isinstance(result.content, list) and len(result.content) > 0:
                first_item = result.content[0]
                if hasattr(first_item, 'text'):
                    print(f"\n📄 Server Info:")
                    print(first_item.text)
                    
                    # Look for MCP wrapper indicators
                    if "MCP response wrapper" in first_item.text:
                        print("🎉 Found MCP wrapper references in server info!")
                    else:
                        print("⚠️ No MCP wrapper references found in server info")
        else:
            print(f"❌ Failed to get server info: {result.error}")
        
        # List tools to see what's available
        print(f"\n📋 Available tools:")
        tools = client.available_tools
        sprite_tools = [t for t in tools if 'quick_search' in t.get('name', '')]
        
        for tool in sprite_tools:
            print(f"  • {tool.get('name', 'unknown')}: {tool.get('description', 'no description')}")
        
        # Test a simple tool to see the raw response
        print(f"\n🧪 Testing echostring tool (should be simple)...")
        echo_result = await client.call_tool(
            tool_name="echostring",
            tool_input={"message": "test"},
            tool_call_id="echo_test"
        )
        
        if echo_result.success:
            print("✅ Echo test successful")
            if isinstance(echo_result.content, list) and len(echo_result.content) > 0:
                first_item = echo_result.content[0]
                if hasattr(first_item, 'text'):
                    print(f"📄 Echo response: {first_item.text}")
        
    except Exception as e:
        print(f"💥 Exception: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        try:
            await client.cleanup()
        except Exception as e:
            print(f"⚠️ Cleanup warning: {e}")

if __name__ == "__main__":
    try:
        asyncio.run(diagnose_server())
    except KeyboardInterrupt:
        print("\n⚠️ Interrupted by user")
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
