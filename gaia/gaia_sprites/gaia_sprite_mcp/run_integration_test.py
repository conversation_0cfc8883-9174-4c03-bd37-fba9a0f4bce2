#!/usr/bin/env python3
"""
Runner script for MCP integration tests.

This script provides a convenient way to run the MCP integration tests
with proper error handling and setup verification.
"""

import sys
import subprocess
import time
import requests


def check_dependencies():
    """Check if required dependencies are available."""
    try:
        import requests
        print("✅ requests library available")
        return True
    except ImportError:
        print("❌ requests library not found. Install with: pip install requests")
        return False


def check_server_connectivity(max_retries=3, retry_delay=2):
    """
    Check if the MCP server is accessible with retries.
    
    Args:
        max_retries: Maximum number of connection attempts
        retry_delay: Delay between retries in seconds
    """
    print(f"🔍 Checking MCP server connectivity (max {max_retries} attempts)...")
    
    for attempt in range(1, max_retries + 1):
        try:
            response = requests.get("http://localhost:9000/health", timeout=5)
            if response.status_code == 200:
                print(f"✅ MCP server is accessible on port 9000")
                return True
            else:
                print(f"⚠️  Attempt {attempt}: Server responded with status {response.status_code}")
        except requests.exceptions.ConnectionError:
            print(f"⚠️  Attempt {attempt}: Connection refused")
        except requests.exceptions.Timeout:
            print(f"⚠️  Attempt {attempt}: Connection timeout")
        except Exception as e:
            print(f"⚠️  Attempt {attempt}: Unexpected error: {e}")
        
        if attempt < max_retries:
            print(f"   Retrying in {retry_delay} seconds...")
            time.sleep(retry_delay)
    
    print("❌ Could not connect to MCP server on port 9000")
    print("\n💡 Troubleshooting tips:")
    print("   1. Ensure the MCP server is running")
    print("   2. Check if port 9000 is available")
    print("   3. Verify server configuration")
    print("   4. Check firewall settings")
    
    return False


def run_integration_test():
    """Run the MCP integration test."""
    print("🚀 Running MCP Integration Test")
    print("="*50)
    
    try:
        # Run the test script
        result = subprocess.run([
            sys.executable, 
            "mcp_integration_tests_tools.py"
        ], capture_output=True, text=True, timeout=60)
        
        # Print output
        if result.stdout:
            print("📋 Test Output:")
            print(result.stdout)
        
        if result.stderr:
            print("⚠️  Test Errors:")
            print(result.stderr)
        
        # Check result
        if result.returncode == 0:
            print("\n🎉 Integration test completed successfully!")
            return True
        else:
            print(f"\n❌ Integration test failed with exit code {result.returncode}")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ Test timed out after 60 seconds")
        return False
    except Exception as e:
        print(f"❌ Error running test: {e}")
        return False


def main():
    """Main function to run all checks and tests."""
    print("MCP Integration Test Runner")
    print("="*40)
    
    # Check dependencies
    print("\n1️⃣ Checking dependencies...")
    if not check_dependencies():
        sys.exit(1)
    
    # Check server connectivity
    print("\n2️⃣ Checking server connectivity...")
    if not check_server_connectivity():
        print("\n❌ Cannot proceed without server connectivity")
        print("\n🔧 To start the MCP server, try:")
        print("   cd /path/to/sprite/mcp/server")
        print("   python sprite_mcp_server.py --transport http --port 9000")
        sys.exit(1)
    
    # Run the integration test
    print("\n3️⃣ Running integration test...")
    success = run_integration_test()
    
    print("\n" + "="*40)
    if success:
        print("✅ ALL CHECKS PASSED!")
        print("✅ MCP server is working correctly!")
        sys.exit(0)
    else:
        print("❌ INTEGRATION TEST FAILED!")
        print("❌ Please check the server and try again!")
        sys.exit(1)


if __name__ == "__main__":
    main()
