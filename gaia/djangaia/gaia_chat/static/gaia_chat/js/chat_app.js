// Chat App JavaScript
// This file contains the Vue.js application logic for the chat interface

/* -----------------------------------------------------------
 *  Composite-pattern renderer (no Markdown dependency)
 *  wired into the Vue instance as methods.
 * ----------------------------------------------------------- */

/* ---------- Composite hierarchy (outside Vue) ---------- */
class Node {
    render () { throw new Error('Subclasses must implement render()'); }
}

class TextNode extends Node {
    constructor (text) { super(); this.text = text; }
    render () { return ChatHelpers.plainTextToHtml(this.text); }
}

class ListNode extends Node {
    constructor () { super(); this.children = []; }
    add (child)  { this.children.push(child); }
    render () {
        return `<ul>${this.children.map(c => `<li>${c.render()}</li>`).join('')}</ul>`;
  }
}

class TableNode extends Node {
  constructor ({ title = '', columns = [] } = {}) {
    super();
    this.title   = title;
    this.columns = columns;
    this.rows    = [];               // array<array<Node>>
  }
  addRow (...cells) { this.rows.push(cells); }

  render () {
    let html = '';

    if (this.title) {
        html += `<h4 style="margin-bottom:10px;color:#333;">${ChatHelpers.escapeHtml(this.title)}</h4>`;
    }

    html += '<table style="border-collapse:collapse;width:100%;margin:10px 0;border:1px solid #ddd;">';

    if (this.columns.length) {
        html += '<thead><tr>';
        html += this.columns
            .map(c => `<th style="border:1px solid #ddd;padding:8px;background:#f5f5f5;text-align:left;font-weight:bold;">${ChatHelpers.escapeHtml(c)}</th>`)
            .join('');
        html += '</tr></thead>';
    }

    html += '<tbody>';
    this.rows.forEach((row, i) => {
        const bg = i % 2 ? '#f9f9f9' : '#ffffff';
        html += `<tr style="background-color:${bg};">`;
        html += row
            .map(cell => `<td style="border:1px solid #ddd;padding:8px;">${cell.render()}</td>`)
            .join('');
        html += '</tr>';
    });
    html += '</tbody></table>';

    return html;
  }
}


// Configure Vue to use [[ ]] for interpolation to avoid conflict with Django templates
Vue.config.delimiters = ['[[', ']]'];

// Initialize the Vue application
function initializeChatApp(djangoData) {

    /* helper namespace referenced by Node classes */
    const ChatHelpers = {
        /* ---------- basic escaping / plain-text → HTML ---------- */
        escapeHtml (str = '') {
        return str
            .replace(/&/g,  '&amp;')
            .replace(/</g,  '&lt;')
            .replace(/>/g,  '&gt;')
            .replace(/"/g,  '&quot;')
            .replace(/'/g,  '&#39;');
        },
        plainTextToHtml (text) {
        return ChatHelpers.escapeHtml(text).replace(/\r?\n/g, '<br>');
        }
    };

    /* expose helpers for Node classes */
    window.ChatHelpers = ChatHelpers;


    new Vue({
        el: '#app',
        delimiters: ['[[', ']]'],
        data: {
            // LLM settings
            providers: [],
            models: [],
            selectedProvider: 'litellm',
            selectedModel: '',
            hasOpenAIKey: djangoData.hasOpenAIKey,
            hasAnthropicKey: djangoData.hasAnthropicKey,
            isSettingLLM: false,
            mcpProtocol: '',  // Will store the MCP protocol (SSE or HTTP)

            // Conversations
            conversations: [],
            activeConversation: null,
            newConversationTitle: '',
            isCreatingConversation: false,
            isLoadingConversation: false,
            isDeletingConversation: null,

            // Messaging
            userInput: '',
            isSendingMessage: false,
            isStreamingMessage: false,
            streamingProgress: [],
            streamingInfo: [],
            toolResult: null,

            // Errors
            error: null,

            // Chat context
            chatContext: djangoData.chatContext,

            // UI Settings
            showSystemMessages: false,  // Toggle for showing/hiding system messages

            // Navigation - will be set in mounted()
            activeNavSection: null,

            // Debug panel
            debugPanelVisible: false,
            debugLogs: [],
            debugPaneMode: 'calls', // 'calls', 'api', 'history', 'tools', 'memory', 'errors'
            mcpTools: [],
            mcpToolsLoading: false,
            callDetails: [], // Separate array for call tracking

            // Error tracking
            errorLog: [], // Array to store detailed error information
            maxErrors: 100, // Maximum number of errors to keep

            // Waiting animation system
            waitingAnimation: {
                active: false,
                currentPhrase: '',
                phraseIndex: 0,
                animationFrame: null,
                startTime: 0,
                animationType: 0, // 0=pulse, 1=matrix, 2=neural
                phrases: [
                    'synapses firing',
                    'neurons processing',
                    'analyzing quantum patterns',
                    'interfacing with neural networks',
                    'processing cognitive matrices',
                    'calibrating thought vectors',
                    'synthesizing data streams',
                    'engaging neural pathways',
                    'computing semantic layers',
                    'activating intelligence cores'
                ]
            },

            // Memory bank tracking
            memoryBankFiles: [],
            memoryBankLoading: false,
            memoryBankError: null,
            memoryBankLastUpdated: null,

            // Tool inspector
            toolSearchQuery: '',
            currentProviderName: 'Unknown',

            // Pagination
            currentPage: 1,
            itemsPerPage: 100,  // Show 100 companies per page

            // Dynamic loading
            isLoading: false,
            loadingError: null,
            searchTerm: '',
            totalCompanies: 0,
            totalPages: 0,

            // Add this to the data object
            sidebarChatContainer: null,

            // Store Django data for use in methods
            djangoData: djangoData
        },
        computed: {
            // Filtered tools for search
            filteredTools() {
                if (!this.mcpTools || this.mcpTools.length === 0) {
                    return [];
                }

                // All tools should now be proper objects from the API
                const toolsWithExpansion = this.mcpTools.map(tool => ({
                    ...tool,
                    expanded: false
                }));

                if (!this.toolSearchQuery) {
                    return toolsWithExpansion;
                }

                const query = this.toolSearchQuery.toLowerCase();
                return toolsWithExpansion.filter(tool =>
                    tool.name.toLowerCase().includes(query) ||
                    (tool.description && tool.description.toLowerCase().includes(query))
                );
            },

            // Dynamic pagination computed properties
            computedTotalPages() {
                if (this.chatContext && this.chatContext.dynamic_loading) {
                    return this.totalPages; // From server response
                }
                if (!this.chatContext || !this.chatContext.data) return 1;
                return Math.ceil(this.chatContext.data.length / this.itemsPerPage);
            },
            paginatedCompanies() {
                if (this.chatContext && this.chatContext.dynamic_loading) {
                    return this.chatContext.data; // Already paginated from server
                }
                if (!this.chatContext || !this.chatContext.data) return [];
                const start = (this.currentPage - 1) * this.itemsPerPage;
                const end = start + this.itemsPerPage;
                return this.chatContext.data.slice(start, end);
            },
            displayTotalCompanies() {
                if (this.chatContext && this.chatContext.dynamic_loading) {
                    return this.totalCompanies; // From server response
                }
                return this.chatContext ? this.chatContext.data.length : 0;
            },
            // Filter messages based on showSystemMessages toggle
            filteredMessages() {
                if (!this.activeConversation || !this.activeConversation.messages) {
                    return [];
                }
                if (this.showSystemMessages) {
                    return this.activeConversation.messages;
                } else {
                    return this.activeConversation.messages.filter(msg => msg.role !== 'system');
                }
            }
        },
        mounted() {
            // Set active navigation section based on current URL
            this.activeNavSection = this.getActiveNavSectionFromUrl();

            // Test JSON highlighting function
            //console.log('Testing JSON highlighting...');
            const testJson = '{"columns": ["id", "name"], "data": [[1, "Alice"], [2, "Bob"]]}';
            const highlighted = this.highlightJson(JSON.stringify(JSON.parse(testJson), null, 4));
            //console.log('Highlighted JSON:', highlighted);

            this.fetchProviders();
            this.setupGlobalErrorHandlers();
            this.fetchConversations();

            // Check if there's a conversation ID in the URL and load it
            this.checkAndLoadConversationFromUrl();

            // Load company data if dynamic loading is enabled
            if (this.chatContext && this.chatContext.dynamic_loading) {
                console.log('Dynamic loading enabled, calling loadCompanyData()');
                this.loadCompanyData();
            } else {
                console.log('Dynamic loading disabled or no chatContext', {
                    chatContext: this.chatContext,
                    dynamic_loading: this.chatContext?.dynamic_loading
                });
            }

            // Set reference to sidebar chat container
            this.$nextTick(() => {
                this.sidebarChatContainer = document.querySelector('.chat-messages-container');
            });
        },
        updated() {
            this.scrollToBottom();
        },
        methods: {
            // Navigation
            getActiveNavSectionFromUrl() {
                const path = window.location.pathname;
                if (path.includes('/companies/')) {
                    return 'companies';
                } else if (path.includes('/people/')) {
                    return 'people';
                } else if (path.includes('/deals/')) {
                    return 'deals';
                } else {
                    return null; // No section active for base chat page
                }
            },

            setActiveNavSection(section) {
                this.activeNavSection = section;
                console.log('Active navigation section:', section);
                // You can add logic here to filter content or change views based on the section
            },

            // URL handling
            checkAndLoadConversationFromUrl() {
                // Check if there's a conversation parameter in the URL
                const urlParams = new URLSearchParams(window.location.search);
                const conversationId = urlParams.get('conversation');

                if (conversationId) {
                    console.log('Found conversation ID in URL:', conversationId);
                    // Wait a bit for conversations to be fetched, then load the conversation
                    setTimeout(() => {
                        this.loadConversation(conversationId);
                    }, 500);
                }
            },

            // Dynamic company data loading
            async loadCompanyData(page = null, search = null) {
                if (page === null) page = this.currentPage;
                if (search === null) search = this.searchTerm;

                this.isLoading = true;
                this.loadingError = null;

                try {
                    const params = {
                        page: page,
                        per_page: this.itemsPerPage
                    };

                    if (search && search.trim()) {
                        params.search = search.trim();
                    }

                    console.log('Making API call to /gaia_chat/api/company-data/ with params:', params);
                    const response = await axios.get('/gaia_chat/api/company-data/', { params });
                    const data = response.data;
                    console.log('API response:', data);

                    // Update chat context with new data
                    if (this.chatContext) {
                        // Use Vue.set to ensure reactivity
                        this.$set(this.chatContext, 'data', data.data);
                        this.$set(this.chatContext, 'title', data.title);
                        this.$set(this.chatContext, 'description', data.description);
                    }

                    // Update pagination info
                    this.totalCompanies = data.pagination.total_companies;
                    this.totalPages = data.pagination.total_pages;
                    this.currentPage = data.pagination.current_page;

                    console.log(`Loaded page ${this.currentPage}/${this.totalPages} with ${data.data.length} companies`);
                    console.log('Updated chatContext.data:', this.chatContext.data);
                    console.log('paginatedCompanies computed property:', this.paginatedCompanies);

                } catch (error) {
                    console.error('Error loading company data:', error);
                    this.loadingError = error.response?.data?.error || 'Failed to load company data';
                } finally {
                    this.isLoading = false;
                }
            },

            // Search companies
            async searchCompanies() {
                this.currentPage = 1; // Reset to first page when searching
                await this.loadCompanyData(1, this.searchTerm);
            },

            // Pagination methods
            async goToPage(page) {
                if (page >= 1 && page <= this.totalPages && page !== this.currentPage) {
                    await this.loadCompanyData(page);
                }
            },

            async goToFirstPage() {
                await this.goToPage(1);
            },

            async goToPreviousPage() {
                await this.goToPage(this.currentPage - 1);
            },

            async goToNextPage() {
                await this.goToPage(this.currentPage + 1);
            },

            async goToLastPage() {
                await this.goToPage(this.totalPages);
            },

            // API calls
            async fetchProviders() {
                try {
                    const response = await axios.get('/gaia_chat/api/llm/providers/');
                    this.providers = response.data.providers;

                    // Set default provider to MCP HTTP if available, otherwise LiteLLM, otherwise mock
                    const mcpHttpProvider = this.providers.find(p => p.id === 'mcp-http');
                    const litellmProvider = this.providers.find(p => p.id === 'litellm');
                    if (mcpHttpProvider) {
                        this.selectedProvider = 'mcp-http';
                    } else if (litellmProvider) {
                        this.selectedProvider = 'litellm';
                    } else {
                        this.selectedProvider = 'mock';
                    }

                    await this.onProviderChange();
                } catch (error) {
                    console.error('Error fetching providers:', error);
                }
            },

            async onProviderChange() {
                try {
                    const response = await axios.get('/gaia_chat/api/llm/models/', {
                        params: { provider: this.selectedProvider }
                    });
                    this.models = response.data.models;
                    if (this.models.length > 0) {
                        // Use the default model from the API response (from centralized config)
                        this.selectedModel = response.data.default_model;

                        // If default model is not in the list, fall back to first model
                        if (!this.models.find(m => m.id === this.selectedModel)) {
                            this.selectedModel = this.models[0].id;
                        }

                        // Automatically set the LLM when provider changes
                        await this.setLLM();
                    }
                } catch (error) {
                    console.error('Error fetching models:', error);
                    this.models = [];
                }
            },

            async setLLM() {
                this.isSettingLLM = true;
                try {
                    // Set the MCP protocol based on the selected provider
                    if (this.selectedProvider === 'mcp') {
                        this.mcpProtocol = 'SSE';
                    } else if (this.selectedProvider === 'mcp-http') {
                        this.mcpProtocol = 'HTTP';
                    } else {
                        this.mcpProtocol = '';
                    }

                    await axios.post('/gaia_chat/api/llm/set/', {
                        provider: this.selectedProvider,
                        model: this.selectedModel
                    });

                    // Refresh tools after LLM change
                    this.fetchMcpTools();
                } catch (error) {
                    console.error('Error setting LLM:', error);
                } finally {
                    this.isSettingLLM = false;
                }
            },

            async fetchConversations() {
                try {
                    const response = await axios.get('/gaia_chat/api/conversations/');
                    this.conversations = response.data.conversations;
                } catch (error) {
                    console.error('Error fetching conversations:', error);
                }
            },

            async createConversation() {
                this.isCreatingConversation = true;
                try {
                    console.log('Creating new conversation...');

                    // Use the custom title if provided, otherwise use a default title
                    const title = this.newConversationTitle.trim()
                        ? this.newConversationTitle.trim()
                        : `Conversation ${new Date().toLocaleString()}`;

                    console.log('Using title:', title);

                    // Prepare the request payload
                    const payload = { title: title };

                    // If we have context data, include it in the request
                    if (this.chatContext && this.chatContext.data && this.chatContext.data.length) {
                        console.log('Including context data in conversation creation...');
                        payload.context = this.chatContext;
                    }

                    const response = await axios.post('/gaia_chat/api/conversations/create/', payload);

                    console.log('Create conversation response:', response.data);

                    if (response.data.success) {
                        // Clear the title input
                        this.newConversationTitle = '';

                        // Refresh the conversation list
                        await this.fetchConversations();

                        // Get the newly created conversation ID
                        const newConversationId = response.data.conversation.id;
                        console.log('New conversation ID:', newConversationId);

                        // Load the new conversation (context will already be loaded by backend)
                        await this.loadConversation(newConversationId);
                        console.log('Loaded conversation:', this.activeConversation);
                    }
                } catch (error) {
                    console.error('Error creating conversation:', error);
                    if (error.response) {
                        console.error('Response status:', error.response.status);
                        console.error('Response data:', error.response.data);
                    }
                    this.error = "Error creating conversation: " + (error.response?.data?.error || error.message);
                } finally {
                    this.isCreatingConversation = false;
                }
            },

            async loadConversation(id) {
                this.isLoadingConversation = true;
                try {
                    const response = await axios.get(`/gaia_chat/api/conversations/${id}/`);
                    if (response.data.success) {
                        this.activeConversation = response.data.conversation;

                        // Make sure the conversation has a messages array
                        if (!this.activeConversation.messages) {
                            this.activeConversation.messages = [];
                        }

                        // Update the URL to include the conversation ID while preserving the current path
                        const currentPath = window.location.pathname;
                        const newUrl = `${currentPath}?conversation=${id}`;
                        window.history.pushState({}, '', newUrl);

                        this.$nextTick(() => {
                            this.scrollToBottom();
                        });
                    }
                } catch (error) {
                    console.error('Error loading conversation:', error);
                    this.error = "Error loading conversation: " + (error.response?.data?.error || error.message);
                } finally {
                    this.isLoadingConversation = false;
                }
            },

            isDirectToolCall(message) {
                // Check if the message is a direct tool call (simple tool name)
                const trimmed = message.trim();

                // List of known direct tool calls
                const directToolCalls = [
                    'long_task',
                    'echostring_table',
                    'firecrawl_scrape',
                    'firecrawl_scrape_text_only'
                ];

                return directToolCalls.includes(trimmed);
            },

            // Debug panel methods
            toggleDebugPanel() {
                this.debugPanelVisible = !this.debugPanelVisible;
                console.log('🐛 Debug panel toggled:', this.debugPanelVisible);

                // Load tools when opening debug panel and switching to tools mode
                if (this.debugPanelVisible && this.debugPaneMode === 'tools') {
                    this.fetchMcpTools();
                }
            },

            setDebugPaneMode(mode) {
                this.debugPaneMode = mode;
                console.log('🐛 Debug pane mode changed to:', mode);

                // Load tools when switching to tools mode
                if (mode === 'tools') {
                    this.fetchMcpTools();
                }

                // Load memory bank when switching to memory mode
                if (mode === 'memory') {
                    this.fetchMemoryBankContents();
                }

                // Load tools when switching to inspector mode
                if (mode === 'inspector') {
                    this.fetchMcpTools();
                }
            },

            // Enhanced call tracking methods
            startCallTracking(method, url, requestData) {
                const callId = Date.now() + Math.random();
                const timestamp = new Date().toISOString();
                const requestBody = JSON.stringify(requestData);
                const sentBytes = new Blob([requestBody]).size;

                const callEntry = {
                    id: callId,
                    timestamp,
                    startTime: performance.now(),
                    method,
                    url,
                    status: 'PENDING',
                    requestData: requestData,
                    requestBody: requestBody,
                    sentBytes: sentBytes,
                    responseData: null,
                    responseBody: null,
                    receivedBytes: null,
                    latency: null,
                    error: null,
                    completed: false
                };

                this.callDetails.push(callEntry);
                console.log('🔄 Started call tracking:', method, url, callId);

                // Keep only last 50 calls
                if (this.callDetails.length > 50) {
                    this.callDetails = this.callDetails.slice(-50);
                }

                return callId;
            },

            completeCallTracking(callId, responseData, responseStatus, error = null) {
                const callEntry = this.callDetails.find(c => c.id === callId);
                if (!callEntry) {
                    console.warn('🚫 Call entry not found for completion:', callId);
                    return;
                }

                const endTime = performance.now();
                const responseBody = responseData ? JSON.stringify(responseData) : null;

                callEntry.latency = Math.round(endTime - callEntry.startTime);
                callEntry.status = error ? 'ERROR' : responseStatus;
                callEntry.responseData = responseData;
                callEntry.responseBody = responseBody;
                callEntry.receivedBytes = responseBody ? new Blob([responseBody]).size : 0;
                callEntry.error = error;
                callEntry.completed = true;

                console.log('✅ Completed call tracking:', callId, callEntry.status, callEntry.latency + 'ms');
            },

            clearCallDetails() {
                this.callDetails = [];
                console.log('🧹 Cleared call details');
            },

            async fetchMcpTools() {
                if (this.mcpToolsLoading) return;

                this.mcpToolsLoading = true;
                try {
                    const response = await axios.get('/gaia_chat/api/mcp/tools/');
                    console.log('🔍 Raw tools response:', response.data);

                    let rawTools = response.data.tools || [];
                    this.currentProviderName = response.data.provider || 'Unknown';

                    // Check if tools is a JSON string that needs parsing
                    if (typeof rawTools === 'string') {
                        try {
                            rawTools = JSON.parse(rawTools);
                            console.log('🔍 Parsed tools from JSON string:', rawTools);
                        } catch (e) {
                            console.error('❌ Failed to parse tools JSON string:', e);
                            rawTools = [];
                        }
                    }

                    // Validate that all tools are proper objects
                    const validTools = rawTools.filter(tool => {
                        if (typeof tool !== 'object' || !tool.name) {
                            console.error('❌ Invalid tool format:', tool);
                            return false;
                        }
                        return true;
                    });

                    this.mcpTools = validTools;
                    console.log('🔧 Fetched MCP tools:', this.mcpTools.length, 'Provider:', this.currentProviderName);

                    if (validTools.length !== rawTools.length) {
                        console.warn(`⚠️ Filtered out ${rawTools.length - validTools.length} invalid tools`);
                    }

                } catch (error) {
                    console.error('Error fetching MCP tools:', error);
                    this.mcpTools = [];
                    this.currentProviderName = 'Error';
                } finally {
                    this.mcpToolsLoading = false;
                }
            },

            // Tool validation method
            validateToolCall(toolName, parameters) {
                const tool = this.mcpTools.find(t => t.name === toolName);

                if (!tool) {
                    const availableNames = this.mcpTools.map(t => t.name);
                    const suggestion = this.findClosestMatch(toolName, availableNames);
                    throw new Error(`Tool '${toolName}' not found. Available tools: ${availableNames.join(', ')}${suggestion ? `. Did you mean '${suggestion}'?` : ''}`);
                }

                // Validate required parameters
                const schema = tool.input_schema;
                if (schema && schema.required) {
                    const missingParams = schema.required.filter(param =>
                        parameters[param] === undefined || parameters[param] === null
                    );

                    if (missingParams.length > 0) {
                        throw new Error(`Missing required parameters for '${toolName}': ${missingParams.join(', ')}`);
                    }
                }

                return tool;
            },

            // Simple string similarity for suggestions
            findClosestMatch(target, options) {
                if (!options.length) return null;

                let bestMatch = null;
                let bestScore = 0;

                for (const option of options) {
                    const score = this.calculateSimilarity(target.toLowerCase(), option.toLowerCase());
                    if (score > bestScore && score > 0.5) {
                        bestScore = score;
                        bestMatch = option;
                    }
                }

                return bestMatch;
            },

            // Calculate string similarity (simple implementation)
            calculateSimilarity(str1, str2) {
                const longer = str1.length > str2.length ? str1 : str2;
                const shorter = str1.length > str2.length ? str2 : str1;

                if (longer.length === 0) return 1.0;

                const editDistance = this.levenshteinDistance(longer, shorter);
                return (longer.length - editDistance) / longer.length;
            },

            // Levenshtein distance calculation
            levenshteinDistance(str1, str2) {
                const matrix = [];

                for (let i = 0; i <= str2.length; i++) {
                    matrix[i] = [i];
                }

                for (let j = 0; j <= str1.length; j++) {
                    matrix[0][j] = j;
                }

                for (let i = 1; i <= str2.length; i++) {
                    for (let j = 1; j <= str1.length; j++) {
                        if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
                            matrix[i][j] = matrix[i - 1][j - 1];
                        } else {
                            matrix[i][j] = Math.min(
                                matrix[i - 1][j - 1] + 1,
                                matrix[i][j - 1] + 1,
                                matrix[i - 1][j] + 1
                            );
                        }
                    }
                }

                return matrix[str2.length][str1.length];
            },

            // Memory bank methods
            async fetchMemoryBankContents() {
                if (!this.activeConversation?.id) {
                    this.memoryBankError = 'No active conversation';
                    return;
                }

                if (this.memoryBankLoading) return;

                this.memoryBankLoading = true;
                this.memoryBankError = null;

                let callId = null;

                try {
                    const requestData = {
                        tool_name: 'elf_mem_list_keys',
                        parameters: {
                            bank: `convo/${this.activeConversation.id}`
                        }
                    };

                    // Validate tool call before making request
                    if (this.mcpTools.length > 0) {
                        try {
                            this.validateToolCall(requestData.tool_name, requestData.parameters);
                            console.log('✅ Tool validation passed for:', requestData.tool_name);
                        } catch (validationError) {
                            console.error('❌ Tool validation failed:', validationError.message);
                            this.memoryBankError = `Validation Error: ${validationError.message}`;
                            return;
                        }
                    }

                    // Start call tracking
                    callId = this.startCallTracking('POST', '/gaia_chat/api/mcp/call-tool/', requestData);

                    const response = await axios.post('/gaia_chat/api/mcp/call-tool/', requestData);

                    // Complete call tracking
                    this.completeCallTracking(callId, response.data, response.status);

                    if (response.data.success) {
                        // Parse the result from elf_mem_list_keys tool
                        let result = response.data.result;
                        console.log('🔍 Raw memory bank result:', result);

                        // Handle different result formats
                        let keys = [];
                        if (typeof result === 'string') {
                            // The result is a JSON string - parse it first
                            try {
                                const parsed = JSON.parse(result);
                                console.log('🔍 Parsed memory bank result:', parsed);
                                keys = parsed.keys || [];
                            } catch (e) {
                                console.error('❌ Failed to parse memory bank JSON:', e);
                                // If not JSON, split by lines as fallback
                                keys = result.split('\n').filter(line => line.trim());
                            }
                        } else if (result && result.keys) {
                            // If it's already an object format with keys array
                            keys = result.keys;
                        } else if (Array.isArray(result)) {
                            // If it's already an array
                            keys = result;
                        }

                        this.memoryBankFiles = keys.map(key => ({
                            key: typeof key === 'string' ? key : key.key || key,
                            showPreview: false,
                            content: null
                        }));

                        this.memoryBankLastUpdated = new Date().toISOString();
                        console.log('📁 Memory bank updated:', this.memoryBankFiles.length, 'files');
                        console.log('📁 Memory bank keys:', keys);
                    } else {
                        this.memoryBankError = response.data.error || 'Failed to fetch memory bank';
                        this.memoryBankFiles = [];
                    }

                } catch (error) {
                    console.error('Error fetching memory bank:', error);

                    // Log error to the error tracking system
                    this.logError(error, {
                        operation: 'fetchMemoryBankContents',
                        tool: 'elf_mem_list_keys',
                        bank: `convo/${this.activeConversation.id}`,
                        url: '/gaia_chat/api/mcp/call-tool/',
                        method: 'POST'
                    });

                    this.memoryBankError = error.response?.data?.error || error.message;
                    this.memoryBankFiles = [];

                    // Complete call tracking with error (only if callId exists)
                    if (callId) {
                        this.completeCallTracking(callId, null, error.response?.status || 'ERROR', error.message);
                    }
                } finally {
                    this.memoryBankLoading = false;
                }
            },

            async refreshMemoryBank() {
                await this.fetchMemoryBankContents();
            },

            async toggleFilePreview(fileIndex) {
                const file = this.memoryBankFiles[fileIndex];

                if (file.showPreview) {
                    // Hide preview
                    file.showPreview = false;
                    return;
                }

                // Show preview - fetch content if not already loaded
                if (!file.content) {
                    try {
                        const requestData = {
                            tool_name: 'elf_mem_get',
                            parameters: {
                                bank: `convo/${this.activeConversation.id}`,
                                key: file.key
                            }
                        };

                        const callId = this.startCallTracking('POST', '/gaia_chat/api/mcp/call-tool/', requestData);
                        const response = await axios.post('/gaia_chat/api/mcp/call-tool/', requestData);
                        this.completeCallTracking(callId, response.data, response.status);

                        if (response.data.success) {
                            file.content = response.data.result;
                        } else {
                            file.content = `Error loading content: ${response.data.error}`;
                        }
                    } catch (error) {
                        // Log memory content loading errors
                        this.logError(error, {
                            operation: 'loadMemoryContent',
                            tool: 'elf_mem_get',
                            bank: `convo/${this.activeConversation.id}`,
                            key: file.key,
                            url: '/gaia_chat/api/mcp/call-tool/',
                            method: 'POST'
                        });

                        file.content = `Error loading content: ${error.message}`;
                    }
                }

                file.showPreview = true;
                this.$forceUpdate(); // Ensure Vue updates the display
            },

            // Tool Inspector methods
            toggleToolExpansion(index) {
                const tool = this.filteredTools[index];
                tool.expanded = !tool.expanded;
                this.$forceUpdate();
            },

            generateToolExample(tool) {
                const example = {
                    tool_name: tool.name,
                    parameters: {}
                };

                // Generate example parameters based on schema
                if (tool.input_schema && tool.input_schema.properties) {
                    for (const [paramName, paramSchema] of Object.entries(tool.input_schema.properties)) {
                        if (paramSchema.type === 'string') {
                            example.parameters[paramName] = `"example_${paramName}"`;
                        } else if (paramSchema.type === 'number') {
                            example.parameters[paramName] = 123;
                        } else if (paramSchema.type === 'boolean') {
                            example.parameters[paramName] = true;
                        } else if (paramSchema.type === 'array') {
                            example.parameters[paramName] = [];
                        } else if (paramSchema.type === 'object') {
                            example.parameters[paramName] = {};
                        } else {
                            example.parameters[paramName] = null;
                        }
                    }
                } else {
                    // For tools without detailed schema, provide smart defaults based on tool name
                    if (tool.name.includes('elf_mem')) {
                        example.parameters.bank = "example_bank";
                        if (tool.name.includes('get') || tool.name.includes('store') || tool.name.includes('delete')) {
                            example.parameters.key = "example_key";
                        }
                        if (tool.name.includes('store')) {
                            example.parameters.value = {"example": "data"};
                        }
                        if (tool.name.includes('list_keys')) {
                            example.parameters.key_prefix = null;
                        }
                    } else {
                        // Generic example for unknown tools
                        example.parameters.example_param = "example_value";
                    }
                }

                return JSON.stringify(example, null, 2);
            },

            async testTool(tool) {
                try {
                    // Generate basic test parameters
                    const testParams = {};
                    if (tool.input_schema && tool.input_schema.properties) {
                        for (const [paramName, paramSchema] of Object.entries(tool.input_schema.properties)) {
                            if (tool.input_schema.required && tool.input_schema.required.includes(paramName)) {
                                if (paramName === 'bank' && this.activeConversation) {
                                    testParams[paramName] = `convo/${this.activeConversation.id}`;
                                } else if (paramSchema.type === 'string') {
                                    testParams[paramName] = 'test_value';
                                } else if (paramSchema.type === 'number') {
                                    testParams[paramName] = 1;
                                } else {
                                    testParams[paramName] = null;
                                }
                            }
                        }
                    }

                    const requestData = {
                        tool_name: tool.name,
                        parameters: testParams
                    };

                    const callId = this.startCallTracking('POST', '/gaia_chat/api/mcp/call-tool/', requestData);
                    const response = await axios.post('/gaia_chat/api/mcp/call-tool/', requestData);
                    this.completeCallTracking(callId, response.data, response.status);

                    console.log(`✅ Tool test successful for ${tool.name}:`, response.data);
                    alert(`Tool test successful! Check Call Details for results.`);

                } catch (error) {
                    console.error(`❌ Tool test failed for ${tool.name}:`, error);

                    // Log tool test errors
                    this.logError(error, {
                        operation: 'tool_test',
                        tool_name: tool.name,
                        parameters: testParams,
                        url: '/gaia_chat/api/mcp/call-tool/',
                        method: 'POST'
                    });

                    alert(`Tool test failed: ${error.response?.data?.error || error.message}`);
                }
            },

            copyToolCall(tool, event) {
                const example = this.generateToolExample(tool);
                navigator.clipboard.writeText(example).then(() => {
                    console.log(`📋 Copied tool call template for ${tool.name}`);
                    // Show temporary feedback
                    if (event && event.target) {
                        const originalText = event.target.innerHTML;
                        event.target.innerHTML = '<i class="fas fa-check"></i>';
                        setTimeout(() => {
                            event.target.innerHTML = originalText;
                        }, 1000);
                    }
                }).catch(err => {
                    console.error('Failed to copy:', err);
                });
            },

            // Error tracking methods
            logError(error, context = {}) {
                // Enhanced error logging with comprehensive HTTP details
                const errorEntry = {
                    id: Date.now() + Math.random(),
                    timestamp: new Date().toISOString(),
                    message: error.message || String(error),
                    stack: error.stack || null,
                    context: context,
                    type: error.name || 'Error',
                    
                    // HTTP Request Details
                    url: error.config?.url || context.url || null,
                    method: error.config?.method?.toUpperCase() || context.method || null,
                    baseURL: error.config?.baseURL || null,
                    timeout: error.config?.timeout || null,
                    
                    // HTTP Response Details
                    status: error.response?.status || context.status || null,
                    statusText: error.response?.statusText || null,
                    responseHeaders: error.response?.headers || null,
                    responseData: error.response?.data || context.responseData || null,
                    
                    // HTTP Request Details
                    requestData: error.config?.data || context.requestData || null,
                    requestHeaders: error.config?.headers || null,
                    params: error.config?.params || null,
                    
                    // Network/Connection Details
                    code: error.code || null, // ECONNABORTED, ENOTFOUND, etc.
                    errno: error.errno || null,
                    syscall: error.syscall || null,
                    hostname: error.hostname || null,
                    port: error.port || null,
                    address: error.address || null,
                    
                    // Browser/Environment Details
                    userAgent: navigator.userAgent,
                    url_location: window.location.href,
                    referrer: document.referrer || null,
                    connectionType: navigator.connection?.effectiveType || null,
                    onLine: navigator.onLine,
                    
                    // Additional Axios Error Details
                    isAxiosError: error.isAxiosError || false,
                    toJSON: error.toJSON ? error.toJSON() : null,
                    
                    // Timing Information
                    request_timestamp: error.config?.metadata?.startTime || Date.now(),
                    
                    // Enhanced 504/Timeout Information
                    isTimeout: error.code === 'ECONNABORTED' || error.message?.includes('timeout') || 
                              (error.response?.status === 504) || (error.response?.status === 408),
                    timeoutType: error.code === 'ECONNABORTED' ? 'client_timeout' :
                                error.response?.status === 504 ? 'gateway_timeout' :
                                error.response?.status === 408 ? 'request_timeout' : null,
                    
                    // Server Error Context for 5xx errors
                    isServerError: error.response?.status >= 500,
                    serverErrorType: error.response?.status === 500 ? 'internal_server_error' :
                                   error.response?.status === 502 ? 'bad_gateway' :
                                   error.response?.status === 503 ? 'service_unavailable' :
                                   error.response?.status === 504 ? 'gateway_timeout' :
                                   error.response?.status >= 500 ? 'server_error' : null
                };

                // Add to error log
                this.errorLog.unshift(errorEntry);

                // Keep only the most recent errors
                if (this.errorLog.length > this.maxErrors) {
                    this.errorLog = this.errorLog.slice(0, this.maxErrors);
                }

                // Also log to console for immediate debugging
                console.error('🚨 Error logged:', errorEntry);

                return errorEntry;
            },

            clearErrorLog() {
                this.errorLog = [];
                console.log('🧹 Error log cleared');
            },

            resetAllState() {
                console.log('🔄 RESETTING ALL STATE...');

                // Clear all debug data
                this.debugLogs = [];
                this.callDetails = [];
                this.errorLog = [];
                this.mcpTools = [];
                this.memoryBankFiles = [];

                // Reset loading states
                this.mcpToolsLoading = false;
                this.memoryBankLoading = false;
                this.loading = false;
                this.isStreamingMessage = false;

                // Clear errors
                this.error = null;
                this.errorMessage = null;
                this.memoryBankError = null;

                // Reset timestamps
                this.memoryBankLastUpdated = null;

                // Clear current provider
                this.currentProviderName = 'Unknown';

                // Stop waiting animation
                this.stopWaitingAnimation();

                // Re-initialize everything
                this.fetchProviders();
                this.fetchMcpTools();
                this.fetchMemoryBankContents();

                console.log('✅ All state reset and re-initialized');
                alert('Debug state has been reset and re-initialized!');
            },

            // Waiting animation methods
            startWaitingAnimation() {
                if (this.waitingAnimation.active) return;

                this.waitingAnimation.active = true;
                this.waitingAnimation.startTime = Date.now();
                this.waitingAnimation.phraseIndex = Math.floor(Math.random() * this.waitingAnimation.phrases.length);
                this.waitingAnimation.currentPhrase = this.waitingAnimation.phrases[this.waitingAnimation.phraseIndex];
                this.waitingAnimation.animationType = Math.floor(Math.random() * 3); // 0, 1, or 2

                this.animateWaitingText();
            },

            stopWaitingAnimation() {
                this.waitingAnimation.active = false;
                if (this.waitingAnimation.animationFrame) {
                    cancelAnimationFrame(this.waitingAnimation.animationFrame);
                    this.waitingAnimation.animationFrame = null;
                }
            },

            animateWaitingText() {
                if (!this.waitingAnimation.active) return;

                const elapsed = (Date.now() - this.waitingAnimation.startTime) / 1000;

                // Change phrase every 3 seconds
                if (elapsed > 3 && elapsed % 3 < 0.1) {
                    this.waitingAnimation.phraseIndex = (this.waitingAnimation.phraseIndex + 1) % this.waitingAnimation.phrases.length;
                    this.waitingAnimation.currentPhrase = this.waitingAnimation.phrases[this.waitingAnimation.phraseIndex];
                }

                this.waitingAnimation.animationFrame = requestAnimationFrame(() => this.animateWaitingText());
            },

            getWaitingAnimationStyle() {
                if (!this.waitingAnimation.active) return {};

                const elapsed = (Date.now() - this.waitingAnimation.startTime) / 1000;
                const type = this.waitingAnimation.animationType;

                if (type === 0) {
                    // Pulse Animation - High contrast cyan/magenta
                    const pulse = Math.sin(elapsed * 2) * 0.5 + 0.5;
                    const color = pulse > 0.5 ? '#00ffff' : '#ff00ff';
                    return {
                        color: color,
                        textShadow: `0 0 ${8 + pulse * 12}px ${color}, 0 0 ${4 + pulse * 6}px ${color}`,
                        transform: `scale(${0.98 + pulse * 0.08})`,
                        fontWeight: 'bold'
                    };
                } else if (type === 1) {
                    // Matrix Animation - Green digital rain effect
                    const flicker = Math.random() > 0.8 ? 0.3 : 1;
                    const intensity = Math.sin(elapsed * 3) * 0.3 + 0.7;
                    return {
                        color: `hsl(120, 100%, ${60 + intensity * 20}%)`,
                        textShadow: `0 0 10px #00ff00, 0 0 20px #00ff00, 0 0 30px #00ff00`,
                        opacity: flicker,
                        fontFamily: 'Courier New, monospace',
                        letterSpacing: '2px',
                        fontWeight: 'bold'
                    };
                } else {
                    // Neural Animation - Electric blue with sparks
                    const spark = Math.sin(elapsed * 4) * 0.5 + 0.5;
                    const lightning = Math.random() > 0.9 ? 2 : 1;
                    return {
                        color: '#4da6ff',
                        textShadow: `0 0 ${6 * lightning}px #0080ff, 0 0 ${12 * lightning}px #0080ff, 0 0 ${18 * lightning}px #ffffff`,
                        transform: `translateX(${Math.sin(elapsed * 6) * 2}px) scale(${0.98 + spark * 0.06})`,
                        fontWeight: 'bold',
                        filter: `brightness(${1 + spark * 0.5})`
                    };
                }
            },

            formatTimestamp(timestamp) {
                try {
                    const date = new Date(timestamp);
                    return date.toLocaleString();
                } catch (e) {
                    return timestamp;
                }
            },

            setupGlobalErrorHandlers() {
                // Global JavaScript error handler
                window.addEventListener('error', (event) => {
                    this.logError(event.error || new Error(event.message), {
                        operation: 'global_js_error',
                        filename: event.filename,
                        lineno: event.lineno,
                        colno: event.colno,
                        source: 'window.error'
                    });
                });

                // Global unhandled promise rejection handler
                window.addEventListener('unhandledrejection', (event) => {
                    this.logError(event.reason || new Error('Unhandled promise rejection'), {
                        operation: 'unhandled_promise_rejection',
                        source: 'window.unhandledrejection'
                    });
                });

                // Vue.js error handler
                if (this.$options && this.$options.errorHandler) {
                    const originalErrorHandler = this.$options.errorHandler;
                    this.$options.errorHandler = (err, vm, info) => {
                        this.logError(err, {
                            operation: 'vue_error',
                            component: vm.$options.name || 'Unknown',
                            info: info,
                            source: 'vue.errorHandler'
                        });
                        if (originalErrorHandler) {
                            originalErrorHandler(err, vm, info);
                        }
                    };
                }

                // Axios response interceptor for HTTP errors
                if (window.axios && window.axios.interceptors) {
                    window.axios.interceptors.response.use(
                        (response) => response,
                        (error) => {
                            // Log all HTTP errors to the error panel
                            this.logError(error, {
                                operation: 'http_request',
                                url: error.config?.url,
                                method: error.config?.method?.toUpperCase(),
                                status: error.response?.status,
                                statusText: error.response?.statusText,
                                responseData: error.response?.data,
                                requestData: error.config?.data,
                                source: 'axios.interceptor'
                            });
                            return Promise.reject(error);
                        }
                    );
                }

                console.log('🛡️ Global error handlers set up');
            },

            addDebugLog(type, direction, data, url = '', responseCode = null, latency = null, sentBytes = null, receivedBytes = null) {
                const timestamp = new Date().toISOString();
                const logEntry = {
                    id: Date.now() + Math.random(),
                    timestamp,
                    type,
                    direction,
                    url,
                    responseCode,
                    latency,
                    sentBytes,
                    receivedBytes,
                    data: typeof data === 'string' ? data : JSON.stringify(data, null, 2)
                };

                this.debugLogs.push(logEntry);
                console.log('🐛 Debug log added:', type, direction, 'Total logs:', this.debugLogs.length);

                // Keep only last 100 logs (remove from beginning)
                if (this.debugLogs.length > 100) {
                    this.debugLogs = this.debugLogs.slice(-100);
                }
            },

            clearDebugLogs() {
                this.debugLogs = [];
                this.callDetails = [];
                console.log('🧹 Cleared debug logs and call details');
            },

            formatTimestamp(timestamp) {
                return new Date(timestamp).toLocaleTimeString();
            },

            formatBytes(bytes) {
                if (!bytes) return '-';
                if (bytes < 1024) return bytes + ' B';
                if (bytes < 1024 * 1024) return Math.round(bytes / 1024) + ' KB';
                return Math.round(bytes / (1024 * 1024)) + ' MB';
            },

            formatLatency(latency) {
                if (!latency) return '-';
                return latency + ' ms';
            },

            async sendMessage() {
                if (!this.userInput.trim()) return;

                const message = this.userInput;
                this.userInput = '';

                // If there's no active conversation, create one automatically
                if (!this.activeConversation) {
                    console.log('No active conversation, creating one automatically...');
                    try {
                        await this.createConversation();
                        // createConversation will set activeConversation, so we can proceed
                    } catch (error) {
                        console.error('Failed to create conversation automatically:', error);
                        this.error = "Failed to create conversation. Please try creating one manually.";
                        return;
                    }
                }

                // Check if this is a direct tool call for MCP providers
                const isDirectToolCall = this.isDirectToolCall(message);
                const isMcpProvider = this.selectedProvider === 'mcp' || this.selectedProvider === 'mcp-http';

                console.log(`🔍 PROVIDER CHECK: selectedProvider = "${this.selectedProvider}"`);
                console.log(`🔧 TOOL CHECK: isDirectToolCall = ${isDirectToolCall}`);

                if (isMcpProvider && isDirectToolCall) {
                    console.log('🚀 USING STREAMING ENDPOINT for MCP provider with direct tool call');
                    await this.sendStreamingMessage(message);
                } else {
                    console.log('📝 USING REGULAR ENDPOINT for regular message or non-MCP provider');
                    await this.sendRegularMessage(message);
                }
            },

            async sendRegularMessage(message) {
                this.isSendingMessage = true;
                this.startWaitingAnimation();

                try {
                    // Add the user message to the conversation
                    this.activeConversation.messages.push({
                        role: 'user',
                        content: message,
                        timestamp: new Date().toISOString()
                    });

                    // Prepare the messages to send to the API
                    let messagesToSend = [...this.activeConversation.messages];

                    // Send the messages to the API
                    const requestData = {
                        conversation_id: this.activeConversation.id,
                        messages: messagesToSend,
                        provider: this.selectedProvider
                    };

                    // Start call tracking BEFORE making the request
                    const callId = this.startCallTracking('POST', '/gaia_chat/api/chat/', requestData);

                    // Log outgoing request (for API pane)
                    this.addDebugLog('HTTP', 'REQUEST', requestData, '/gaia_chat/api/chat/', null, null, new Blob([JSON.stringify(requestData)]).size);

                    let response;
                    try {
                        response = await axios.post('/gaia_chat/api/chat/', requestData);

                        // Complete call tracking with success
                        this.completeCallTracking(callId, response.data, response.status);

                        // Log incoming response (for API pane)
                        const responseBody = JSON.stringify(response.data);
                        const receivedBytes = new Blob([responseBody]).size;
                        const callEntry = this.callDetails.find(c => c.id === callId);
                        const latency = callEntry ? callEntry.latency : null;
                        this.addDebugLog('HTTP', 'RESPONSE', response.data, '/gaia_chat/api/chat/', response.status, latency, null, receivedBytes);

                    } catch (error) {
                        // Log error to error tracking system
                        this.logError(error, {
                            operation: 'sendMessage',
                            url: '/gaia_chat/api/chat/',
                            method: 'POST'
                        });

                        // Complete call tracking with error
                        this.completeCallTracking(callId, null, error.response?.status || 'ERROR', error.message);
                        throw error; // Re-throw to be handled by outer catch
                    }

                    // Add the assistant's response to the conversation
                    this.activeConversation.messages.push({
                        role: 'assistant',
                        content: response.data.message,
                        timestamp: new Date().toISOString()
                    });

                    // Save the conversation using the new method
                    await this.updateConversation(this.activeConversation.id, this.activeConversation.messages);

                    // Auto-refresh memory bank if in memory mode
                    if (this.debugPaneMode === 'memory') {
                        setTimeout(() => this.fetchMemoryBankContents(), 1000);
                    }

                    this.scrollToBottom();
                } catch (error) {
                    console.error('Error sending message:', error);

                    // Log error to error tracking system
                    this.logError(error, {
                        operation: 'sendMessage_outer',
                        url: '/gaia_chat/api/chat/',
                        method: 'POST'
                    });

                    this.error = "Error sending message: " + (error.response?.data?.error || error.message);
                } finally {
                    this.isSendingMessage = false;
                    this.stopWaitingAnimation();
                }
            },

            async sendStreamingMessage(message) {
                this.isStreamingMessage = true;

                // Reset streaming state
                this.streamingProgress = [];
                this.streamingInfo = [];
                this.toolResult = null;

                try {
                    // Add the user message to the conversation
                    this.activeConversation.messages.push({
                        role: 'user',
                        content: message,
                        timestamp: new Date().toISOString()
                    });

                    // Add a placeholder for the assistant's response
                    this.activeConversation.messages.push({
                        role: 'assistant',
                        content: '',
                        timestamp: new Date().toISOString(),
                        streaming: true
                    });

                    const assistantMessageIndex = this.activeConversation.messages.length - 1;

                    // Use the send_message_stream endpoint for real streaming
                    const requestData = {
                        message: message,
                        conversation_id: this.activeConversation.id,
                        provider: this.selectedProvider
                    };

                    // Start call tracking BEFORE making the request
                    const callId = this.startCallTracking('POST (STREAM)', '/gaia_chat/api/messages/send-stream/', requestData);

                    // Log outgoing streaming request (for API pane)
                    this.addDebugLog('HTTP', 'STREAM_REQUEST', requestData, '/gaia_chat/api/messages/send-stream/', null, null, new Blob([JSON.stringify(requestData)]).size);

                    const response = await fetch('/gaia_chat/api/messages/send-stream/', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRFToken': this.getCsrfToken()
                        },
                        body: JSON.stringify(requestData)
                    });

                    if (!response.ok) {
                        // Complete call tracking with error
                        this.completeCallTracking(callId, null, response.status, `HTTP error! status: ${response.status}`);
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }

                    // Update call tracking with initial response status
                    const responseTime = performance.now();
                    const initialLatency = Math.round(responseTime - (this.callDetails.find(c => c.id === callId)?.startTime || responseTime));

                    // For streaming, we'll update the call tracking as we receive data

                    const reader = response.body.getReader();
                    const decoder = new TextDecoder();
                    let totalReceivedBytes = 0;

                    // Log initial response status
                    this.addDebugLog('HTTP', 'STREAM_RESPONSE', `Status: ${response.status}`, '/gaia_chat/api/messages/send-stream/', response.status, initialLatency);

                    while (true) {
                        const { done, value } = await reader.read();
                        if (done) break;

                        const chunk = decoder.decode(value);
                        totalReceivedBytes += value.length;
                        const lines = chunk.split('\n');

                        for (const line of lines) {
                            if (line.startsWith('data: ')) {
                                try {
                                    const data = JSON.parse(line.slice(6));
                                    const currentTime = performance.now();
                                    const streamLatency = Math.round(currentTime - startTime);

                                    // Log streaming data
                                    this.addDebugLog('STREAM', 'RESPONSE', data, '/gaia_chat/api/messages/send-stream/', null, streamLatency, null, totalReceivedBytes);

                                    // Handle all streaming events through the unified handler
                                    this.handleStreamingData(data, assistantMessageIndex);

                                    // Handle completion events
                                    if (data.type === 'complete' || data.type === 'final') {
                                        this.isStreamingMessage = false;
                                        this.activeConversation.messages[assistantMessageIndex].streaming = false;

                                        // Complete call tracking for streaming
                                        this.completeCallTracking(callId, {
                                            type: 'streaming_complete',
                                            total_bytes: totalReceivedBytes,
                                            final_data: data
                                        }, response.status);

                                        // Save the conversation
                                        await this.updateConversation(this.activeConversation.id, this.activeConversation.messages);
                                        return;
                                    }
                                } catch (e) {
                                    console.error('Error parsing streaming data:', e, line);
                                }
                            }
                        }
                    }

                } catch (error) {
                    console.error('Error sending streaming message:', error);
                    this.error = "Error sending streaming message: " + error.message;
                    this.isStreamingMessage = false;
                    if (this.activeConversation.messages.length > 0) {
                        this.activeConversation.messages[this.activeConversation.messages.length - 1].streaming = false;
                    }
                }
            },

            async handleStreamingData(data, assistantMessageIndex) {
                // 🎯 CONSOLE LOGGING: Log all streaming data to browser console
                console.log('📊 STREAMING DATA:', data.type, data);

                switch (data.type) {
                    case 'start':
                        console.log('🚀 TASK STARTED:', data.content);
                        this.activeConversation.messages[assistantMessageIndex].content = '🚀 ' + data.content;
                        break;

                    case 'progress':
                        // Add progress update to the list
                        this.streamingProgress.push(data);

                        // 🎯 CONSOLE LOGGING: Use the exact console message from backend (same format as chat_term.py)
                        if (data.console_message) {
                            console.log(data.console_message);
                        } else {
                            // Fallback to building the message (for backward compatibility)
                            let progressText;
                            if (data.total && data.bar && data.percentage !== null) {
                                // Progress with visual bar
                                progressText = `📊 [${data.bar}] ${data.percentage.toFixed(1)}% ${data.progress}/${data.total}`;
                            } else {
                                // Progress without total (step-based)
                                progressText = `📊 Step ${data.progress}`;
                            }
                            const progressMessage = data.message ? ` – ${data.message}` : '';
                            console.log(progressText + progressMessage);
                        }

                        // Build content with all progress updates
                        let content = '🚀 Task in progress...\n\n';
                        this.streamingProgress.forEach(p => {
                            let pText;
                            if (p.total && p.bar && p.percentage !== null) {
                                pText = `📊 [${p.bar}] ${p.percentage.toFixed(1)}% ${p.progress}/${p.total}`;
                            } else {
                                pText = `📊 Step ${p.progress}`;
                            }
                            const pMessage = p.message ? ` – ${p.message}` : '';
                            content += pText + pMessage + '\n';
                        });

                        this.activeConversation.messages[assistantMessageIndex].content = content;
                        break;

                    case 'info':
                        // Add info message to the list
                        this.streamingInfo.push(data);

                        // Update the current message to show info
                        let currentContent = this.activeConversation.messages[assistantMessageIndex].content;
                        if (!currentContent.includes(data.content)) {
                            currentContent += '\n' + data.content;
                            this.activeConversation.messages[assistantMessageIndex].content = currentContent;
                        }
                        break;

                    case 'log':
                        // Handle log messages from FastMCP
                        this.streamingInfo.push(data);

                        // Display log message with emoji
                        const logMessage = `${data.emoji} [${data.level}] ${data.logger}: ${data.message}`;
                        let logContent = this.activeConversation.messages[assistantMessageIndex].content;
                        logContent += '\n' + logMessage;
                        this.activeConversation.messages[assistantMessageIndex].content = logContent;
                        break;

                    case 'summary':
                        // Handle progress summary from FastMCP
                        const summaryMessage = `\n${data.content}\n📊 Progress msgs: ${data.progress_count}   ℹ️ Info msgs: ${data.info_count}`;
                        let summaryContent = this.activeConversation.messages[assistantMessageIndex].content;
                        summaryContent += summaryMessage;
                        this.activeConversation.messages[assistantMessageIndex].content = summaryContent;
                        break;

                    case 'result':
                        // Handle tool result from FastMCP - store for final processing
                        this.toolResult = data.content;
                        break;

                    case 'final':
                        // Update with final result
                        let finalContent = '';

                        // Add progress summary if any
                        if (this.streamingProgress.length > 0) {
                            finalContent += '✅ Task completed successfully!\n\n';
                            finalContent += `📊 Progress updates: ${this.streamingProgress.length}\n`;
                            finalContent += `ℹ️ Info messages: ${this.streamingInfo.length}\n\n`;
                        }

                        finalContent += data.content;

                        this.activeConversation.messages[assistantMessageIndex].content = finalContent;
                        this.activeConversation.messages[assistantMessageIndex].streaming = false;
                        break;

                    case 'complete':
                        // Task completed
                        this.activeConversation.messages[assistantMessageIndex].streaming = false;
                        break;

                    case 'error':
                        this.activeConversation.messages[assistantMessageIndex].content = `❌ Error: ${data.content}`;
                        this.activeConversation.messages[assistantMessageIndex].streaming = false;
                        this.error = data.content;
                        break;
                }

                this.scrollToBottom();
            },

            // Utility methods
            getCsrfToken() {
                const cookies = document.cookie.split(';');
                for (let cookie of cookies) {
                    const [name, value] = cookie.trim().split('=');
                    if (name === 'csrftoken') {
                        return value;
                    }
                }
                return '';
            },

            formatDate(dateString) {
                if (!dateString) return '';
                const date = new Date(dateString);
                return date.toLocaleString();
            },

            formatRole(role) {
                if (role === 'user') return this.djangoData.username || 'You';
                if (role === 'assistant') return 'AgFunder Gaia';
                if (role === 'system') return 'AgFunder Gaia';
                return role;
            },


    
            isJsonString(str) {
                try {
                    JSON.parse(str);
                } catch (e) {
                    return false;
                }
                return true;
            },

renderPrettyRecord(obj, indent = 0) {
    if (obj === null) return 'null';
    if (typeof obj !== 'object') return String(obj);

    const pad = '  '.repeat(indent);

    return "<PRE>"+Object.entries(obj).map(([key, value]) => {
        const val = typeof value === 'object'
            ? '\n' + this.renderPrettyRecord(value, indent + 1)
            : String(value);
        return `${pad}${key}: ${val}`;
    }).join('\n') + "</PRE>";
},



            renderJson( json_str ){
                // simple JSON syntax highlighting with colors
                return json_str
                    // Highlight property names (keys)
                    .replace(/("([^"\\]|\\.)*")\s*:/g, '<span class="json-key">$1</span>:')
                    // Highlight string values
                    .replace(/:\s*("([^"\\]|\\.)*")/g, ': <span class="json-string">$1</span>')
            },
            defaultRenderContent( content ){
                 str_type =  `Type: ${typeof content} `;
                 str_typesub =  ""

                // if null/empty return blank
                if (!content) return str_type + '';

                // if string:
                if (typeof content === 'string') {


                    // if svg?
                    const svgMatch = content.match(/<svg[\s\S]*?<\/svg>/s);

                    if (svgMatch) {
                        let svgContent = svgMatch[0];
                        svgContent = svgContent.replace(/\\"/g, '"');
                        svgContent = svgContent.replace(/\\n/g, '\n');

                        // Create a container for the SVG with proper styling
                        const svgContainer = `
                            <div class="svg-content" style="
                                margin: 10px 0;
                                padding: 15px;
                                border: 1px solid #e0e0e0;
                                border-radius: 8px;
                                background: #fafafa;
                                text-align: center;
                                overflow: auto;
                            ">
                                <div class="svg-title" style="
                                    font-size: 14px;
                                    color: #666;
                                    margin-bottom: 10px;
                                    font-weight: bold;
                                ">SVG Chart</div>
                                <div class="svg-wrapper" style="
                                    max-width: 100%;
                                    overflow: auto;
                                ">

<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
${svgContent}

</div>
                            </div>`;

                        return svgContainer;
                    }

                    if (this.isJsonString(content)) {
                        inside = JSON.parse(content);
                        if (Array.isArray(inside) && inside.length > 0 && 'tool_call_id' in inside[0]) {

    let html = '';
    for (const item of inside) {
        if ('tool_call_id' in item) {
            html +=  this.renderPrettyRecord( item.content ); // str_type + " subtype: json.TOOL " + String( item.content ) + "<br/>";
        }
    }
    return html;

                        }
                        return str_type + " subtype: json " + this.defaultRenderContent(inside);
                    }
                    return str_type + " subtype: markdown "+  marked.parse(content);
                }
                
                // if array, render each as default
                if (Array.isArray(content)) {
                    return str_type + content.map(c => this.defaultRenderContent(c)).join(' <br> ');
                }   

                // if numeric
                if (typeof content === 'number') {
                    return str_type + content.toString();
                }

                // if boolean
                if (typeof content === 'boolean') {
                    return str_type + content.toString();
                }

                // if object:
                if (typeof content === 'object') {
                    return str_type + JSON.stringify(content);

                    // if has type and items, assume list
                    if (content.type === 'list' && Array.isArray(content.items)) {
                        return this.renderListNode(content);
                    }
                }
            },




            /* public formatter you call when displaying a message */
            formatMessage (content, role = null) {
                console.log('///////////////////////// Formatting message using the Composite pattern....')
                const json = this.extractJsonBlock(content);

                /* 1️⃣ JSON-described composites */
                if (json) {
                    const node      = this.buildNode(json);
                    const rendered  = node.render();
                    const remaining = content.replace(json.__raw, '').trim();
                    return rendered + (remaining ? this.plainTextToHtml(remaining) : '');
                }

                /* 2️⃣ tool-call results (only for assistant messages) */
                if (role === 'assistant' && this.hasToolCallResults(content)) {
                    return this.defaultRenderContent(content);
                    // return this.formatToolCallResults(content);
                }

                /* 3️⃣ fallback: markdown rendering */
                return marked.parse(  String(content)) ;

            },

            /* ---------- helper functions now methods ---------- */
            escapeHtml (str)       { return ChatHelpers.escapeHtml(str); },
            plainTextToHtml (txt)  { return ChatHelpers.plainTextToHtml(txt); },

            extractJsonBlock (text) {
                const match = text.match(/\{[^]*?"type"\s*:\s*"[a-z]+"[^]*?\}/);
                if (!match) return null;
                try {
                    const obj = JSON.parse(match[0]);
                    obj.__raw = match[0];
                return obj;
                } catch (_) { return null; }
            },

            buildNode (data) {
                if (typeof data === 'string') return new TextNode(data);
                if (typeof data === 'number') return new TextNode(data.toString());
                if (typeof data === 'boolean') return new TextNode(data.toString());
                if (data === null || data === undefined) return new TextNode('');

                // Handle objects
                if (typeof data === 'object') {
                    // Check if it has a type property for structured data
                    if (data.type) {
                        switch (data.type) {
                        case 'list': {
                            const list = new ListNode();
                            data.items.forEach(item => list.add(this.buildNode(item)));
                            return list;
                        }
                        case 'table': {
                            const tbl = new TableNode(data);
                            data.rows.forEach(r => tbl.addRow(...r.map(cell => this.buildNode(cell))));
                            return tbl;
                        }
                        default:
                            throw new Error(`Unknown node type: ${data.type}`);
                        }
                    } else {
                        // Handle plain objects by converting to JSON string
                        return new TextNode(JSON.stringify(data, null, 2));
                    }
                }

                // Fallback for any other data types
                return new TextNode(String(data));
            },

            

            /*
            formatMessage(content) {
                // Check if the content contains table data in JSON format
                if (this.isTableData(content)) {
                    return this.formatTableData(content);
                }

                // Check if the content contains tool call results and format them
                if (this.hasToolCallResults(content)) {
                    return this.formatToolCallResults(content);
                }

                // Use marked to render markdown for regular content
                return this.defaultRenderContent(content);
            },

            isTableData(content) {
                // Check if content contains JSON table data
                try {
                    // Look for table data patterns in the content
                    const jsonMatch = content.match(/\{[^}]*"type"\s*:\s*"table"[^}]*\}/);
                    if (jsonMatch) {
                        const tableData = JSON.parse(jsonMatch[0]);
                        return tableData.type === 'table' && tableData.columns && tableData.rows;
                    }
                    return false;
                } catch (e) {
                    return false;
                }
            },

            formatTableData(content) {
                try {
                    // Extract JSON table data from the content
                    const jsonMatch = content.match(/\{[^}]*"type"\s*:\s*"table"[^}]*\}/);
                    if (!jsonMatch) {
                        return this.defaultRenderContent(content);
                    }

                    const tableData = JSON.parse(jsonMatch[0]);

                    // Build HTML table
                    let html = '';

                    // Add title if present
                    if (tableData.title) {
                        html += `<h4 style="margin-bottom: 10px; color: #333;">${tableData.title}</h4>`;
                    }

                    html += '<table style="border-collapse: collapse; width: 100%; margin: 10px 0; border: 1px solid #ddd;">';

                    // Add headers
                    if (tableData.columns && tableData.columns.length > 0) {
                        html += '<thead><tr>';
                        tableData.columns.forEach(column => {
                            html += `<th style="border: 1px solid #ddd; padding: 8px; background-color: #f5f5f5; text-align: left; font-weight: bold;">${column}</th>`;
                        });
                        html += '</tr></thead>';
                    }

                    // Add rows
                    if (tableData.rows && tableData.rows.length > 0) {
                        html += '<tbody>';
                        tableData.rows.forEach((row, rowIndex) => {
                            const bgColor = rowIndex % 2 === 0 ? '#ffffff' : '#f9f9f9';
                            html += `<tr style="background-color: ${bgColor};">`;
                            row.forEach(cell => {
                                html += `<td style="border: 1px solid #ddd; padding: 8px;">${cell}</td>`;
                            });
                            html += '</tr>';
                        });
                        html += '</tbody>';
                    }

                    html += '</table>';

                    // Process the rest of the content (remove the JSON part and render as markdown)
                    const remainingContent = content.replace(jsonMatch[0], '').trim();
                    if (remainingContent) {
                        html += marked.parse(remainingContent);
                    }

                    return html;
                } catch (e) {
                    console.error('Error formatting table data:', e);
                    // Fallback to regular markdown rendering
                    return this.defaultRenderContent(content);
                }
            },
            */


            hasToolCallResults(content) {
                // Check if content contains tool call result patterns
                // which means: is content valid JSON?

                try {
                    const parsed = JSON.parse(content);
                    return true;
                } catch (e) {
                    return false;
                }
            },

            formatToolCallResults(content) {
                try {
                    return String(content);

                    // content is a list of one or more strings that each could be json
                    parts = content;

                    // Build HTML with tool results formatted as gray code boxes
                    let html = '';
                    parts.forEach(part => {
                        if (part.type === 'tool_result') {
                            // Try to format JSON content within the tool result
                            let formattedContent = this.formatToolResultContent(part.content);

                            // Format as gray code box
                            html += `<div class="tool-call-result">
                                <div class="tool-call-header">Tool Call Result</div>
                                <div class="tool-call-content">${formattedContent}</div>
                            </div>`;
                        } else {
                            // Regular markdown content
                            if ( part.content )
                                html += this.defaultRenderContent(part.content);
                        }
                    });

                    return html;
                } catch (e) {
                    console.error('Error formatting tool call results:', e);
                    // Fallback to regular markdown rendering
                    return marked.parse(  String(content)) ;
                }
            },

            formatToolResultContent(content) {
                try {
                    console.log('🔧 formatToolResultContent called with content length:', content.length);
                    //console.log('🔧 Content sample:', content.substring(0, 500));

                    // Look for JSON content in the tool result
                    // Based on logs: Result: [TextContent(type='text', text='{"columns": [...], "data": [...]}', annotations=None)]

                    // Look for JSON content anywhere in the content
                    let jsonMatch = null;

                    // First try to find JSON objects or arrays directly
                    const directJsonMatch = content.match(/(\{[\s\S]*?\}|\[[\s\S]*?\])/);
                    if (directJsonMatch) {
                        jsonMatch = [directJsonMatch[0], directJsonMatch[1]];
                    }

                    // Check for SVG content first (before JSON processing)
                    // Use a more robust pattern that captures the complete SVG content including XML declarations
                    const svgMatch = content.match(/<svg[\s\S]*?<\/svg>/s);

                    console.log('🎨 SVG content detector:', svgMatch);

                    if (svgMatch) {
                        let svgContent = svgMatch[0];
                    
                        console.log('🎨 Detected SVG content, length:', svgContent.length);


                        // Unescape common escape sequences in SVG content
                        svgContent = svgContent
                            .replace(/\\\\/g, '\\')   // Unescape backslashes first
                            .replace(/\\'/g, "'")     // Unescape single quotes
                            .replace(/\\"/g, '"')     // Unescape double quotes
                            .replace(/\\n/g, '\n')    // Unescape newlines
                            .replace(/\\r/g, '\r')    // Unescape carriage returns
                            .replace(/\\t/g, '\t');   // Optional: tabs if SVG was encoded that way

                        // Create a container for the SVG with proper styling
                        const svgContainer = `
                            <div class="svg-content" style="
                                margin: 10px 0;
                                padding: 15px;
                                border: 1px solid #e0e0e0;
                                border-radius: 8px;
                                background: #fafafa;
                                text-align: center;
                                overflow: auto;
                            ">
                                <div class="svg-title" style="
                                    font-size: 14px;
                                    color: #666;
                                    margin-bottom: 10px;
                                    font-weight: bold;
                                ">SVG Chart</div>
                                <div class="svg-wrapper" style="
                                    max-width: 100%;
                                    overflow: auto;
                                ">${svgContent}</div>
                            </div>`;

                        // Replace the SVG part with the rendered container
                        const beforeMatch = content.substring(0, content.indexOf(svgMatch[0]));
                        const afterMatch = content.substring(content.indexOf(svgMatch[0]) + svgMatch[0].length);

                        // return `<pre>${this.escapeHtml(beforeMatch)}</pre>${svgContainer}<pre>${this.escapeHtml(afterMatch)}</pre>`;
                        return svgContainer;
                    }

                    if (jsonMatch) {
                        let jsonText = jsonMatch[1];
                        console.log('📝 Raw extracted text (first 300 chars):', jsonText.substring(0, 300));

                        // Check if this might be SVG chart data before general JSON processing
                        if (jsonText.includes('"svg_content"') && jsonText.includes('"success"')) {
                            console.log('🎨 Detected potential SVG chart data in JSON, attempting to parse...');
                            try {
                                // Try to parse as JSON first to extract SVG content
                                const cleanedJson = jsonText
                                    .replace(/\\n/g, '\n')
                                    .replace(/\\"/g, '"')
                                    .replace(/\\'/g, "'")
                                    .replace(/\\\\/g, '\\');

                                const parsed = JSON.parse(cleanedJson);
                                if (parsed.success && parsed.svg_content) {
                                    console.log('✅ Successfully parsed SVG chart JSON, rendering SVG');
                                    const svgContent = parsed.svg_content;

                                    // Create a container for the SVG with proper styling
                                    const svgContainer = `
                                        <div class="svg-chart-content" style="
                                            margin: 15px 0;
                                            padding: 20px;
                                            border: 1px solid #e0e0e0;
                                            border-radius: 8px;
                                            background: #fafafa;
                                            text-align: center;
                                            overflow: auto;
                                        ">
                                            <div class="svg-title" style="
                                                font-size: 16px;
                                                color: #333;
                                                margin-bottom: 15px;
                                                font-weight: bold;
                                            ">Chart</div>
                                            <div class="svg-wrapper" style="
                                                max-width: 100%;
                                                overflow: auto;
                                                background: white;
                                                border-radius: 4px;
                                                padding: 10px;
                                            ">${svgContent}</div>
                                        </div>`;

                                    // Replace the JSON part with the SVG container
                                    const beforeMatch = content.substring(0, content.indexOf(jsonMatch[0]));
                                    const afterMatch = content.substring(content.indexOf(jsonMatch[0]) + jsonMatch[0].length);

                                    return `<pre>${this.escapeHtml(beforeMatch)}</pre>${svgContainer}<pre>${this.escapeHtml(afterMatch)}</pre>`;
                                }
                            } catch (e) {
                                console.log('❌ Failed to parse SVG chart JSON:', e.message);
                                // Fall through to regular JSON processing
                            }
                        }

                        // Unescape the content
                        jsonText = jsonText
                            .replace(/\\n/g, '\n')
                            .replace(/\\"/g, '"')
                            .replace(/\\'/g, "'")
                            .replace(/\\\\/g, '\\');

                        console.log('🧹 Cleaned text (first 300 chars):', jsonText.substring(0, 300));

                        // Check if it looks like JSON
                        const trimmed = jsonText.trim();
                        if (trimmed.startsWith('{') || trimmed.startsWith('[')) {
                            try {
                                const parsed = JSON.parse(trimmed);
                                console.log('✅ Successfully parsed JSON with keys:', Object.keys(parsed));

                                // Check if this is a table data structure
                                if (parsed.type === 'table') {
                                    console.log('🔧 Detected table data structure, using Composite pattern');
                                    const node = this.buildNode(parsed);
                                    const tableHtml = node.render();

                                    // Replace the JSON part with table
                                    const beforeMatch = content.substring(0, content.indexOf(jsonMatch[0]));
                                    const afterMatch = content.substring(content.indexOf(jsonMatch[0]) + jsonMatch[0].length);

                                    return `<pre>${this.escapeHtml(beforeMatch)}</pre><div class="table-content">${tableHtml}</div><pre>${this.escapeHtml(afterMatch)}</pre>`;
                                } else {
                                    // Regular JSON highlighting
                                    const prettyJson = JSON.stringify(parsed, null, 4);
                                    const highlightedJson = this.highlightJson(prettyJson);

                                    // Replace the JSON part with highlighted version
                                    const beforeMatch = content.substring(0, content.indexOf(jsonMatch[0]));
                                    const afterMatch = content.substring(content.indexOf(jsonMatch[0]) + jsonMatch[0].length);

                                    return `<pre>${this.escapeHtml(beforeMatch)}</pre><div class="json-content">${highlightedJson}</div><pre>${this.escapeHtml(afterMatch)}</pre>`;
                                }
                            } catch (parseError) {
                                console.log('❌ JSON parse error:', parseError.message);
                                console.log('❌ Failed text sample:', trimmed.substring(0, 100));

                                // Try to find a valid JSON substring
                                const jsonStart = trimmed.indexOf('{');
                                const jsonArrayStart = trimmed.indexOf('[');
                                let startIndex = -1;

                                if (jsonStart !== -1 && (jsonArrayStart === -1 || jsonStart < jsonArrayStart)) {
                                    startIndex = jsonStart;
                                } else if (jsonArrayStart !== -1) {
                                    startIndex = jsonArrayStart;
                                }

                                if (startIndex !== -1) {
                                    const jsonSubstring = trimmed.substring(startIndex);
                                    try {
                                        const parsed = JSON.parse(jsonSubstring);
                                        console.log('✅ Successfully parsed JSON substring');

                                        // Check if this is a table data structure
                                        if (parsed.type === 'table') {
                                            console.log('🔧 Detected table data structure in substring, using Composite pattern');
                                            const node = this.buildNode(parsed);
                                            const tableHtml = node.render();
                                            return `<div class="table-content">${tableHtml}</div>`;
                                        }
                                        // Check if this is SVG chart data (from quick_chart tool)
                                        else if (parsed.success && parsed.svg_content) {
                                            console.log('🎨 Detected SVG chart data, rendering SVG');
                                            const svgContent = parsed.svg_content;

                                            // Create a container for the SVG with proper styling
                                            const svgContainer = `
                                                <div class="svg-chart-content" style="
                                                    margin: 15px 0;
                                                    padding: 20px;
                                                    border: 1px solid #e0e0e0;
                                                    border-radius: 8px;
                                                    background: #fafafa;
                                                    text-align: center;
                                                    overflow: auto;
                                                ">
                                                    <div class="svg-title" style="
                                                        font-size: 16px;
                                                        color: #333;
                                                        margin-bottom: 15px;
                                                        font-weight: bold;
                                                    ">Chart</div>
                                                    <div class="svg-wrapper" style="
                                                        max-width: 100%;
                                                        overflow: auto;
                                                        background: white;
                                                        border-radius: 4px;
                                                        padding: 10px;
                                                    ">${svgContent}</div>
                                                </div>`;
                                            return svgContainer;
                                        }
                                        else {
                                            const prettyJson = JSON.stringify(parsed, null, 4);
                                            const highlightedJson = this.highlightJson(prettyJson);
                                            return `<div class="json-content">${highlightedJson}</div>`;
                                        }
                                    } catch (e) {
                                        console.log('❌ JSON substring parse also failed');
                                    }
                                }
                            }
                        } else {
                            console.log('📄 Not JSON format, treating as plain text');
                        }
                    } else {
                        // Fallback: look for any JSON-like structure in the content
                        const jsonPattern = /(\{[^}]*\}|\[[^\]]*\])/;
                        const fallbackMatch = content.match(jsonPattern);
                        if (fallbackMatch) {
                            console.log('🔄 Found fallback JSON pattern:', fallbackMatch[1].substring(0, 100));
                            try {
                                const parsed = JSON.parse(fallbackMatch[1]);

                                // Check if this is a table data structure
                                if (parsed.type === 'table') {
                                    console.log('🔧 Detected table data structure in fallback, using Composite pattern');
                                    const node = this.buildNode(parsed);
                                    const tableHtml = node.render();
                                    return `<div class="table-content">${tableHtml}</div>`;
                                }
                                // Check if this is SVG chart data (from quick_chart tool)
                                else if (parsed.success && parsed.svg_content) {
                                    console.log('🎨 Detected SVG chart data in fallback, rendering SVG');
                                    const svgContent = parsed.svg_content;

                                    // Create a container for the SVG with proper styling
                                    const svgContainer = `
                                        <div class="svg-chart-content" style="
                                            margin: 15px 0;
                                            padding: 20px;
                                            border: 1px solid #e0e0e0;
                                            border-radius: 8px;
                                            background: #fafafa;
                                            text-align: center;
                                            overflow: auto;
                                        ">
                                            <div class="svg-title" style="
                                                font-size: 16px;
                                                color: #333;
                                                margin-bottom: 15px;
                                                font-weight: bold;
                                            ">Chart</div>
                                            <div class="svg-wrapper" style="
                                                max-width: 100%;
                                                overflow: auto;
                                                background: white;
                                                border-radius: 4px;
                                                padding: 10px;
                                            ">${svgContent}</div>
                                        </div>`;
                                    return svgContainer;
                                }
                                else {
                                    const prettyJson = JSON.stringify(parsed, null, 4);
                                    const highlightedJson = this.highlightJson(prettyJson);
                                    return `<div class="json-content">${highlightedJson}</div>`;
                                }
                            } catch (e) {
                                console.log('❌ Fallback JSON parse failed');
                            }
                        }
                    }

                    // Fallback: return as plain pre-formatted text
                    return `<pre>${this.escapeHtml(content)}</pre>`;
                } catch (e) {
                    console.log('💥 Error in formatToolResultContent:', e);
                    return `<pre>${this.escapeHtml(content)}</pre>`;
                }
            },

            highlightJson(jsonString) {
                // Simple JSON syntax highlighting with colors
                return jsonString
                    // Highlight property names (keys)
                    .replace(/("([^"\\]|\\.)*")\s*:/g, '<span class="json-key">$1</span>:')
                    // Highlight string values
                    .replace(/:\s*("([^"\\]|\\.)*")/g, ': <span class="json-string">$1</span>')
                    // Highlight boolean values
                    .replace(/:\s*(true|false)/g, ': <span class="json-boolean">$1</span>')
                    // Highlight null values
                    .replace(/:\s*(null)/g, ': <span class="json-null">$1</span>')
                    // Highlight numbers
                    .replace(/:\s*(-?\d+\.?\d*)/g, ': <span class="json-number">$1</span>')
                    // Highlight punctuation
                    .replace(/([{}[\],])/g, '<span class="json-punctuation">$1</span>');
            },

            renderJsonAsTable(tableData) {
                try {
                    console.log('🔧 renderJsonAsTable called with data:', tableData);

                    let html = '';

                    // Add title if present
                    if (tableData.title) {
                        html += `<h4 style="margin: 0 0 10px 0; color: #333; font-size: 1.1em;">${this.escapeHtml(tableData.title)}</h4>`;
                    }

                    html += '<table style="border-collapse: collapse; width: 100%; margin: 0; border: 1px solid #ddd; font-size: 0.9em;">';

                    // Add headers
                    if (tableData.columns && tableData.columns.length > 0) {
                        html += '<thead><tr>';
                        tableData.columns.forEach(header => {
                            html += `<th style="border: 1px solid #ddd; padding: 8px; background-color: #f5f5f5; text-align: left; font-weight: bold;">${this.escapeHtml(header)}</th>`;
                        });
                        html += '</tr></thead>';
                    }

                    // Add rows
                    if (tableData.rows && tableData.rows.length > 0) {
                        html += '<tbody>';
                        tableData.rows.forEach((row, rowIndex) => {
                            const bgColor = rowIndex % 2 === 0 ? '#ffffff' : '#f9f9f9';
                            html += `<tr style="background-color: ${bgColor};">`;
                            row.forEach(cell => {
                                html += `<td style="border: 1px solid #ddd; padding: 8px;">${this.escapeHtml(String(cell))}</td>`;
                            });
                            html += '</tr>';
                        });
                        html += '</tbody>';
                    }

                    html += '</table>';

                    return html;
                } catch (e) {
                    console.error('Error rendering JSON as table:', e);
                    // Fallback to JSON display
                    const prettyJson = JSON.stringify(tableData, null, 4);
                    return `<div class="json-content">${this.highlightJson(prettyJson)}</div>`;
                }
            },

            escapeHtml(text) {
                const div = document.createElement('div');
                div.textContent = text;
                return div.innerHTML;
            },

            /**
             * Properly extract text content from MCP TextContent objects or their string representations
             * @param {any} content - The content to extract text from
             * @returns {string} - The extracted text content
             */
            extractTextFromMCPResult(content) {
                // Handle null/undefined
                if (!content) return '';

                // Handle string content
                if (typeof content === 'string') {
                    // Try to parse as JSON first
                    try {
                        const parsed = JSON.parse(content);
                        return this.extractTextFromMCPResult(parsed); // Recursive call
                    } catch (e) {
                        // Not JSON, check if it's a TextContent string representation
                        // Handle both complete and truncated TextContent strings

                        // Try complete pattern first: text='...' or text="..."
                        let textMatch = content.match(/text=['"]([^'"]*)['"]/);

                        if (!textMatch) {
                            // Try truncated pattern: text='... (no closing quote)
                            textMatch = content.match(/text=['"]([^'"]*?)$/);
                        }

                        if (!textMatch) {
                            // Try even more truncated: type='text', text='...
                            textMatch = content.match(/type='text',\s*text=['"]([^'"]*?)$/);
                        }

                        if (!textMatch) {
                            // Try minimal pattern: just text='...
                            textMatch = content.match(/text=['"](.*)$/);
                        }

                        if (textMatch) {
                            let extractedText = textMatch[1];

                            // Unescape common escape sequences
                            extractedText = extractedText
                                .replace(/\\n/g, '\n')
                                .replace(/\\t/g, '\t')
                                .replace(/\\"/g, '"')
                                .replace(/\\'/g, "'")
                                .replace(/\\\\/g, '\\');

                            // If text seems truncated, add indicator
                            if (content.endsWith(extractedText) && !content.includes("', annotations")) {
                                extractedText += " [TRUNCATED]";
                            }

                            return extractedText;
                        }

                        return content; // Return as-is
                    }
                }

                // Handle TextContent object
                if (content && typeof content === 'object' && content.type === 'text' && content.text) {
                    return content.text;
                }

                // Handle array of TextContent objects
                if (Array.isArray(content) && content.length > 0) {
                    return this.extractTextFromMCPResult(content[0]); // Recursive call
                }

                // Handle generic object with text property
                if (content && typeof content === 'object' && content.text) {
                    return content.text;
                }

                return String(content);
            },

            scrollToBottom() {
                this.$nextTick(() => {
                    // Scroll main chat container
                    if (this.$refs.chatContainer) {
                        this.$refs.chatContainer.scrollTop = this.$refs.chatContainer.scrollHeight;
                    }

                    // Scroll sidebar chat container using ref
                    if (this.$refs.sidebarChatContainer) {
                        this.$refs.sidebarChatContainer.scrollTop = this.$refs.sidebarChatContainer.scrollHeight;
                    }
                });
            },

            generateTitleFromMessages(messages) {
                if (!messages || !messages.length) return null;

                // Find the first user message
                const userMessages = messages.filter(msg => msg.role === 'user');
                if (!userMessages.length) return null;

                const firstUserMessage = userMessages[0].content;
                if (!firstUserMessage) return null;

                // Extract the first 4 words (or fewer if the message is shorter)
                const words = firstUserMessage.split(/\s+/).filter(word => word.trim().length > 0).slice(0, 4);
                if (!words.length) return null;

                let title = words.join(' ');

                // Add ellipsis if the message is longer than 4 words
                if (firstUserMessage.split(/\s+/).filter(word => word.trim().length > 0).length > 4) {
                    title += '...';
                }

                return title;
            },

            async deleteConversation(conversationId) {
                this.isDeletingConversation = conversationId;
                this.error = null;

                try {
                    const response = await axios.delete(`/gaia_chat/api/conversations/${conversationId}/delete/`);

                    if (response.data.success) {
                        // If the deleted conversation was the active one, clear it
                        if (this.activeConversation && this.activeConversation.id === conversationId) {
                            this.activeConversation = null;
                        }

                        // Remove the conversation from the list
                        this.conversations = this.conversations.filter(conv => conv.id !== conversationId);
                    }
                } catch (error) {
                    console.error('Error deleting conversation:', error);
                    if (error.response && error.response.status === 403) {
                        // Permission error
                        this.error = "You do not have permission to delete this conversation";
                    } else if (error.response && error.response.status === 404) {
                        // Not found error
                        this.error = "Conversation not found";
                        // Remove it from the list anyway
                        this.conversations = this.conversations.filter(conv => conv.id !== conversationId);
                    } else {
                        this.error = "Error deleting conversation";
                    }
                } finally {
                    this.isDeletingConversation = null;
                }
            },

            // Fix the updateConversation method to ensure proper URL formatting
            async updateConversation(conversationId, messages) {
                try {
                    // Make sure we have a valid conversation ID
                    if (!conversationId) {
                        console.error('Cannot update conversation: No conversation ID provided');
                        return false;
                    }

                    // Make sure we have messages
                    if (!messages || !Array.isArray(messages)) {
                        console.error('Cannot update conversation: Invalid messages array');
                        return false;
                    }

                    // Use the conversation ID as is, without any formatting
                    console.log(`Updating conversation with ID: ${conversationId}`);
                    console.log(`URL: /gaia_chat/api/conversations/${conversationId}/update/`);

                    // Send the update request
                    const response = await axios.post(`/gaia_chat/api/conversations/${conversationId}/update/`, {
                        messages: messages
                    });

                    console.log('Update response:', response.data);

                    return response.data.success;
                } catch (error) {
                    console.error(`Error updating conversation ${conversationId}:`, error);

                    // Log detailed error information
                    if (error.response) {
                        console.error('Response status:', error.response.status);
                        console.error('Response data:', error.response.data);
                    }

                    // If the conversation doesn't exist (404), try to create a new one
                    if (error.response && error.response.status === 404) {
                        console.log('Conversation not found, attempting to create a new one...');
                        try {
                            // Create a new conversation with the same messages
                            const createResponse = await axios.post('/gaia_chat/api/conversations/create/', {
                                title: `Conversation ${new Date().toLocaleString()}`,
                                messages: messages
                            });

                            if (createResponse.data.success) {
                                console.log('Created new conversation:', createResponse.data.conversation.id);

                                // Update the active conversation with the new one
                                this.activeConversation = createResponse.data.conversation;

                                // Update the URL while preserving the current path
                                const currentPath = window.location.pathname;
                                const newUrl = `${currentPath}?conversation=${this.activeConversation.id}`;
                                window.history.pushState({}, '', newUrl);

                                return true;
                            }
                        } catch (createError) {
                            console.error('Failed to create new conversation:', createError);
                        }
                    }

                    return false;
                }
            }
        }
    });
}
