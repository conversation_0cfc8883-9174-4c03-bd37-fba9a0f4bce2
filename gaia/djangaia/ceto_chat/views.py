from django.shortcuts import render
from django.http import JsonResponse
from django.views.decorators.http import require_http_methods
from django.views.decorators.csrf import csrf_exempt
from django.conf import settings
import logging

# Import MCP client library
try:
    from gaia.gaia_ceto.proto_mcp_http.mcp_http_clientlib import MCPClientLib
    MCP_AVAILABLE = True
except ImportError:
    MCP_AVAILABLE = False

logger = logging.getLogger(__name__)

# MCP Configuration - move to settings.py later
MCP_SERVERS = getattr(settings, 'MCP_SERVERS', {
    'default': {
        'url': 'http://0.0.0.0:9000/mcp',
        'timeout': 30,
        'retry_attempts': 3
    }
})

class MCPAPIClient:
    """Configurable MCP client with dependency injection"""

    def __init__(self, server_name='default'):
        if server_name not in MCP_SERVERS:
            raise ValueError(f"Unknown MCP server: {server_name}")

        self.config = MCP_SERVERS[server_name]
        self.server_url = self.config['url']
        self.timeout = self.config.get('timeout', 30)
        self.retry_attempts = self.config.get('retry_attempts', 3)

    async def get_tools(self):
        """Get available MCP tools asynchronously"""
        client = MCPClientLib()

        try:
            success = await client.connect_to_server(self.server_url)

            if success:
                tools = client.available_tools
                return {
                    'success': True,
                    'tools': tools,
                    'server_url': self.server_url,
                    'tool_count': len(tools)
                }
            else:
                return {
                    'success': False,
                    'error': 'Failed to connect to MCP server',
                    'tools': []
                }

        except Exception as e:
            logger.error(f"Error connecting to MCP server {self.server_url}: {e}")
            return {
                'success': False,
                'error': str(e),
                'tools': []
            }

        finally:
            try:
                await client.cleanup()
            except Exception as e:
                logger.warning(f"Error during MCP client cleanup: {e}")


def ceto_chat_view(request):
    """
    Renders the Ceto Chat base template.
    """
    return render(request, 'ceto_chat/ceto_chat_base.html')


@csrf_exempt
@require_http_methods(["GET"])
async def list_mcp_tools(request):
    """
    List available MCP tools from the running server.
    Level 0031: Connect to MCP server (async version with dependency injection)
    """
    if not MCP_AVAILABLE:
        return JsonResponse({
            'success': False,
            'error': 'MCP client library not available',
            'tools': []
        }, status=500)

    try:
        # Use the new configurable MCP client
        mcp_client = MCPAPIClient(server_name='default')
        result = await mcp_client.get_tools()

        logger.info(f"MCP tools request result: success={result['success']}, "
                   f"tool_count={result.get('tool_count', 0)}")

        if result['success']:
            return JsonResponse(result)
        else:
            return JsonResponse(result, status=500)

    except Exception as e:
        logger.error(f"Error listing MCP tools: {e}")
        return JsonResponse({
            'success': False,
            'error': str(e),
            'tools': []
        }, status=500)
