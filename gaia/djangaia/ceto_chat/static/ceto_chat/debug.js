// Debug Panel Functionality for Ceto Chat v2
// This file contains all debug panel logic and utilities

// Debug panel mixin that can be used by Vue components
window.DebugPanelMixin = {
    data() {
        return {
            // Debug panel state
            debugPanelVisible: false,
            debugPaneMode: 'calls', // 'calls', 'api', 'history', 'tools', 'errors'
            
            // Debug data collections
            debugLogs: [],
            callDetails: [],
            errorLog: [],
            
            // Debug counters and IDs
            debugLogCounter: 0,
            callDetailCounter: 0,
            errorCounter: 0
        };
    },
    
    methods: {
        // Toggle debug panel visibility
        toggleDebugPanel() {
            this.debugPanelVisible = !this.debugPanelVisible;
            console.log('Debug panel toggled:', this.debugPanelVisible);
        },
        
        // Set debug pane mode (which tab is active)
        setDebugPaneMode(mode) {
            this.debugPaneMode = mode;
            console.log('Debug pane mode set to:', mode);
        },
        
        // Clear all debug logs
        clearDebugLogs() {
            this.debugLogs = [];
            this.callDetails = [];
            this.errorLog = [];
            console.log('Debug logs cleared');
        },
        
        // Reset all debug state
        resetAllState() {
            this.clearDebugLogs();
            this.debugPaneMode = 'calls';
            console.log('Debug state reset');
        },
        
        // Add a debug log entry
        addDebugLog(type, direction, data, url = null) {
            const logEntry = {
                id: ++this.debugLogCounter,
                timestamp: new Date(),
                type: type, // 'HTTP', 'WEBSOCKET', 'ERROR', etc.
                direction: direction, // 'SENT', 'RECEIVED'
                data: typeof data === 'object' ? JSON.stringify(data, null, 2) : data,
                url: url
            };
            
            this.debugLogs.unshift(logEntry); // Add to beginning
            
            // Keep only last 100 entries
            if (this.debugLogs.length > 100) {
                this.debugLogs = this.debugLogs.slice(0, 100);
            }
            
            console.log('Debug log added:', logEntry);
        },
        
        // Add a call detail entry
        addCallDetail(method, url, requestBody = null) {
            const callEntry = {
                id: ++this.callDetailCounter,
                timestamp: new Date(),
                method: method,
                url: url,
                status: 'PENDING',
                latency: null,
                sentBytes: requestBody ? new Blob([JSON.stringify(requestBody)]).size : 0,
                receivedBytes: 0,
                requestBody: requestBody ? JSON.stringify(requestBody, null, 2) : '',
                responseBody: '',
                error: null,
                completed: false
            };
            
            this.callDetails.unshift(callEntry); // Add to beginning
            
            // Keep only last 50 entries
            if (this.callDetails.length > 50) {
                this.callDetails = this.callDetails.slice(0, 50);
            }
            
            console.log('Call detail added:', callEntry);
            return callEntry;
        },
        
        // Update a call detail entry
        updateCallDetail(callEntry, status, responseBody = null, error = null) {
            callEntry.status = status;
            callEntry.completed = true;
            callEntry.latency = new Date() - callEntry.timestamp;
            
            if (responseBody) {
                callEntry.responseBody = typeof responseBody === 'object' ? 
                    JSON.stringify(responseBody, null, 2) : responseBody;
                callEntry.receivedBytes = new Blob([callEntry.responseBody]).size;
            }
            
            if (error) {
                callEntry.error = typeof error === 'object' ? 
                    JSON.stringify(error, null, 2) : error;
            }
            
            console.log('Call detail updated:', callEntry);
        },
        
        // Add an error log entry
        addErrorLog(message, source = null) {
            const errorEntry = {
                id: ++this.errorCounter,
                timestamp: new Date(),
                message: typeof message === 'object' ? JSON.stringify(message, null, 2) : message,
                source: source
            };
            
            this.errorLog.unshift(errorEntry); // Add to beginning
            
            // Keep only last 50 entries
            if (this.errorLog.length > 50) {
                this.errorLog = this.errorLog.slice(0, 50);
            }
            
            console.log('Error logged:', errorEntry);
        },
        
        // Format timestamp for display
        formatTimestamp(timestamp) {
            if (!timestamp) return '';
            const date = new Date(timestamp);
            return date.toLocaleTimeString() + '.' + String(date.getMilliseconds()).padStart(3, '0');
        },
        
        // Format bytes for display
        formatBytes(bytes) {
            if (!bytes || bytes === 0) return '0 B';
            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
        }
    },
    
    mounted() {
        // Add some sample debug data for testing
        this.addDebugLog('SYSTEM', 'INFO', 'Debug panel initialized');
        this.addCallDetail('GET', '/api/test');
        this.updateCallDetail(this.callDetails[0], 200, { message: 'Test successful' });
        
        console.log('Debug panel mixin mounted');
    }
};

// Global debug utilities
window.DebugUtils = {
    // Log function calls for debugging
    logFunctionCall(functionName, args = []) {
        console.log(`[DEBUG] Function called: ${functionName}`, args);
    },
    
    // Log API calls
    logApiCall(method, url, data = null) {
        console.log(`[DEBUG] API Call: ${method} ${url}`, data);
    },
    
    // Log errors
    logError(error, context = null) {
        console.error(`[DEBUG] Error${context ? ` in ${context}` : ''}:`, error);
    }
};

console.log('Debug panel utilities loaded');
