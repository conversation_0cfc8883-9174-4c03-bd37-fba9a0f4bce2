// Ceto Chat JavaScript v2
console.log('Ceto Chat app v2 loading...');

// Wait for DOM to be ready
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Vue app with debug panel functionality
    const { createApp } = Vue;

    const app = createApp({
        // Mix in debug panel functionality
        mixins: [window.DebugPanelMixin],

        data() {
            return {
                appVersion: '2.0.0',
                appName: 'Ceto Chat',
                // MCP-related data
                mcpTools: [],
                mcpServerUrl: '',
                mcpConnected: false,
                mcpLoading: false
            };
        },

        methods: {
            // Level 0031: Load MCP tools from server
            async loadMcpTools() {
                this.mcpLoading = true;
                this.addDebugLog('MCP', 'SENT', 'Loading MCP tools...');

                const callDetail = this.addCallDetail('GET', '/ceto_chat/api/mcp/tools/');

                try {
                    const response = await fetch('/ceto_chat/api/mcp/tools/');
                    const data = await response.json();

                    if (response.ok && data.success) {
                        this.mcpTools = data.tools || [];
                        this.mcpServerUrl = data.server_url || '';
                        this.mcpConnected = true;

                        this.updateCallDetail(callDetail, response.status, data);
                        this.addDebugLog('MCP', 'RECEIVED', `Loaded ${this.mcpTools.length} tools from ${this.mcpServerUrl}`);

                        console.log('MCP tools loaded:', this.mcpTools);
                    } else {
                        this.mcpConnected = false;
                        this.updateCallDetail(callDetail, response.status, data, data.error);
                        this.addErrorLog(data.error || 'Failed to load MCP tools', 'loadMcpTools');
                    }
                } catch (error) {
                    this.mcpConnected = false;
                    this.updateCallDetail(callDetail, 'ERROR', null, error.message);
                    this.addErrorLog(`Network error loading MCP tools: ${error.message}`, 'loadMcpTools');
                    console.error('Error loading MCP tools:', error);
                } finally {
                    this.mcpLoading = false;
                }
            },

            // Test function to demonstrate debug logging
            testDebugPanel() {
                this.addDebugLog('TEST', 'SENT', { message: 'Test debug panel functionality' });
                this.addErrorLog('This is a test error message', 'testDebugPanel');

                const callDetail = this.addCallDetail('POST', '/api/test-endpoint', { test: true });
                setTimeout(() => {
                    this.updateCallDetail(callDetail, 200, { success: true, data: 'Test response' });
                }, 1000);
            }
        },

        mounted() {
            console.log(`${this.appName} v${this.appVersion} initialized`);

            // Add initial debug log
            this.addDebugLog('SYSTEM', 'INFO', `${this.appName} v${this.appVersion} started`);

            // Level 0031: Load MCP tools on startup
            this.loadMcpTools();

            // Test debug panel after 3 seconds (after MCP tools load)
            setTimeout(() => {
                this.testDebugPanel();
            }, 3000);
        }
    });

    // Mount the app
    app.mount('#app');

    console.log('Ceto Chat app v2 mounted successfully');
});
