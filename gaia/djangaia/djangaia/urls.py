"""djangaia URL Configuration

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/3.2/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.contrib import admin
from django.urls import include, path

from django.contrib.staticfiles.urls import staticfiles_urlpatterns

urlpatterns = [
    #path('kvx/', include('kvexplore.urls')),
    #path('gaiachat/', include('gaiachat.urls')),
    path('dealer_cs/', include('coresignal_browser.urls')),
    path('kvx/', include('kvexplore.urls')),
    path('endpoint/', include('endpoint.urls')),
    path('gaia_chat/', include('gaia_chat.urls')),
    path('ceto_chat/', include('ceto_chat.urls')),
    path('admin/', admin.site.urls),
]


urlpatterns += staticfiles_urlpatterns()
