#!/usr/bin/env python3
"""
Test script to verify immediate log flushing.
This script will write logs and immediately check if they appear in files.
"""

import sys
import time
from pathlib import Path

# Add current directory to path for imports
sys.path.insert(0, str(Path(__file__).parent))

from core.level_0003_logging import Level0003Logger
from core.logging_factory import create_llm_loggers

def test_immediate_flush():
    """Test that logs are flushed immediately to files."""
    print("🧪 Testing immediate log flushing...")
    
    # Clean up any existing test logs
    log_dir = Path('/tmp/gaia_logs/ceto')
    test_files = ['flushtest.log', 'flushtest_send.log', 'flushtest_rcv.log']
    for file_name in test_files:
        file_path = log_dir / file_name
        if file_path.exists():
            file_path.unlink()
    
    print("\n1. Testing Level 0003 immediate flush...")
    logger = Level0003Logger('flushtest')
    
    # Log a request and immediately check if it's in the file
    request_id = "flush-test-001"
    print(f"   Writing log entry with request_id: {request_id}")
    
    logger.log_request(request_id, "immediate flush test", {"test": "flush_verification"})
    
    # Check immediately if the log appears in files (no sleep!)
    send_file = log_dir / 'flushtest_send.log'
    main_file = log_dir / 'flushtest.log'
    
    print(f"   Checking send log: {send_file}")
    if send_file.exists():
        content = send_file.read_text()
        if request_id in content:
            print("   ✅ SEND log flushed immediately!")
            print(f"      Content: {content.strip().split(chr(10))[-1]}")
        else:
            print("   ❌ Request ID not found in send log")
            print(f"      Content: {content}")
    else:
        print("   ❌ Send log file doesn't exist")
    
    print(f"   Checking main log: {main_file}")
    if main_file.exists():
        content = main_file.read_text()
        if request_id in content:
            print("   ✅ MAIN log flushed immediately!")
            print(f"      Content: {content.strip().split(chr(10))[-1]}")
        else:
            print("   ❌ Request ID not found in main log")
            print(f"      Content: {content}")
    else:
        print("   ❌ Main log file doesn't exist")
    
    # Test response logging
    print(f"   Writing response log...")
    logger.log_response(request_id, "flush test complete", {"result": "verified"}, 42.0)
    
    rcv_file = log_dir / 'flushtest_rcv.log'
    print(f"   Checking receive log: {rcv_file}")
    if rcv_file.exists():
        content = rcv_file.read_text()
        if request_id in content and "verified" in content:
            print("   ✅ RECEIVE log flushed immediately!")
            print(f"      Content: {content.strip().split(chr(10))[-1]}")
        else:
            print("   ❌ Response not found in receive log")
            print(f"      Content: {content}")
    else:
        print("   ❌ Receive log file doesn't exist")
    
    print("\n2. Testing legacy factory immediate flush...")
    # Clean up factory test files
    factory_files = ['llmwrap.log', 'llmwrap_send.log', 'llmwrap_rcv.log']
    for file_name in factory_files:
        file_path = log_dir / file_name
        if file_path.exists():
            file_path.unlink()
    
    wrap_logger, send_logger, rcv_logger = create_llm_loggers()
    
    test_message = "factory_flush_test_12345"
    print(f"   Writing factory log: {test_message}")
    
    wrap_logger.info(f"Testing factory flush: {test_message}")
    send_logger.info(f"SEND - factory-test - {test_message}")
    rcv_logger.info(f"RECEIVE - factory-test - {test_message}")
    
    # Check immediately
    wrap_file = log_dir / 'llmwrap.log'
    if wrap_file.exists():
        content = wrap_file.read_text()
        if test_message in content:
            print("   ✅ Factory WRAP log flushed immediately!")
        else:
            print("   ❌ Factory wrap log not flushed")
            print(f"      Content: {content}")
    else:
        print("   ❌ Factory wrap log file doesn't exist")
    
    factory_send_file = log_dir / 'llmwrap_send.log'
    if factory_send_file.exists():
        content = factory_send_file.read_text()
        if test_message in content:
            print("   ✅ Factory SEND log flushed immediately!")
        else:
            print("   ❌ Factory send log not flushed")
    else:
        print("   ❌ Factory send log file doesn't exist")
    
    print("\n3. Summary of log directory...")
    if log_dir.exists():
        all_log_files = list(log_dir.glob('*.log'))
        print(f"   📂 Directory: {log_dir}")
        print(f"   📄 Total log files: {len(all_log_files)}")
        for log_file in sorted(all_log_files):
            size = log_file.stat().st_size
            print(f"      • {log_file.name} ({size} bytes)")
            
            # Show last line of each file for verification
            if size > 0:
                try:
                    content = log_file.read_text().strip()
                    last_line = content.split('\n')[-1] if content else "(empty)"
                    print(f"        Last: {last_line[:100]}...")
                except Exception as e:
                    print(f"        Error reading: {e}")
    
    print("\n🎯 Immediate flush test complete!")

if __name__ == "__main__":
    test_immediate_flush()