#!/usr/bin/env python3
"""
Test the new exa_research functionality in mcp_integration.py
"""

import os
import sys
from pathlib import Path

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent))

from core.mcp_integration import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

def test_exa_research():
    """Test the exa_research tool implementation."""
    
    print("🔬 TESTING EXA RESEARCH FUNCTIONALITY")
    print("=" * 50)
    
    # Check if EXA_API_KEY is set
    api_key = os.getenv("EXA_API_KEY")
    if not api_key:
        print("❌ EXA_API_KEY not set - testing will show error handling")
    else:
        print(f"✅ EXA_API_KEY found (ends with: ...{api_key[-6:]})")
    
    # Initialize MCP handler
    handler = MCPToolHandler()
    
    # Test 1: Check if exa_research is in available tools
    print("\n📋 Available tools:")
    tools = handler.get_available_tools()
    for tool in tools:
        marker = "🔬" if tool == "exa_research" else "🛠️"
        print(f"  {marker} {tool}")
    
    if "exa_research" not in tools:
        print("❌ exa_research not found in available tools!")
        return False
    
    # Test 2: Check tool schema
    print("\n📝 Tool schemas:")
    schemas = handler.get_available_tools_with_schema()
    exa_research_schema = None
    for schema in schemas:
        if schema["name"] == "exa_research":
            exa_research_schema = schema
            break
    
    if exa_research_schema:
        print(f"✅ exa_research schema found:")
        print(f"  Description: {exa_research_schema['description']}")
        print(f"  Parameters: {list(exa_research_schema['parameters']['properties'].keys())}")
    else:
        print("❌ exa_research schema not found!")
        return False
    
    # Test 3: Execute exa_research (short query to minimize cost)
    print(f"\n🔍 Testing exa_research execution...")
    
    test_query = "latest trends in AI search technology 2025"
    args = {
        "query": test_query,
        "max_sources": 5,  # Small number for testing
        "use_schema": False  # Start simple
    }
    
    print(f"Query: '{test_query}'")
    print(f"Args: {args}")
    print("⏱️  This may take 30-60 seconds for research to complete...")
    
    try:
        result = handler.execute_single_tool("exa_research", args)
        
        print(f"\n📊 Result:")
        print(f"Success: {result.get('success')}")
        
        if result.get("success"):
            print("✅ exa_research executed successfully!")
            result_text = result.get("result", "")
            if len(result_text) > 500:
                print(f"Result preview (first 500 chars):")
                print(result_text[:500] + "...")
            else:
                print(f"Full result:")
                print(result_text)
        else:
            error = result.get("error", "Unknown error")
            print(f"❌ exa_research failed: {error}")
            
            # This is expected if no API key is set
            if "unavailable" in error:
                print("ℹ️  This is expected without EXA_API_KEY")
                return True  # Still a successful test of error handling
                
    except Exception as e:
        print(f"❌ Exception during exa_research test: {e}")
        return False
    
    print(f"\n✅ exa_research test completed!")
    return True

if __name__ == "__main__":
    success = test_exa_research()
    print(f"\n{'🎉 ALL TESTS PASSED' if success else '💥 TESTS FAILED'}")
    sys.exit(0 if success else 1)