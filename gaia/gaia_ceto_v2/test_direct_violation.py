#!/usr/bin/env python3
"""Direct test of the flag violation logging"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import logging
logging.basicConfig(level=logging.DEBUG, format='%(levelname)s - %(name)s - %(message)s')

from gaia_ceto_v2.core import create_chat_manager
from gaia_ceto_v2.settings import GAIA_SETTINGS

# Create chat manager with MCP
chat_manager = create_chat_manager(
    provider_name="gemini",
    enable_mcp=True,
    verbose=True
)

# Send research query
print("Sending research query...")
response = chat_manager.send_message("research rocket ships")

print("\n=== Response ===")
print(response[:500] + "..." if len(response) > 500 else response)