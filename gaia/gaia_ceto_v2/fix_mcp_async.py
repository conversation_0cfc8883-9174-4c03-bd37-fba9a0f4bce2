#!/usr/bin/env python3
"""
Fix MCP Async Issue

The problem is that MCPToolHandler._lazy_init() calls asyncio.run() 
which can conflict with existing event loops. Let's patch it to handle
both sync and async contexts properly.
"""

import asyncio

def patch_mcp_integration():
    """Patch the MCP integration to handle async context properly."""
    
    def safe_async_run(coro):
        """Run async code safely - works in both sync and async contexts."""
        try:
            # Try to get current event loop
            loop = asyncio.get_running_loop()
            # We're in an async context, can't use asyncio.run()
            # Create a new thread to run the async code
            import concurrent.futures
            import threading
            
            def run_in_thread():
                # Create new event loop in thread
                new_loop = asyncio.new_event_loop()
                asyncio.set_event_loop(new_loop)
                try:
                    return new_loop.run_until_complete(coro)
                finally:
                    new_loop.close()
            
            with concurrent.futures.ThreadPoolExecutor() as executor:
                future = executor.submit(run_in_thread)
                return future.result(timeout=30)
                
        except RuntimeError:
            # No event loop running, safe to use asyncio.run()
            return asyncio.run(coro)
    
    # Monkey patch the MCPToolHandler
    from core.mcp_integration import MCPToolHandler
    
    original_lazy_init = MCPToolHandler._lazy_init
    
    def patched_lazy_init(self):
        """Patched version that handles async context properly."""
        if self._initialized:
            return
            
        try:
            # Import MCP dependencies only when needed
            from mcp.client.session import ClientSession
            from mcp.client.streamable_http import streamablehttp_client
            self.ClientSession = ClientSession
            self.streamablehttp_client = streamablehttp_client
            
            # Get available tools using safe async runner
            self.available_tools = safe_async_run(self._discover_tools())
            self._initialized = True
            
            import logging
            logger = logging.getLogger(__name__)
            logger.info(f"MCP handler initialized with {len(self.available_tools)} tools")
            
        except ImportError as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"MCP dependencies not available: {e}")
            self._initialized = False
        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Failed to initialize MCP client: {e}")
            self._initialized = False
    
    # Apply the patch
    MCPToolHandler._lazy_init = patched_lazy_init
    print("✅ MCPToolHandler patched to handle async context properly")

if __name__ == "__main__":
    patch_mcp_integration()
    print("🔧 MCP async context patch applied")