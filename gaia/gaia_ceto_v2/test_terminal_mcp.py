#!/usr/bin/env python3
"""
Test script to verify Level 0030 MCP integration works.
"""

import subprocess
import sys
import time
import signal
import os
from pathlib import Path

def test_mcp_integration():
    """Test MCP integration with terminal chat."""
    
    print("🧪 Testing Level 0030 MCP Integration")
    print("=" * 50)
    
    # Start MCP server
    print("1. Starting MCP server...")
    server_proc = subprocess.Popen([
        sys.executable, "tests/mcp_interface/dummy_mcp_server.py", "9999"
    ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
    
    # Wait for server to start  
    time.sleep(3)
    
    try:
        # Test terminal chat
        print("2. Testing terminal chat with MCP...")
        
        chat_input = "tools\ncall echostring hello world\nexit\n"
        
        result = subprocess.run([
            sys.executable, "terminal_chat.py", 
            "--with-mcp", "--mcp-server", "http://127.0.0.1:9999/mcp/"
        ], input=chat_input, text=True, capture_output=True, timeout=30)
        
        print("3. Results:")
        print("-" * 30)
        print("STDOUT:")
        print(result.stdout)
        
        if result.stderr:
            print("STDERR:")
            print(result.stderr)
        
        # Check for expected outputs
        success = (
            "2 tools available: dummy_tool, echostring" in result.stdout and
            "hello world, hello world, hello world for SURE" in result.stdout
        )
        
        if success:
            print("✅ Level 0030 MCP Integration: PASSED")
            print("   - MCP tools discovered correctly")
            print("   - echostring tool executed successfully")
        else:
            print("❌ Level 0030 MCP Integration: FAILED")
            
        return success
        
    except subprocess.TimeoutExpired:
        print("❌ Test timed out")
        return False
        
    finally:
        # Clean up server
        print("4. Cleaning up MCP server...")
        server_proc.terminate()
        try:
            server_proc.wait(timeout=5)
        except subprocess.TimeoutExpired:
            server_proc.kill()


if __name__ == "__main__":
    sys.exit(0 if test_mcp_integration() else 1)