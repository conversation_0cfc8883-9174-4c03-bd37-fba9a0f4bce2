"""
Gaia Ceto v2 Interfaces - Protocol Adapters

This package contains interface adapters that expose the core business logic
through different protocols and frameworks.

Interface Adapters:
- http_server.py - Simple HTTP REST API
- django_views.py - Django REST framework integration  
- mcp_server.py - MCP (Model Context Protocol) tools
- cli.py - Command-line interface

Each interface is a thin adapter that:
1. Handles protocol-specific concerns (HTTP, MCP, Django, etc.)
2. Translates between protocol formats and core business objects
3. Contains NO business logic - just protocol translation
4. Depends on core package, never the reverse

This design allows the core business logic to remain framework-agnostic
while supporting multiple client interfaces simultaneously.
"""

__version__ = "2.0.0-interfaces"