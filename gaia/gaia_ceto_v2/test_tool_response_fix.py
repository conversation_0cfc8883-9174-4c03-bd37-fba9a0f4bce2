#!/usr/bin/env python3
"""
Test that ToolResponse handling works with SimpleToolRegistry
"""

import sys
sys.path.insert(0, '.')

from tools.simple_tools import SimpleToolRegistry
from core.tool_response import ToolResponse, handle_tool_response

def test_tool_response_handling():
    """Test that SimpleToolRegistry + ToolResponse works correctly."""
    
    # Create a mock tool that returns a ToolResponse
    def mock_research_tool(query: str) -> ToolResponse:
        return ToolResponse(
            content=f"Research results for '{query}': [detailed findings here]",
            include_in_context=True,
            needs_synthesis=False,  # ← Key setting: no LLM summarization needed
            show_to_user=True,
            store_as_document=True,
            content_format="research_report"
        )
    
    # Register the mock tool
    registry = SimpleToolRegistry()
    registry.add(
        "mock_research",
        mock_research_tool,
        {
            "type": "object",
            "properties": {
                "query": {"type": "string", "description": "Research query"}
            },
            "required": ["query"]
        },
        "Mock research tool that returns ToolResponse"
    )
    
    print("=== Testing ToolResponse Handling ===")
    
    # Call the tool
    result = registry.call("mock_research", query="test query")
    print(f"1. Tool result type: {type(result)}")
    print(f"2. Is ToolResponse: {isinstance(result, ToolResponse)}")
    
    if isinstance(result, ToolResponse):
        print(f"3. needs_synthesis: {result.needs_synthesis}")
        print(f"4. show_to_user: {result.show_to_user}")
        print(f"5. content preview: {result.content[:50]}...")
        
        # Test the handle_tool_response function
        processed = handle_tool_response("mock_research", result)
        print(f"6. Processed result keys: {list(processed.keys())}")
        print(f"7. needs_synthesis in processed: {processed['needs_synthesis']}")
        print(f"8. Content for display: {processed['content'][:50] if processed['content'] else 'None'}...")
        
        return True
    else:
        print("❌ Tool did not return ToolResponse object!")
        return False

if __name__ == "__main__":
    success = test_tool_response_handling()
    if success:
        print("✅ ToolResponse handling test PASSED")
    else:
        print("❌ ToolResponse handling test FAILED")
        sys.exit(1)