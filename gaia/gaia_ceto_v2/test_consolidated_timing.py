#!/usr/bin/env python3
"""
Test Consolidated Level 0004 Timing System

Verifies that all timing code across the codebase is now consolidated 
and using the Level 0004 simple timing system.
"""

import json
import time
from pathlib import Path
from datetime import datetime

from core.level_0004_simple import (
    log_timing, 
    timer,
    log_existing_timing,
    generate_timing_id
)


def test_consolidated_timing_log():
    """Test that all timing entries go to the centralized timing log."""
    print("🧪 Testing consolidated timing system...")
    
    # Clear timing log for clean test
    timing_log_path = Path('/tmp/gaia_logs/ceto/timing.log')
    if timing_log_path.exists():
        with open(timing_log_path, 'w') as f:
            f.write('')  # Clear file
    
    # Test entries from different sources
    print("1. Simulating timing entries from different services:")
    
    # Simulate LLM wrapper timing
    req_id1 = generate_timing_id("llm")
    start_time1 = datetime.now()
    time.sleep(0.05)
    log_existing_timing(req_id1, "llm_call_success", start_time1, {
        "model": "gpt-4", "vendor": "openai", "char_count": 150, "success": True
    })
    print("   ✓ LLM timing logged")
    
    # Simulate Gemini wrapper timing
    req_id2 = generate_timing_id("gemini")
    start_time2 = datetime.now()
    time.sleep(0.03)
    log_existing_timing(req_id2, "gemini_with_tools", start_time2, {
        "model": "gemini-2.0-flash", "tool_calls_count": 2, "success": True
    })
    print("   ✓ Gemini timing logged")
    
    # Simulate chat manager timing  
    req_id3 = generate_timing_id("chat")
    start_time3 = datetime.now()
    time.sleep(0.02)
    log_existing_timing(req_id3, "chat_send_message", start_time3, {
        "conversation_id": "test_conv_123", "message_length": 50, "success": True
    })
    print("   ✓ Chat timing logged")
    
    # Simulate MCP timing
    req_id4 = generate_timing_id("mcp")
    with timer("mcp_call_success", req_id4, tool_name="test_tool", server_id="test_server"):
        time.sleep(0.01)
    print("   ✓ MCP timing logged")
    
    # Simulate service wrapper timing
    req_id5 = generate_timing_id("svc")
    log_timing(req_id5, "test_service_operation", 25.5, {
        "service": "test_service", "operation": "test_op", "success": True
    })
    print("   ✓ Service wrapper timing logged")
    
    print("✅ All timing entries created")
    return [req_id1, req_id2, req_id3, req_id4, req_id5]


def verify_timing_log(expected_request_ids):
    """Verify that all timing entries are in the centralized log."""
    print("\n📋 Verifying centralized timing log...")
    
    timing_log_path = Path('/tmp/gaia_logs/ceto/timing.log')
    
    if not timing_log_path.exists():
        print("❌ Timing log not found!")
        return False
    
    with open(timing_log_path, 'r') as f:
        lines = f.readlines()
    
    print(f"📊 Found {len(lines)} total timing entries")
    
    # Parse entries and verify our test entries are present
    found_request_ids = set()
    operations_found = []
    
    for line in lines:
        if 'TIMING -' in line:
            try:
                # Extract JSON part
                json_start = line.find('- {')
                if json_start > 0:
                    json_part = line[json_start + 2:].strip()
                    data = json.loads(json_part)
                    
                    request_id = data.get('request_id', '')
                    operation = data.get('operation', '')
                    duration = data.get('duration_ms', 0)
                    
                    if any(req_id in request_id for req_id in expected_request_ids):
                        found_request_ids.add(request_id)
                        operations_found.append({
                            'request_id': request_id,
                            'operation': operation,
                            'duration_ms': duration,
                            'metadata': {k: v for k, v in data.items() 
                                       if k not in ['request_id', 'operation', 'duration_ms', 'timestamp']}
                        })
                        
            except (json.JSONDecodeError, Exception) as e:
                # Skip malformed entries
                continue
    
    print(f"🔍 Found {len(operations_found)} test timing entries:")
    for entry in operations_found:
        print(f"   • {entry['operation']}: {entry['duration_ms']:.1f}ms "
              f"(req: {entry['request_id'][:15]}...)")
        if entry['metadata']:
            print(f"     Meta: {entry['metadata']}")
    
    # Verify we have entries from all major services
    operation_types = {entry['operation'].split('_')[0] for entry in operations_found}
    expected_types = {'llm', 'gemini', 'chat', 'mcp', 'test'}
    
    print(f"\n📈 Operation types found: {operation_types}")
    
    if expected_types.issubset(operation_types):
        print("✅ All expected timing sources are logging to centralized system")
        return True
    else:
        missing = expected_types - operation_types
        print(f"❌ Missing timing from: {missing}")
        return False


def analyze_timing_patterns():
    """Analyze timing patterns in the consolidated log."""
    print("\n📊 Analyzing timing patterns...")
    
    timing_log_path = Path('/tmp/gaia_logs/ceto/timing.log')
    
    with open(timing_log_path, 'r') as f:
        lines = f.readlines()
    
    # Parse all timing entries
    entries = []
    for line in lines:
        if 'TIMING -' in line:
            try:
                json_start = line.find('- {')
                if json_start > 0:
                    json_part = line[json_start + 2:].strip()
                    data = json.loads(json_part)
                    entries.append(data)
            except:
                continue
    
    if not entries:
        print("❌ No timing entries found for analysis")
        return
    
    # Analyze patterns
    operations = {}
    for entry in entries:
        op = entry.get('operation', 'unknown')
        duration = entry.get('duration_ms', 0)
        
        if op not in operations:
            operations[op] = []
        operations[op].append(duration)
    
    print("📈 Timing Analysis:")
    for op, durations in operations.items():
        if durations:
            avg = sum(durations) / len(durations)
            min_dur = min(durations)
            max_dur = max(durations)
            count = len(durations)
            
            print(f"   • {op}: {count} calls, avg {avg:.1f}ms, "
                  f"range {min_dur:.1f}-{max_dur:.1f}ms")
    
    # Check for request ID correlation
    request_ids = {entry.get('request_id') for entry in entries if entry.get('request_id')}
    print(f"\n🔗 Request ID correlation: {len(request_ids)} unique request IDs")
    
    # Look for multi-operation requests (same request_id, different operations)
    request_ops = {}
    for entry in entries:
        req_id = entry.get('request_id')
        op = entry.get('operation')
        if req_id and op:
            if req_id not in request_ops:
                request_ops[req_id] = []
            request_ops[req_id].append(op)
    
    multi_op_requests = {req_id: ops for req_id, ops in request_ops.items() if len(ops) > 1}
    if multi_op_requests:
        print(f"🔄 Found {len(multi_op_requests)} requests with multiple operations:")
        for req_id, ops in list(multi_op_requests.items())[:3]:  # Show first 3
            print(f"   • {req_id[:15]}...: {ops}")
    
    print("✅ Timing pattern analysis complete")


def main():
    """Run consolidated timing system test."""
    print("🚀 Level 0004 Consolidated Timing System Test")
    print("=" * 60)
    
    # Ensure log directory exists
    Path('/tmp/gaia_logs/ceto').mkdir(parents=True, exist_ok=True)
    
    try:
        # Test consolidated logging
        request_ids = test_consolidated_timing_log()
        
        # Verify all entries are in centralized log
        success = verify_timing_log(request_ids)
        
        # Analyze patterns in existing timing data
        analyze_timing_patterns()
        
        print("\n" + "=" * 60)
        if success:
            print("✅ Level 0004 consolidated timing system is working correctly!")
            print("📝 All timing data is centralized in: /tmp/gaia_logs/ceto/timing.log")
            print("🔗 Request IDs provide correlation across different services")
            print("📊 Structured JSON format enables easy analysis and monitoring")
        else:
            print("❌ Some issues found with timing consolidation")
        
    except Exception as e:
        print(f"\n❌ Test suite failed with error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()