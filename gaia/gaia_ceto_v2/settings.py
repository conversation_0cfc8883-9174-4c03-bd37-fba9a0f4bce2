"""
Centralized settings for GAIA CETO v2 paths and configuration.
Uses Pydantic for validation and environment variable loading.

Usage:
    from gaia_ceto_v2.settings import GAIA_SETTINGS
    
    # Access paths - EASY TO GREP!
    cache_dir = GAIA_SETTINGS.GAIA_CACHE_DIR
    logs_dir = GAIA_SETTINGS.GAIA_LOGS_DIR
    
    # Or get all paths
    all_paths = GAIA_SETTINGS.get_all_paths()
"""

import os
from pathlib import Path
from typing import Optional, Dict
from pydantic import Field, validator
from pydantic_settings import BaseSettings


class GaiaSettings(BaseSettings):
    """
    GAIA CETO v2 Settings with exact environment variable names.
    
    All settings use EXACT env var names (e.g., GAIA_CACHE_DIR not cache_dir)
    to avoid confusion and make grepping easier.
    
    To find usages in code, grep for:
        GAIA_SETTINGS.GAIA_CACHE_DIR
        GAIA_SETTINGS.GAIA_LOGS_DIR
        etc.
    """
    
    # Base directory - can override all paths at once
    GAIA_BASE_DIR: str = Field(
        default="/tmp",
        description="Base directory for all GAIA data. Can be overridden by individual path env vars."
    )
    
    # Individual path settings with EXACT env var names
    GAIA_CACHE_DIR: Optional[str] = Field(
        default=None,
        description="Directory for cache files. Default: {GAIA_BASE_DIR}/gaia_cache"
    )
    
    GAIA_LOGS_DIR: Optional[str] = Field(
        default=None,
        description="Directory for log files. Default: {GAIA_BASE_DIR}/gaia_logs/ceto"
    )
    
    GAIA_DOCS_DIR: Optional[str] = Field(
        default=None,
        description="Directory for document storage. Default: {GAIA_BASE_DIR}/gaia_docs"
    )
    
    GAIA_CONVERSATIONS_DIR: Optional[str] = Field(
        default=None,
        description="Directory for conversation storage. Default: {GAIA_BASE_DIR}/gaia_conversations"
    )
    
    GAIA_ACCOUNTING_DIR: Optional[str] = Field(
        default=None,
        description="Directory for accounting data. Default: {GAIA_BASE_DIR}/gaia_accounting"
    )
    
    class Config:
        """Pydantic configuration"""
        # Look for .env file
        env_file = ".env"
        env_file_encoding = "utf-8"
        
        # Keep env var names as-is (don't lowercase)
        case_sensitive = True
        
        # Allow field assignment after initialization
        allow_mutation = True
    
    @validator('GAIA_CACHE_DIR', pre=True, always=True)
    def set_cache_dir(cls, v, values):
        """Set default based on GAIA_BASE_DIR if not provided"""
        if v is None:
            base = values.get('GAIA_BASE_DIR', '/tmp')
            return os.path.join(base, 'gaia_cache')
        return v
    
    @validator('GAIA_LOGS_DIR', pre=True, always=True)
    def set_logs_dir(cls, v, values):
        """Set default based on GAIA_BASE_DIR if not provided"""
        if v is None:
            base = values.get('GAIA_BASE_DIR', '/tmp')
            return os.path.join(base, 'gaia_logs', 'ceto')
        return v
    
    @validator('GAIA_DOCS_DIR', pre=True, always=True)
    def set_docs_dir(cls, v, values):
        """Set default based on GAIA_BASE_DIR if not provided"""
        if v is None:
            base = values.get('GAIA_BASE_DIR', '/tmp')
            return os.path.join(base, 'gaia_docs')
        return v
    
    @validator('GAIA_CONVERSATIONS_DIR', pre=True, always=True)
    def set_conversations_dir(cls, v, values):
        """Set default based on GAIA_BASE_DIR if not provided"""
        if v is None:
            base = values.get('GAIA_BASE_DIR', '/tmp')
            return os.path.join(base, 'gaia_conversations')
        return v
    
    @validator('GAIA_ACCOUNTING_DIR', pre=True, always=True)
    def set_accounting_dir(cls, v, values):
        """Set default based on GAIA_BASE_DIR if not provided"""
        if v is None:
            base = values.get('GAIA_BASE_DIR', '/tmp')
            return os.path.join(base, 'gaia_accounting')
        return v
    
    def ensure_directories(self) -> None:
        """Create all directories if they don't exist"""
        for path in [
            self.GAIA_CACHE_DIR,
            self.GAIA_LOGS_DIR,
            self.GAIA_DOCS_DIR,
            self.GAIA_CONVERSATIONS_DIR,
            self.GAIA_ACCOUNTING_DIR
        ]:
            Path(path).mkdir(parents=True, exist_ok=True)
    
    def get_all_paths(self) -> Dict[str, str]:
        """
        Get all configured paths as a dictionary.
        
        Returns:
            Dict with keys: cache, logs, docs, conversations, accounting
        """
        return {
            'cache': self.GAIA_CACHE_DIR,
            'logs': self.GAIA_LOGS_DIR,
            'docs': self.GAIA_DOCS_DIR,
            'conversations': self.GAIA_CONVERSATIONS_DIR,
            'accounting': self.GAIA_ACCOUNTING_DIR
        }
    
    def print_config(self) -> None:
        """Print current configuration for debugging"""
        print("GAIA CETO v2 Path Configuration:")
        print(f"  Base Directory:    {self.GAIA_BASE_DIR}")
        print(f"  Cache Directory:   {self.GAIA_CACHE_DIR}")
        print(f"  Logs Directory:    {self.GAIA_LOGS_DIR}")
        print(f"  Docs Directory:    {self.GAIA_DOCS_DIR}")
        print(f"  Conversations Dir: {self.GAIA_CONVERSATIONS_DIR}")
        print(f"  Accounting Dir:    {self.GAIA_ACCOUNTING_DIR}")


# Create global settings instance
# This is what should be imported everywhere
GAIA_SETTINGS = GaiaSettings()


# For backward compatibility with existing env var usage
# These can be gradually phased out
def get_cache_dir() -> str:
    """Get cache directory - backward compatibility"""
    return GAIA_SETTINGS.GAIA_CACHE_DIR


def get_logs_dir() -> str:
    """Get logs directory - backward compatibility"""
    return GAIA_SETTINGS.GAIA_LOGS_DIR


def get_docs_dir() -> str:
    """Get docs directory - backward compatibility"""
    return GAIA_SETTINGS.GAIA_DOCS_DIR


def get_conversations_dir() -> str:
    """Get conversations directory - backward compatibility"""
    return GAIA_SETTINGS.GAIA_CONVERSATIONS_DIR


def get_accounting_dir() -> str:
    """Get accounting directory - backward compatibility"""
    return GAIA_SETTINGS.GAIA_ACCOUNTING_DIR


if __name__ == "__main__":
    # Test/debug - print current configuration
    GAIA_SETTINGS.print_config()
    
    # Ensure all directories exist
    GAIA_SETTINGS.ensure_directories()
    print("\nAll directories created/verified.")