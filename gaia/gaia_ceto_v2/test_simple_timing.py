#!/usr/bin/env python3
"""
Test Simple Level 0004 Timing

Quick test of the simplified timing system.
"""

import time
from datetime import datetime
from pathlib import Path

from core.level_0004_simple import (
    log_timing,
    time_start, 
    time_end,
    generate_timing_id,
    timer,
    log_existing_timing
)


def test_simple_timing():
    """Test the simple timing functions."""
    print("🧪 Testing simple Level 0004 timing...")
    
    # Test 1: Manual timing
    print("1. Manual timing:")
    req_id = generate_timing_id("test")
    start = time_start()
    time.sleep(0.05)
    duration = time_end(start)
    log_timing(req_id, "manual_test", duration, {"method": "manual"})
    print(f"   Duration: {duration:.1f}ms")
    
    # Test 2: Context manager
    print("2. Context manager:")
    with timer("context_test", metadata={"method": "context"}):
        time.sleep(0.03)
        print("   Context operation completed")
    
    # Test 3: Existing pattern integration
    print("3. Existing datetime pattern:")
    req_id2 = generate_timing_id("existing")
    start_dt = datetime.now()
    time.sleep(0.02)
    log_existing_timing(req_id2, "existing_pattern", start_dt, {"method": "datetime"})
    print("   Existing pattern logged")
    
    # Test 4: Error handling
    print("4. Error handling:")
    try:
        with timer("error_test", metadata={"will_fail": True}):
            time.sleep(0.01)
            raise ValueError("Test error")
    except ValueError:
        print("   Error caught and timed")
    
    print("✅ Simple timing tests completed")


def check_log():
    """Check timing log contents."""
    print("\n📋 Checking timing log...")
    
    log_path = Path('/tmp/gaia_logs/ceto/timing.log')
    if log_path.exists():
        with open(log_path, 'r') as f:
            lines = f.readlines()
        
        print(f"📊 {len(lines)} timing entries")
        print("Last 3 entries:")
        for line in lines[-3:]:
            if 'TIMING -' in line:
                print(f"   {line.strip()}")
    else:
        print("❌ No timing log found")


if __name__ == "__main__":
    print("🚀 Simple Level 0004 Timing Test")
    print("=" * 40)
    
    Path('/tmp/gaia_logs/ceto').mkdir(parents=True, exist_ok=True)
    
    test_simple_timing()
    check_log()
    
    print("\n✅ Simple timing test complete!")
    print("📝 See: /tmp/gaia_logs/ceto/timing.log")