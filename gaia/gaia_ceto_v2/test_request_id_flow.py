#!/usr/bin/env python3
"""
Test script to verify request_id flows from chat_manager to accounting system.
"""

import sys
import os
from pathlib import Path

# Add current directory to path for imports
sys.path.insert(0, str(Path(__file__).parent))

from core import create_chat_manager, create_llm_provider
from core.mcp_integration import <PERSON><PERSON><PERSON><PERSON><PERSON>and<PERSON>
from gaia_accounting.system import AccountingSystem

def test_request_id_flow():
    """Test that request_id flows through the system correctly."""
    print("🧪 Testing request_id flow...")
    
    # Create components
    accounting = AccountingSystem()
    mcp_handler = MCPToolHandler(accounting_system=accounting)
    
    # Test direct MCP tool call with request_id
    print("\n1. Testing MCP tool call with explicit request_id...")
    test_request_id = "test-request-123"
    result = mcp_handler.execute_single_tool(
        tool_name="echostring", 
        args={"text": "Hello request_id test"},
        request_id=test_request_id
    )
    print(f"   Tool result: {result}")
    
    # Check recent accounting entries
    print("\n2. Checking accounting entries...")
    recent_calls = accounting.get_calls(days=1)
    
    if recent_calls:
        latest_call = recent_calls[-1]  # Get most recent
        print(f"   Latest call_id: {latest_call.call_id}")
        print(f"   Latest request_id: {latest_call.request_id}")
        print(f"   Provider: {latest_call.provider}")
        print(f"   Service: {latest_call.model}")
        
        if latest_call.request_id == test_request_id:
            print("   ✅ request_id correctly recorded in accounting!")
        else:
            print(f"   ❌ request_id mismatch: expected '{test_request_id}', got '{latest_call.request_id}'")
    else:
        print("   ❌ No recent accounting entries found")
    
    print("\n3. Testing full chat manager flow...")
    # Create chat manager with real provider that calls accounting
    llm_provider = create_llm_provider(
        'gemini',  # Use real provider if API key available, otherwise fallback
        accounting_system=accounting
    )
    chat_manager = create_chat_manager(
        llm_provider=llm_provider,
        accounting_system=accounting
    )
    
    # Create conversation and send message
    conversation_id = chat_manager.create_conversation("test_user")
    print(f"   Created conversation: {conversation_id}")
    
    # This should generate a request_id and pass it through
    try:
        response = chat_manager.send_message(conversation_id, "Hello world")
        print(f"   Response: {response[:50]}...")
        
        # Check if new entry was added with request_id
        new_calls = accounting.get_calls(days=1)
        if len(new_calls) > len(recent_calls):
            newest_call = new_calls[-1]
            print(f"   New call request_id: {newest_call.request_id}")
            if newest_call.request_id:
                print("   ✅ Chat manager generated and passed request_id!")
            else:
                print("   ❌ Chat manager did not pass request_id")
        else:
            print("   ℹ️  No new accounting entries (likely using MockLLM)")
            
    except Exception as e:
        print(f"   ⚠️  Chat error: {e}")
    
    print("\n🎯 request_id flow test complete!")

if __name__ == "__main__":
    test_request_id_flow()