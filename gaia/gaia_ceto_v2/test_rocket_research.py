#!/usr/bin/env python3
"""Test script to debug the rocket research query"""

import subprocess
import sys

# Run the terminal chat with the query
process = subprocess.Popen(
    [sys.executable, 'terminal_chat.py', '--verbose', '--provider', 'gemini', '--with-mcp'],
    stdin=subprocess.PIPE,
    stdout=subprocess.PIPE,
    stderr=subprocess.STDOUT,
    text=True
)

# Send the query and exit
output, _ = process.communicate("research rocket ships\nexit\n")

# Print relevant lines
for line in output.splitlines():
    if any(marker in line for marker in ['🔧', '🤖', 'Tool:', 'Args:', 'exa_research', 'DEBUG']):
        print(line)