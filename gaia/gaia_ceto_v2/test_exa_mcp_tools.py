#!/usr/bin/env python3
"""
Test what tools the official exa-mcp-server actually exposes
"""

import subprocess
import json
import os
import sys
import time
from pathlib import Path

def test_exa_mcp_tools():
    """Test the official exa-mcp-server to see what tools it exposes."""
    
    print("🔍 TESTING OFFICIAL EXA-MCP-SERVER TOOLS")
    print("=" * 50)
    
    # Check if EXA_API_KEY is set
    api_key = os.getenv("EXA_API_KEY")
    if not api_key:
        print("❌ EXA_API_KEY not set - server may not work properly")
        return False
    else:
        print(f"✅ EXA_API_KEY found (ends with: ...{api_key[-6:]})")
    
    try:
        # Set up environment with EXA_API_KEY
        env = os.environ.copy()
        env["EXA_API_KEY"] = api_key
        
        print("\n🚀 Starting exa-mcp-server...")
        
        # Start the exa-mcp-server process
        proc = subprocess.Popen(
            ["npx", "exa-mcp-server"],
            stdin=subprocess.PIPE,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            env=env,
            text=True,
            bufsize=0
        )
        
        # Give it a moment to start
        time.sleep(2)
        
        print("📡 Sending MCP initialize request...")
        
        # Send MCP initialize request
        init_request = {
            "jsonrpc": "2.0",
            "id": 1,
            "method": "initialize",
            "params": {
                "protocolVersion": "2024-11-05",
                "capabilities": {
                    "tools": {}
                },
                "clientInfo": {
                    "name": "test-client",
                    "version": "1.0.0"
                }
            }
        }
        
        # Send the request
        proc.stdin.write(json.dumps(init_request) + "\n")
        proc.stdin.flush()
        
        # Read response
        response_line = proc.stdout.readline()
        if response_line:
            try:
                init_response = json.loads(response_line.strip())
                print(f"✅ Initialize response received")
                print(f"   Server: {init_response.get('result', {}).get('serverInfo', {}).get('name', 'Unknown')}")
                print(f"   Version: {init_response.get('result', {}).get('serverInfo', {}).get('version', 'Unknown')}")
            except json.JSONDecodeError as e:
                print(f"❌ Failed to parse init response: {e}")
                print(f"   Raw response: {response_line}")
        
        print("\n🛠️  Requesting tools list...")
        
        # Send tools/list request
        tools_request = {
            "jsonrpc": "2.0",
            "id": 2,
            "method": "tools/list",
            "params": {}
        }
        
        proc.stdin.write(json.dumps(tools_request) + "\n")
        proc.stdin.flush()
        
        # Read tools response
        response_line = proc.stdout.readline()
        if response_line:
            try:
                tools_response = json.loads(response_line.strip())
                tools = tools_response.get("result", {}).get("tools", [])
                
                print(f"📋 Found {len(tools)} tools:")
                for i, tool in enumerate(tools, 1):
                    name = tool.get("name", "Unknown")
                    description = tool.get("description", "No description")
                    print(f"  {i}. {name}")
                    print(f"     {description}")
                    
                    # Show input schema if available
                    input_schema = tool.get("inputSchema", {})
                    if input_schema and "properties" in input_schema:
                        props = list(input_schema["properties"].keys())
                        print(f"     Parameters: {', '.join(props)}")
                    print()
                
                return True
                
            except json.JSONDecodeError as e:
                print(f"❌ Failed to parse tools response: {e}")
                print(f"   Raw response: {response_line}")
                return False
        else:
            print("❌ No response received for tools/list")
            return False
            
    except Exception as e:
        print(f"❌ Error testing exa-mcp-server: {e}")
        return False
    finally:
        # Clean up process
        if 'proc' in locals():
            proc.terminate()
            proc.wait()
            print("🧹 Cleaned up exa-mcp-server process")

if __name__ == "__main__":
    success = test_exa_mcp_tools()
    print(f"\n{'🎉 TEST COMPLETED' if success else '💥 TEST FAILED'}")
    sys.exit(0 if success else 1)