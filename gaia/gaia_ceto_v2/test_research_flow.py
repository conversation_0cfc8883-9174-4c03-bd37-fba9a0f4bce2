#!/usr/bin/env python3
"""
Test the complete flow: research tool -> ToolResponse -> message processing
"""

import sys
sys.path.insert(0, '.')

# Define minimal classes to avoid import issues
class ToolResponse:
    def __init__(self, content, include_in_context=True, needs_synthesis=False, 
                 show_to_user=True, store_as_document=False, content_format='text', metadata=None):
        self.content = content
        self.include_in_context = include_in_context
        self.needs_synthesis = needs_synthesis
        self.show_to_user = show_to_user
        self.store_as_document = store_as_document
        self.content_format = content_format
        self.metadata = metadata or {}

def handle_tool_response_simple(tool_name, response):
    """Simplified version of handle_tool_response."""
    if isinstance(response, str):
        response = ToolResponse(content=response)
    
    return {
        'content': response.content if response.show_to_user else None,
        'context_content': response.content if response.include_in_context else None,
        'needs_synthesis': response.needs_synthesis,
        'document_id': getattr(response, 'document_id', None),
        'metadata': response.metadata
    }

def test_research_flow():
    """Test complete research tool flow."""
    print("=== Testing Research Tool Flow ===")
    
    # Import the SimpleToolRegistry
    try:
        from tools.simple_tools import SimpleToolRegistry
        print("✅ SimpleToolRegistry imported")
    except Exception as e:
        print(f"❌ Import failed: {e}")
        return False
    
    # Create a mock research tool that returns ToolResponse
    def mock_exa_research(query="", max_sources=10, use_schema=False, bypass_cache=False):
        """Mock research tool that behaves like the real one."""
        research_results = f"""Exa research for '{query}' completed:

Rocketships:
1. Saturn V - November 9, 1967 (first flight)
   Description: Saturn V is a three-stage liquid-fueled rocket developed under NASA's Apollo program. It was the most powerful rocket ever built at the time and successfully carried astronauts to the Moon.

2. Falcon 9 - June 4, 2010 (first flight)  
   Description: Falcon 9 is a partially reusable rocket developed by SpaceX. It features a first stage that can land and be reused, significantly reducing launch costs.

3. Space Shuttle - April 12, 1981 (first flight)
   Description: A partially reusable orbital spacecraft that served as the primary vehicle for NASA's human spaceflight missions for 30 years.

Key Findings:
- Rocket propulsion relies on Newton's third law of motion
- Multi-stage rockets improve efficiency by shedding mass as fuel is consumed
- Reusable rockets like Falcon 9 have revolutionized space access costs
- Liquid fuel rockets offer better control than solid fuel rockets

Sources consulted: 8 technical documents, 12 historical records"""
        
        return ToolResponse(
            content=research_results,
            include_in_context=True,
            needs_synthesis=False,  # ← KEY: No LLM summarization needed
            show_to_user=True,
            store_as_document=True,
            content_format="research_report"
        )
    
    # Register the mock tool
    registry = SimpleToolRegistry()
    registry.add(
        "exa_research",
        mock_exa_research,
        {
            "type": "object",
            "properties": {
                "query": {"type": "string", "description": "Research query"},
                "max_sources": {"type": "integer", "default": 10},
                "use_schema": {"type": "boolean", "default": False},
                "bypass_cache": {"type": "boolean", "default": False}
            },
            "required": ["query"]
        },
        "Conduct deep research using Exa AI research endpoint"
    )
    
    print(f"✅ Mock exa_research registered. Available tools: {registry.list_tools()}")
    
    # Simulate the message service flow
    print("\n--- Simulating Message Service Flow ---")
    
    # 1. LLM calls tool
    print("1. LLM calls exa_research tool...")
    raw_result = registry.call("exa_research", query="rocket ships")
    print(f"   Tool result type: {type(raw_result)}")
    print(f"   Is ToolResponse: {isinstance(raw_result, ToolResponse)}")
    
    if not isinstance(raw_result, ToolResponse):
        print("❌ Tool should return ToolResponse object")
        return False
    
    # 2. Process with handle_tool_response
    print("2. Processing tool response...")
    processed = handle_tool_response_simple("exa_research", raw_result)
    print(f"   needs_synthesis: {processed['needs_synthesis']}")
    print(f"   show_to_user: {processed['content'] is not None}")
    print(f"   content preview: {processed['content'][:100] if processed['content'] else 'None'}...")
    
    # 3. Check if synthesis is needed
    if processed['needs_synthesis']:
        print("3. ❌ WRONG: Tool result needs LLM synthesis (should be False)")
        return False
    else:
        print("3. ✅ CORRECT: Tool result does NOT need LLM synthesis")
        print("   → Research results should be shown verbatim to user")
    
    # 4. Final content for user
    final_content = processed['content']
    if final_content and len(final_content) > 200:
        print(f"4. ✅ Research results ready for user ({len(final_content)} chars)")
        print("   Preview:")
        print("   " + "\n   ".join(final_content.split('\n')[:5]))
        print("   ...")
        return True
    else:
        print("4. ❌ No research content generated")
        return False

if __name__ == "__main__":
    success = test_research_flow()
    if success:
        print("\n🎉 Research tool flow test PASSED")
        print("✅ Research results should now be displayed verbatim (no LLM summarization)")
    else:
        print("\n❌ Research tool flow test FAILED")
        sys.exit(1)