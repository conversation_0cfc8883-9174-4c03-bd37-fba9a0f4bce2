#!/usr/bin/env python3
"""
Clean MCP Server Test - Kill any existing processes and start fresh
"""

import subprocess
import time
import sys
import signal
import os

def kill_port_processes(port):
    """Kill any processes using the specified port."""
    try:
        # Find processes using the port
        result = subprocess.run(['lsof', '-i', f':{port}'], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            lines = result.stdout.strip().split('\n')[1:]  # Skip header
            pids = []
            
            for line in lines:
                if line.strip():
                    parts = line.split()
                    if len(parts) >= 2:
                        pid = parts[1]
                        if pid.isdigit():
                            pids.append(pid)
            
            for pid in pids:
                print(f"🔫 Killing process {pid} using port {port}")
                subprocess.run(['kill', '-9', pid], capture_output=True)
                
            if pids:
                time.sleep(1)  # Wait for processes to die
                print(f"✅ Cleared port {port}")
            else:
                print(f"✅ Port {port} was already free")
        else:
            print(f"✅ Port {port} is free")
            
    except Exception as e:
        print(f"⚠️  Error checking port {port}: {e}")

def test_mcp_server():
    """Test the MCP server after clearing the port."""
    port = 9999
    
    print("🧹 CLEANING UP MCP SERVER PROCESSES")
    print("=" * 50)
    
    # Kill any existing processes on port 9999
    kill_port_processes(port)
    
    print(f"\n🚀 STARTING FRESH MCP SERVER ON PORT {port}")
    print("=" * 50)
    
    # Start the MCP server
    try:
        proc = subprocess.Popen([
            sys.executable, 'tests/mcp_interface/dummy_mcp_server.py', str(port)
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
        
        print(f"Started MCP server process {proc.pid}")
        
        # Wait for startup
        time.sleep(3)
        
        # Test if it's running
        result = subprocess.run(['lsof', '-i', f':{port}'], 
                              capture_output=True, text=True)
        
        if result.returncode == 0 and str(proc.pid) in result.stdout:
            print(f"✅ MCP server is running on port {port}")
            print(f"🌐 Server URL: http://127.0.0.1:{port}/mcp/")
            
            # Test HTTP endpoint
            try:
                import requests
                response = requests.get(f"http://127.0.0.1:{port}/mcp/", timeout=3)
                print(f"✅ HTTP response: {response.status_code}")
            except:
                print("⚠️  HTTP test failed (server may still be starting)")
            
            print(f"\n🎯 MCP server ready! PID: {proc.pid}")
            print("Press Ctrl+C to stop...")
            
            # Keep running until interrupted
            try:
                proc.wait()
            except KeyboardInterrupt:
                print("\n🛑 Stopping MCP server...")
                proc.terminate()
                proc.wait()
                print("✅ MCP server stopped")
                
        else:
            print("❌ Failed to start MCP server")
            proc.terminate()
            return False
            
    except Exception as e:
        print(f"❌ Error starting MCP server: {e}")
        return False
    
    return True

if __name__ == "__main__":
    os.chdir('/usr/local/agfunder/agbase_admin/gaia/gaia_ceto_v2')
    success = test_mcp_server()
    sys.exit(0 if success else 1)