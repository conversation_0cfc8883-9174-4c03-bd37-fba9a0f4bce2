#!/usr/bin/env python3
"""
Test the tool calling interface and integration without external dependencies.
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'core'))

from core.tool_calling_interface import (
    ToolCapableLLM, ToolCall, ToolResult, LLMResponse,
    create_universal_tool_schema, convert_mcp_to_universal
)
from core.chat_manager import create_chat_manager
from core.llm_providers import LLMProvider

class MockToolCapableLLM(ToolCapableLLM, LLMProvider):
    """Mock implementation of tool-capable LLM for testing."""
    
    def __init__(self):
        super().__init__()
        self.call_count = 0
    
    def supports_tools(self) -> bool:
        return True
    
    def get_model_name(self) -> str:
        return "mock-tool-llm"
    
    def generate_response(self, prompt: str, context, **kwargs):
        """Legacy interface - not used in tool-capable flow."""
        return f"Mock response to: {prompt}"
    
    def generate_with_tools(self, messages, available_tools, **kwargs) -> LLMResponse:
        """Generate response with potential tool calls."""
        self.call_count += 1
        
        # Check if user is asking for echostring
        last_message = messages[-1]['content'] if messages else ""
        
        if 'echo' in last_message.lower() and any(tool['name'] == 'echostring' for tool in available_tools):
            # Simulate LLM deciding to call echostring tool
            return LLMResponse(
                content=None,  # No text response yet
                tool_calls=[ToolCall(
                    id="mock_call_1",
                    name="echostring", 
                    args={"phrase": "Hello from mock tool calling!"}
                )],
                finish_reason="tool_calls",
                usage={"prompt_tokens": 100, "completion_tokens": 0, "total_tokens": 100}
            )
        else:
            # Regular text response
            return LLMResponse(
                content=f"Mock response to: {last_message}",
                tool_calls=[],
                finish_reason="stop", 
                usage={"prompt_tokens": 50, "completion_tokens": 20, "total_tokens": 70}
            )
    
    def continue_with_tool_results(self, messages, tool_results, **kwargs) -> LLMResponse:
        """Continue after tool execution."""
        # Simulate final response incorporating tool results
        results_summary = []
        for result in tool_results:
            if result.success:
                results_summary.append(f"{result.name}: {result.content}")
            else:
                results_summary.append(f"{result.name} failed: {result.error}")
        
        final_content = f"I executed tools and got: {'; '.join(results_summary)}"
        
        return LLMResponse(
            content=final_content,
            tool_calls=[],
            finish_reason="stop",
            usage={"prompt_tokens": 80, "completion_tokens": 30, "total_tokens": 110}
        )
    
    def convert_tools_schema(self, mcp_tools):
        """Mock schema conversion."""
        return mcp_tools  # No conversion needed for mock

def test_tool_interface():
    """Test the basic tool calling interface."""
    print("🧪 Testing Tool Calling Interface...")
    
    # Test data structures
    tool_call = ToolCall(id="test_1", name="echostring", args={"text": "test"})
    tool_result = ToolResult(tool_call_id="test_1", name="echostring", content="test")
    
    print(f"✅ ToolCall: {tool_call}")
    print(f"✅ ToolResult success: {tool_result.success}")
    
    # Test schema conversion
    mcp_tool = {
        "name": "echostring",
        "description": "Echo a phrase",
        "inputSchema": {
            "type": "object",
            "properties": {"phrase": {"type": "string"}},
            "required": ["phrase"]
        }
    }
    
    universal = convert_mcp_to_universal(mcp_tool)
    print(f"✅ Universal schema: {universal}")
    
    return True

def test_mock_tool_capable_llm():
    """Test the mock tool-capable LLM."""
    print("\n🤖 Testing Mock Tool-Capable LLM...")
    
    mock_llm = MockToolCapableLLM()
    print(f"✅ Supports tools: {mock_llm.supports_tools()}")
    
    # Test with tools available
    messages = [{"role": "user", "content": "Please echo hello world"}]
    tools = [{
        "name": "echostring",
        "description": "Echo a phrase", 
        "parameters": {"type": "object", "properties": {"phrase": {"type": "string"}}}
    }]
    
    response = mock_llm.generate_with_tools(messages, tools)
    print(f"✅ Tool call response: {response}")
    print(f"✅ Has tool calls: {response.has_tool_calls}")
    
    if response.has_tool_calls:
        # Simulate tool execution
        tool_results = [ToolResult(
            tool_call_id=response.tool_calls[0].id,
            name=response.tool_calls[0].name,
            content="Hello from mock tool calling!"
        )]
        
        final_response = mock_llm.continue_with_tool_results(messages, tool_results)
        print(f"✅ Final response: {final_response}")
    
    return True

def test_chat_manager_integration():
    """Test chat manager with tool-capable LLM."""
    print("\n🔗 Testing Chat Manager Integration...")
    
    # Create mock tool-capable LLM
    mock_tool_llm = MockToolCapableLLM()
    
    # Create chat manager with the mock LLM
    chat_manager = create_chat_manager(
        storage_dir='/tmp/test_tool_interface',
        llm_provider=mock_tool_llm,
        with_mcp=True  # This will try to connect to MCP but gracefully handle failure
    )
    
    print(f"✅ Chat manager created")
    print(f"✅ LLM is tool-capable: {isinstance(chat_manager.message_service.llm_provider, ToolCapableLLM)}")
    
    # Create conversation
    conversation_id = chat_manager.create_conversation("test_user", "Tool Test")
    print(f"✅ Created conversation: {conversation_id}")
    
    # Test message that should trigger tool calling
    response = chat_manager.send_message(conversation_id, "Please echo 'integration test'")
    print(f"✅ Response: {response}")
    
    return True

def main():
    """Main test runner."""
    print("🚀 Tool Calling Interface Test (No External Dependencies)")
    print("=" * 60)
    
    tests = [
        ("Tool Interface", test_tool_interface),
        ("Mock Tool-Capable LLM", test_mock_tool_capable_llm), 
        ("Chat Manager Integration", test_chat_manager_integration)
    ]
    
    results = {}
    for test_name, test_func in tests:
        print(f"\n{'=' * 15} {test_name} {'=' * 15}")
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ Test failed: {e}")
            import traceback
            traceback.print_exc()
            results[test_name] = False
    
    # Summary
    print(f"\n{'=' * 60}")
    print("📋 Test Summary:")
    
    passed = sum(results.values())
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  {status} {test_name}")
    
    print(f"\n🎯 Results: {passed}/{total} tests passed")
    return passed == total

if __name__ == '__main__':
    success = main()
    print(f"\n{'🎉 SUCCESS' if success else '⚠️ FAILURE'}: Tool calling interface {'working' if success else 'has issues'}")
    sys.exit(0 if success else 1)