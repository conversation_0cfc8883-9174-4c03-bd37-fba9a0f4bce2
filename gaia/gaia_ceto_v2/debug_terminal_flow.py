#!/usr/bin/env python3
"""
Debug script to trace why terminal_chat.py isn't generating logs.
"""

import sys
import os
from pathlib import Path

# Add current directory to path for imports
sys.path.insert(0, str(Path(__file__).parent))

from core import create_chat_manager, create_llm_provider

def test_terminal_flow():
    """Test the exact same flow as terminal_chat.py uses."""
    print("🔍 Debugging terminal chat flow...")
    
    # Clear existing logs
    log_dir = Path('/tmp/gaia_logs/ceto')
    if log_dir.exists():
        for log_file in log_dir.glob('*.log'):
            log_file.unlink()
    
    # Create the same components as terminal_chat.py
    print("\n1. Creating LLM provider (gemini)...")
    llm_provider = create_llm_provider('gemini')
    print(f"   Provider type: {type(llm_provider)}")
    print(f"   Model: {getattr(llm_provider, 'model_name', 'unknown')}")
    print(f"   Wrapper: {type(getattr(llm_provider, 'wrapper', None))}")
    
    print("\n2. Creating chat manager...")
    chat_manager = create_chat_manager(
        llm_provider=llm_provider,
        storage_dir='/tmp/gaia_conversations',
        max_context_messages=20
    )
    print(f"   Chat manager type: {type(chat_manager)}")
    
    print("\n3. Starting conversation...")
    conversation_id = chat_manager.create_conversation(
        user_id='debug_user',
        title='Debug Test'
    )
    print(f"   Conversation ID: {conversation_id}")
    
    print("\n4. Sending test message...")
    try:
        # This is the exact call that terminal_chat.py makes
        response = chat_manager.send_message(conversation_id, "echostring hello")
        print(f"   ✅ Response: {response[:100]}...")
    except Exception as e:
        print(f"   ❌ Error: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n5. Checking for log files...")
    if log_dir.exists():
        log_files = list(log_dir.glob('*.log'))
        print(f"   📂 Log directory: {log_dir}")
        print(f"   📄 Total log files: {len(log_files)}")
        
        if log_files:
            for log_file in sorted(log_files):
                size = log_file.stat().st_size
                print(f"      • {log_file.name} ({size} bytes)")
                if size > 0:
                    content = log_file.read_text().strip()
                    print(f"        Content: {content[:200]}...")
        else:
            print("   ❌ No log files created!")
    else:
        print("   ❌ Log directory doesn't exist!")
    
    print("\n6. Environment check...")
    gemini_key = os.getenv('GEMINI_API_KEY')
    print(f"   GEMINI_API_KEY: {'✅ Set' if gemini_key else '❌ Not set'}")
    if gemini_key:
        print(f"   Key preview: {gemini_key[:10]}...{gemini_key[-5:]}")
    
    print("\n🎯 Debug complete!")

if __name__ == "__main__":
    test_terminal_flow()