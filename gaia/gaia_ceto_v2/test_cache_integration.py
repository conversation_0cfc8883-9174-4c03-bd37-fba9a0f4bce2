#!/usr/bin/env python3
"""
Test script for Level 0008b cache integration.
"""

import subprocess
import sys
import time
import tempfile
import shutil
from pathlib import Path

def test_cache_functionality():
    """Test cache integration with terminal chat."""
    
    print("🧪 Testing Level 0008b Cache Integration")
    print("=" * 50)
    
    # Create temporary cache directory
    with tempfile.TemporaryDirectory() as temp_cache:
        print(f"1. Using temporary cache directory: {temp_cache}")
        
        # Start MCP server in background
        print("2. Starting MCP server...")
        server_proc = subprocess.Popen([
            sys.executable, "tests/mcp_interface/dummy_mcp_server.py", "9999"
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        # Wait for server to start
        time.sleep(3)
        
        try:
            # Test 1: LLM caching
            print("3. Testing LLM response caching...")
            
            # First call - should hit API
            result1 = subprocess.run([
                sys.executable, "terminal_chat.py",
                "--provider", "mock",
                "--cache-dir", temp_cache
            ], input="Hello world\nexit\n", text=True, capture_output=True, timeout=30)
            
            print("   First call output:")
            print("   " + "\n   ".join(result1.stdout.split('\n')[-10:]))
            
            # Check cache directory was created
            cache_path = Path(temp_cache)
            cache_files = list(cache_path.glob("*.json"))
            print(f"   Cache files created: {len(cache_files)}")
            
            if cache_files:
                # Second call - should hit cache
                print("4. Testing cache hit...")
                
                result2 = subprocess.run([
                    sys.executable, "terminal_chat.py", 
                    "--provider", "mock",
                    "--cache-dir", temp_cache
                ], input="Hello world\nexit\n", text=True, capture_output=True, timeout=30)
                
                if "Cache Hit" in result2.stdout:
                    print("   ✅ Cache hit detected!")
                else:
                    print("   ❌ Cache hit not detected")
                    print("   Output:", result2.stdout[-200:])
            
            # Test 2: MCP tool caching
            print("5. Testing MCP tool caching...")
            
            # First tool call
            result3 = subprocess.run([
                sys.executable, "terminal_chat.py",
                "--provider", "mock", 
                "--with-mcp", "--mcp-server", "http://127.0.0.1:9999/mcp/",
                "--cache-dir", temp_cache
            ], input="call echostring test123\nexit\n", text=True, capture_output=True, timeout=30)
            
            print("   Tool call output:")
            print("   " + "\n   ".join(result3.stdout.split('\n')[-8:]))
            
            # Check for more cache files
            new_cache_files = list(cache_path.glob("*.json"))
            print(f"   Total cache files: {len(new_cache_files)}")
            
            # Test 3: Cache stats
            print("6. Testing cache statistics...")
            
            # Import and test cache directly
            sys.path.insert(0, str(Path(__file__).parent))
            from core.simple_cache import SimpleCache
            
            cache = SimpleCache(temp_cache)
            stats = cache.stats()
            
            print(f"   Cache stats: {stats['total_entries']} entries, {stats['total_size_bytes']} bytes")
            print(f"   Entries by type: {stats['entries_by_type']}")
            
            # Test 4: Cache cleanup
            print("7. Testing cache cleanup...")
            expired_count = cache.cleanup_expired()
            print(f"   Expired entries cleaned: {expired_count}")
            
            # Test 5: No cache mode
            print("8. Testing --no-cache flag...")
            
            result4 = subprocess.run([
                sys.executable, "terminal_chat.py",
                "--provider", "mock",
                "--no-cache"
            ], input="Hello test\nexit\n", text=True, capture_output=True, timeout=30)
            
            if "TTL caching disabled" in result4.stdout:
                print("   ✅ No-cache mode working")
            else:
                print("   ❌ No-cache mode not working")
            
            print("\n✅ Level 0008b Cache Integration: TESTS COMPLETED")
            
        except subprocess.TimeoutExpired:
            print("❌ Test timed out")
            return False
        except Exception as e:
            print(f"❌ Test failed: {e}")
            return False
        finally:
            # Clean up server
            print("9. Cleaning up MCP server...")
            server_proc.terminate()
            try:
                server_proc.wait(timeout=5)
            except subprocess.TimeoutExpired:
                server_proc.kill()
    
    return True

if __name__ == "__main__":
    success = test_cache_functionality()
    sys.exit(0 if success else 1)