#!/usr/bin/env python3
"""
Test Log Inspector - LLM-Powered Quality Assessment Tool

Uses an LLM to intelligently analyze test logs and provide nuanced quality ratings.
Catches logical inconsistencies, empty responses, and subtle issues that string matching misses.

Usage:
    python test_log_inspector.py path/to/test.log
    python test_log_inspector.py tests/test_logs/gemini_integration_test_20250730_131038.log
    python test_log_inspector.py tests/test_logs/ --all  # Inspect all logs in directory
    
Output: JSON rating with scores 0-10 for:
- correctness: Test logic and assertions are sound
- completeness: All expected test scenarios covered  
- error_free: No unexpected errors or failures
- results_are_visible: Clear, readable output and progress indicators that show sufficient data to evaluate if the module is properly working or not
"""

import argparse
import json
import sys
import os
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime

# Try to import LLM components - they should be available from the same directory
try:
    from gaia_ceto_v2.core import create_llm_provider, LiteLLM
    HAS_LLM = True
except ImportError:
    try:
        # Fallback - direct import from core module in same directory
        from core import create_llm_provider, LiteLLM
        HAS_LLM = True
    except ImportError as e:
        HAS_LLM = False
        IMPORT_ERROR = str(e)


@dataclass
class TestLogAnalysis:
    """Analysis results for a test log."""
    correctness: int      # 0-10: Sound test logic and valid assertions
    completeness: int     # 0-10: All scenarios covered, no gaps
    error_free: int       # 0-10: No unexpected errors or failures  
    results_are_visible: int  # 0-10: Clear output, progress indicators
    
    reasoning: str = ""   # LLM's reasoning for the scores
    
    def to_dict(self) -> Dict:
        return {
            'ratings': {
                'correctness': self.correctness,
                'completeness': self.completeness, 
                'error_free': self.error_free,
                'results_are_visible': self.results_are_visible
            },
            'reasoning': self.reasoning
        }
    
    def average_score(self) -> float:
        return (self.correctness + self.completeness + self.error_free + self.results_are_visible) / 4


class LLMTestLogInspector:
    """Uses an LLM to intelligently analyze test logs."""
    
    def __init__(self, llm_provider=None):
        """Initialize with an LLM provider."""
        if not HAS_LLM:
            raise ImportError(f"Cannot import LLM components: {IMPORT_ERROR}")
        
        if llm_provider is None:
            # Try to create a default LLM provider
            try:
                # Check for explicit mock mode
                if os.getenv('USE_MOCK_LLM') == '1':
                    self.llm = create_llm_provider('mock')
                    # Override the mock to provide intelligent analysis
                    self.llm._mock_analysis = True
                    print("⚠️ Using MockLLM due to USE_MOCK_LLM=1", file=sys.stderr)
                # Then try Gemini
                elif os.getenv('GEMINI_API_KEY'):
                    self.llm = create_llm_provider('gemini', model_name='gemini-2.5-flash-lite')
                # First try Anthropic (best for analysis)
                elif os.getenv('ANTHROPIC_API_KEY'):
                    self.llm = create_llm_provider('anthropic', model_name='claude-3-5-sonnet-20240620')
                # Then try OpenAI  
                elif os.getenv('OPENAI_API_KEY'):
                    self.llm = create_llm_provider('openai', model_name='gpt-4')
                else:
                    # Fall back to mock for testing
                    self.llm = create_llm_provider('mock')
                    print("⚠️ No API keys found - using MockLLM (scores will be placeholder)", file=sys.stderr)
            except Exception as e:
                # Ultimate fallback
                self.llm = create_llm_provider('mock')
                print(f"⚠️ LLM creation failed: {e} - using MockLLM", file=sys.stderr)
        else:
            self.llm = llm_provider
    
    def inspect_log_file(self, log_path: Path) -> Tuple[TestLogAnalysis, Dict]:
        """Inspect a test log file using LLM analysis.
        
        Args:
            log_path: Path to the test log file
            
        Returns:
            Tuple of (analysis results, metadata)
        """
        if not log_path.exists():
            raise FileNotFoundError(f"Log file not found: {log_path}")
        
        with open(log_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Prepare the analysis prompt
        analysis_prompt = self._create_analysis_prompt(content)
        
        # Get LLM analysis
        try:
            # Check if we're using mock LLM for analysis
            if hasattr(self.llm, '_mock_analysis'):
                analysis = self._mock_analyze_log(content)
            else:
                llm_response = self.llm.generate_response(analysis_prompt, [])
                # Handle both old string format and new LLMResponse format
                if hasattr(llm_response, 'success'):
                    # New structured response
                    if llm_response.success:
                        response_text = llm_response.content
                    else:
                        response_text = f"LLM Error: {llm_response.error.message}"
                else:
                    # Old string format (backward compatibility)
                    response_text = str(llm_response)
                analysis = self._parse_llm_response(response_text)
        except Exception as e:
            # Fallback analysis if LLM fails
            analysis = TestLogAnalysis(
                correctness=0,
                completeness=0, 
                error_free=0,
                results_are_visible=0,
                reasoning=f"LLM analysis failed: {str(e)}"
            )
        
        # Basic metadata
        metadata = {
            'file_size_bytes': log_path.stat().st_size,
            'line_count': len(content.splitlines()),
            'llm_model': getattr(self.llm, 'model_name', 'unknown'),
            'analysis_method': 'llm_powered'
        }
        
        return analysis, metadata
    
    def _create_analysis_prompt(self, log_content: str) -> str:
        """Create a detailed prompt for LLM analysis."""
        
        prompt = f"""You are a senior software engineer analyzing a test log for quality assessment. 

Please analyze this test log and provide scores (0-10) for four criteria:

**CRITERIA:**
1. **correctness** (0-10): Are the test logic and assertions sound? Look for:
   - Logical inconsistencies (e.g., "No response generated" marked as "success")
   - Tests that claim to pass but show no actual functionality
   - Proper test assertions and validations
   - Sound test methodology

2. **completeness** (0-10): Are all expected test scenarios covered? Look for:
   - Multiple test types and scenarios
   - Edge cases and error conditions
   - Integration testing across components
   - Comprehensive test coverage

3. **error_free** (0-10): Are there unexpected errors or failures? Consider:
   - Unexpected exceptions or failures
   - API errors (expected vs unexpected)
   - Proper error handling
   - Clean execution without issues

4. **results_are_visible** (0-10): Is the output clear and informative? Look for:
   - Clear progress indicators and formatting
   - Detailed test output and results
   - Good structure and readability
   - Sufficient information to understand what happened

**CRITICAL**: Look for logical inconsistencies like claiming success when outputs show failure!

**TEST LOG:**
```
{log_content}
```

Please respond with ONLY a JSON object in this exact format:
{{
 "ratings" : {{
  "correctness": <score 0-10>,
  "completeness": <score 0-10>, 
  "error_free": <score 0-10>,
  "results_are_visible": <score 0-10>
 }},
 "reasoning": "<TELEGRAPHIC ultra-concise analysis: key issues found, logic flaws, missing coverage. Max 4-5 phrases, empty if no major comment.>"
}}"""

        return prompt
    
    def _mock_analyze_log(self, log_content: str) -> TestLogAnalysis:
        """Mock analysis that can detect the actual problems in the Gemini log."""
        lines = log_content.splitlines()
        
        # Look for the specific problem: "No response generated" marked as success
        no_response_lines = [line for line in lines if "No response generated" in line]
        success_claims = [line for line in lines if "✅ Successfully got real" in line or "Successfully got real" in line]
        
        # Detect the logical inconsistency
        has_logical_error = len(no_response_lines) > 0 and len(success_claims) > 0
        
        # Count test indicators  
        test_starts = len([line for line in lines if "🧪 Testing" in line])
        test_passes = len([line for line in lines if "✅" in line and ("passed" in line.lower() or "success" in line.lower())])
        api_errors = len([line for line in lines if "API key not valid" in line])
        progress_indicators = len([line for line in lines if any(emoji in line for emoji in ["👤", "🤖", "📝", "📊"])])
        
        # Score based on detected issues
        if has_logical_error:
            correctness = 2  # Major logical error
            reasoning = f"CRITICAL ISSUE: Found {len(no_response_lines)} instances of 'No response generated' but {len(success_claims)} claims of successful responses. This is a logical inconsistency - tests are claiming success when they're actually failing to get real responses."
        else:
            correctness = 8
            reasoning = "No major logical inconsistencies detected."
        
        # Completeness based on test variety
        if test_starts >= 4:
            completeness = 8
        elif test_starts >= 2:
            completeness = 6
        else:
            completeness = 3
            
        # Error-free based on API errors (expected) vs logical errors
        if has_logical_error:
            error_free = 3  # Logical errors are worse than API errors
        elif api_errors > 0:
            error_free = 7  # API errors are expected without keys
        else:
            error_free = 9
            
        # Visibility based on progress indicators
        if progress_indicators >= 15:
            results_are_visible = 8
        elif progress_indicators >= 5:
            results_are_visible = 6
        else:
            results_are_visible = 3
            
        return TestLogAnalysis(
            correctness=correctness,
            completeness=completeness,
            error_free=error_free,
            results_are_visible=results_are_visible,
            reasoning=reasoning + f" Stats: {test_starts} test starts, {test_passes} passes, {api_errors} API errors, {progress_indicators} progress indicators."
        )
    
    def _parse_llm_response(self, response: str) -> TestLogAnalysis:
        """Parse the LLM response into a TestLogAnalysis object."""
        try:
            # Extract JSON from response (handle cases where LLM adds extra text)
            response = response.strip()
            
            # Find JSON block
            start_idx = response.find('{')
            end_idx = response.rfind('}') + 1
            
            if start_idx != -1 and end_idx != -1:
                json_str = response[start_idx:end_idx]
                data = json.loads(json_str)
                
                ratings = data.get('ratings', {})
                return TestLogAnalysis(
                    correctness=max(0, min(10, int(ratings.get('correctness', 0)))),
                    completeness=max(0, min(10, int(ratings.get('completeness', 0)))),
                    error_free=max(0, min(10, int(ratings.get('error_free', 0)))),
                    results_are_visible=max(0, min(10, int(ratings.get('results_are_visible', 0)))),
                    reasoning=str(data.get('reasoning', 'No reasoning provided'))
                )
            else:
                raise ValueError("No JSON found in response")
                
        except (json.JSONDecodeError, ValueError, KeyError) as e:
            # Fallback parsing
            return TestLogAnalysis(
                correctness=0,
                completeness=0,
                error_free=0, 
                results_are_visible=0,
                reasoning=f"Failed to parse LLM response: {str(e)}. Raw response: {response[:200]}..."
            )


class FallbackInspector:
    """Simple fallback inspector when LLM is not available."""
    
    def inspect_log_file(self, log_path: Path) -> Tuple[TestLogAnalysis, Dict]:
        """Basic inspection without LLM."""
        if not log_path.exists():
            raise FileNotFoundError(f"Log file not found: {log_path}")
        
        with open(log_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        lines = content.splitlines()
        
        # Very basic analysis
        has_pass = any('✅' in line or 'passed' in line.lower() for line in lines)
        has_fail = any('❌' in line or 'failed' in line.lower() for line in lines)
        has_structure = len([l for l in lines if '=' in l]) > 3
        
        analysis = TestLogAnalysis(
            correctness=7 if has_pass and not has_fail else 3,
            completeness=5 if len(lines) > 20 else 2,
            error_free=8 if not has_fail else 4,
            results_are_visible=6 if has_structure else 3,
            reasoning="Basic analysis - LLM not available. Install LLM dependencies and set API keys for detailed analysis."
        )
        
        metadata = {
            'file_size_bytes': log_path.stat().st_size,
            'line_count': len(lines),
            'llm_model': 'none',
            'analysis_method': 'fallback'
        }
        
        return analysis, metadata


def inspect_single_log(log_path: Path, verbose: bool = False) -> Dict:
    """Inspect a single log file and return results."""
    
    try:
        if HAS_LLM:
            inspector = LLMTestLogInspector()
        else:
            inspector = FallbackInspector()
            
        analysis, metadata = inspector.inspect_log_file(log_path)
        
        analysis_dict = analysis.to_dict()
        result = {
            'file': str(log_path),
            'timestamp': datetime.now().isoformat(),
            'ratings': analysis_dict['ratings'],
            'reasoning': analysis_dict['reasoning'],
            'average_score': round(analysis.average_score(), 1),
            'grade': _score_to_grade(analysis.average_score())
        }
        
        if verbose:
            result['metadata'] = metadata
        
        return result
        
    except Exception as e:
        return {
            'file': str(log_path),
            'timestamp': datetime.now().isoformat(),
            'error': str(e),
            'ratings': {
                'correctness': 0, 
                'completeness': 0, 
                'error_free': 0, 
                'results_are_visible': 0
            },
            'reasoning': f'Analysis failed: {str(e)}',
            'average_score': 0.0,
            'grade': 'F'
        }


def inspect_directory(dir_path: Path, pattern: str = "*.log", verbose: bool = False) -> Dict:
    """Inspect all matching log files in a directory."""
    log_files = list(dir_path.glob(pattern))
    
    if not log_files:
        return {
            'directory': str(dir_path),
            'pattern': pattern,
            'files_found': 0,
            'error': f'No log files matching "{pattern}" found in {dir_path}'
        }
    
    results = []
    for log_file in sorted(log_files):
        print(f"Analyzing {log_file.name}...", file=sys.stderr)
        result = inspect_single_log(log_file, verbose)
        results.append(result)
    
    # Calculate summary stats
    scores = [r.get('average_score', 0) for r in results if 'error' not in r]
    avg_score = sum(scores) / len(scores) if scores else 0
    
    return {
        'directory': str(dir_path),
        'pattern': pattern,
        'files_found': len(log_files),
        'files_inspected': len(results),
        'summary': {
            'average_score': round(avg_score, 1),
            'grade': _score_to_grade(avg_score),
            'best_score': max(scores) if scores else 0,
            'worst_score': min(scores) if scores else 0
        },
        'results': results
    }


def _score_to_grade(score: float) -> str:
    """Convert numeric score to letter grade."""
    if score >= 9.0: return 'A+'
    elif score >= 8.5: return 'A'
    elif score >= 8.0: return 'A-'
    elif score >= 7.5: return 'B+'
    elif score >= 7.0: return 'B'
    elif score >= 6.5: return 'B-'
    elif score >= 6.0: return 'C+'
    elif score >= 5.5: return 'C'
    elif score >= 5.0: return 'C-'
    elif score >= 4.0: return 'D'
    else: return 'F'


def main():
    """Command line interface."""
    parser = argparse.ArgumentParser(
        description='Test Log Inspector - LLM-Powered Quality Assessment',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python test_log_inspector.py test.log
  python test_log_inspector.py tests/test_logs/gemini_integration_test_20250730_131038.log
  python test_log_inspector.py tests/test_logs/ --all --pretty
  
Environment Variables:
  ANTHROPIC_API_KEY - For Claude analysis (recommended)
  OPENAI_API_KEY    - For GPT-4 analysis
  GEMINI_API_KEY    - For Gemini analysis
        """
    )
    
    parser.add_argument('path', help='Path to log file or directory')
    parser.add_argument('--all', action='store_true', 
                       help='Inspect all log files in directory (if path is directory)')
    parser.add_argument('--pattern', default='*.log',
                       help='File pattern to match when inspecting directory (default: *.log)')
    parser.add_argument('--verbose', '-v', action='store_true',
                       help='Include detailed metadata in output')
    parser.add_argument('--pretty', action='store_true',
                       help='Pretty-print JSON output')
    
    args = parser.parse_args()
    
    # Check for LLM availability
    if not HAS_LLM:
        print(f"⚠️ Warning: LLM components not available: {IMPORT_ERROR}", file=sys.stderr)
        print("⚠️ Falling back to basic analysis. Install dependencies for full LLM analysis.", file=sys.stderr)
    
    path = Path(args.path)
    
    if not path.exists():
        print(json.dumps({'error': f'Path does not exist: {path}'}))
        return 1
    
    try:
        if path.is_file():
            # Inspect single file
            result = inspect_single_log(path, args.verbose)
        elif path.is_dir() and args.all:
            # Inspect directory
            result = inspect_directory(path, args.pattern, args.verbose)
        else:
            result = {'error': f'Path is directory but --all not specified: {path}'}
        
        # Output JSON
        if args.pretty:
            print(json.dumps(result, indent=2))
        else:
            print(json.dumps(result))
        
        return 0
        
    except Exception as e:
        print(json.dumps({'error': f'Inspection failed: {str(e)}'}))
        return 1


if __name__ == '__main__':
    sys.exit(main())
