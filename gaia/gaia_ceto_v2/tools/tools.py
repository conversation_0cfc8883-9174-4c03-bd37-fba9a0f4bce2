"""Unified tool system for Gaia Ceto v2.

Single, unified tool interface. Replaces both CetoTool and Tool hierarchies.
"""

import logging
from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional, Callable
import inspect
import json

logger = logging.getLogger(__name__)


class Tool(ABC):
    """Base tool interface."""
    
    def __init__(self, name: str, description: str):
        self.name = name
        self.description = description
    
    @abstractmethod
    def execute(self, **kwargs) -> str:
        """Execute the tool with given parameters.
        
        Args:
            **kwargs: Tool parameters
            
        Returns:
            Tool execution result as string
        """
        pass
    
    @abstractmethod
    def get_schema(self) -> Dict[str, Any]:
        """Get the JSON schema for this tool's parameters.
        
        Returns:
            JSON schema describing the tool's parameters
        """
        pass


class FunctionTool(Tool):
    """Tool wrapper for functions."""
    
    def __init__(self, name: str, description: str, func: Callable, schema: Dict[str, Any] = None):
        """Initialize a function tool.
        
        Args:
            name: Tool name
            description: Tool description
            func: Function to wrap
            schema: Parameter schema (auto-generated if not provided)
        """
        super().__init__(name, description)
        self.func = func
        self._schema = schema or self._generate_schema()
    
    def execute(self, **kwargs) -> str:
        """Execute the wrapped function."""
        try:
            # Filter kwargs to only include function parameters
            sig = inspect.signature(self.func)
            filtered_kwargs = {k: v for k, v in kwargs.items() if k in sig.parameters}
            
            result = self.func(**filtered_kwargs)
            
            # Convert result to string if needed
            if not isinstance(result, str):
                result = str(result)
            
            logger.debug(f"Tool {self.name} executed successfully")
            return result
            
        except Exception as e:
            error_msg = f"Error executing tool {self.name}: {str(e)}"
            logger.error(error_msg)
            return error_msg
    
    def get_schema(self) -> Dict[str, Any]:
        """Get the parameter schema for this tool."""
        return self._schema
    
    def _generate_schema(self) -> Dict[str, Any]:
        """Auto-generate schema from function signature."""
        sig = inspect.signature(self.func)
        properties = {}
        required = []
        
        for param_name, param in sig.parameters.items():
            param_schema = {"type": "string"}  # Default to string
            
            # Add description from docstring if available
            if self.func.__doc__:
                # Simple extraction - could be more sophisticated
                param_schema["description"] = f"Parameter for {param_name}"
            
            properties[param_name] = param_schema
            
            # Check if parameter is required (no default value)
            if param.default == inspect.Parameter.empty:
                required.append(param_name)
        
        return {
            "type": "object",
            "properties": properties,
            "required": required
        }


class ToolRegistry:
    """Tool registry."""
    
    def __init__(self):
        self.tools: Dict[str, Tool] = {}
    
    def register_tool(self, tool: Tool) -> None:
        """Register a tool."""
        self.tools[tool.name] = tool
        logger.info(f"Registered tool: {tool.name}")
    
    def register_function(self, name: str, description: str, func: Callable, schema: Dict[str, Any] = None) -> None:
        """Register a function as a tool.
        
        Args:
            name: Tool name
            description: Tool description  
            func: Function to wrap
            schema: Parameter schema (auto-generated if not provided)
        """
        tool = FunctionTool(name, description, func, schema)
        self.register_tool(tool)
    
    def get_tool(self, name: str) -> Optional[Tool]:
        """Get tool by name."""
        return self.tools.get(name)
    
    def list_tools(self) -> List[str]:
        """List tool names."""
        return list(self.tools.keys())
    
    def get_schemas_for_llm(self) -> List[Dict[str, Any]]:
        """Get tool schemas for LLM consumption."""
        return [
            {
                "name": tool.name,
                "description": tool.description,
                "schema": tool.get_schema()
            }
            for tool in self.tools.values()
        ]
    
    def execute_tool(self, name: str, **kwargs) -> str:
        """Execute tool by name."""
        tool = self.get_tool(name)
        if not tool:
            raise ValueError(f"Tool '{name}' not found")
        
        return tool.execute(**kwargs)
    
    def has_tool(self, name: str) -> bool:
        """Check if tool exists."""
        return name in self.tools


# Built-in tools
def echostring(text: str) -> str:
    """Echo a string with the classic weasly wabbit response.
    
    Args:
        text: The text to echo
        
    Returns:
        The echoed text with the classic response
    """
    return text + ", " + text + ", " + text + " fer SURE!"


def get_time() -> str:
    """Get the current time.
    
    Returns:
        Current time as ISO string
    """
    from datetime import datetime
    return datetime.now().isoformat()


def add_numbers(a: str, b: str) -> str:
    """Add two numbers.
    
    Args:
        a: First number (as string)
        b: Second number (as string)
        
    Returns:
        Sum of the numbers
    """
    try:
        result = float(a) + float(b)
        return str(result)
    except ValueError:
        return "Error: Both arguments must be numbers"


# Global registry
default_registry = ToolRegistry()

# Register built-in tools
default_registry.register_function(
    "echostring",
    "Echo a string with the classic weasly wabbit response",
    echostring,
    {
        "type": "object",
        "properties": {
            "phrase": {
                "type": "string",
                "description": "The phrase to echo"
            }
        },
        "required": ["phrase"]
    }
)

default_registry.register_function(
    "get_time",
    "Get the current time as ISO string",
    get_time,
    {
        "type": "object",
        "properties": {},
        "required": []
    }
)

default_registry.register_function(
    "add_numbers",
    "Add two numbers together",
    add_numbers,
    {
        "type": "object", 
        "properties": {
            "a": {
                "type": "string",
                "description": "First number"
            },
            "b": {
                "type": "string", 
                "description": "Second number"
            }
        },
        "required": ["a", "b"]
    }
)


def create_tool_registry() -> ToolRegistry:
    """Create tool registry with built-ins."""
    registry = ToolRegistry()
    
    # Register the same built-in tools
    for tool_name, tool in default_registry.tools.items():
        registry.register_tool(tool)
    
    return registry