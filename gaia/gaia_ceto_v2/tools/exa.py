"""
Exa Tools Integration - Dedicated module for Exa search and research tools

Provides caching, comprehensive logging, and error handling for Exa API calls.
"""

import json
import hashlib
import logging
import os
import time
from typing import Dict, Any, Optional

try:
    from ..core.tool_response import ToolResponse
except ImportError:
    # Fallback for when running with different import paths
    from gaia_ceto_v2.core.tool_response import ToolResponse

logger = logging.getLogger(__name__)

# Import exa_py if available
try:
    from exa_py import Exa
    HAS_EXA = True
except ImportError:
    HAS_EXA = False
    logger.warning("exa_py not installed - exa tools will use fallback responses")

# Import requests for research API
try:
    import requests
    HAS_REQUESTS = True
except ImportError:
    HAS_REQUESTS = False
    logger.warning("requests not installed - exa research will be unavailable")


class ExaTools:
    """Dedicated handler for Exa search and research tools with caching support."""
    
    def __init__(self, simple_cache=None, logger_instances=None, accounting=None):
        """
        Initialize Exa tools handler.
        
        Args:
            simple_cache: Optional cache instance for caching results
            logger_instances: Tuple of (send_logger, rcv_logger, req_logger, resp_logger)
            accounting: Optional accounting system for API cost tracking
        """
        self.simple_cache = simple_cache
        self.accounting = accounting
        
        # Extract loggers if provided
        if logger_instances:
            self._send_logger, self._rcv_logger, self._req_logger, self._resp_logger = logger_instances
        else:
            # Fallback to standard logger
            self._send_logger = logger
            self._rcv_logger = logger
            self._req_logger = logger
            self._resp_logger = logger
    
    def exa_search(self, args: Dict[str, Any], call_id: str) -> Dict[str, Any]:
        """
        Execute Exa search with caching support.
        
        Args:
            args: Tool arguments containing query and num_results
            call_id: Request correlation ID for logging
            
        Returns:
            Tool execution result dictionary
        """
        query = args.get("query", "")
        num_results = args.get("num_results", 3)
        
        # Create deterministic cache key for this search
        query_hash = hashlib.md5(query.encode()).hexdigest()[:8]
        cache_key = f"exa_search_{query_hash}_{num_results}"
        
        def perform_search():
            """Inner function to perform actual exa search (for caching)."""
            if not HAS_EXA or not os.getenv("EXA_API_KEY"):
                missing = []
                if not HAS_EXA:
                    missing.append("exa_py not installed")
                if not os.getenv("EXA_API_KEY"):
                    missing.append("EXA_API_KEY not set")
                
                return {
                    "success": False,
                    "error": f"Exa search unavailable: {', '.join(missing)}"
                }
            
            try:
                exa = Exa(api_key=os.getenv("EXA_API_KEY"))
                
                # Log external API request
                api_request = {
                    'provider': 'exa',
                    'endpoint': 'search_and_contents', 
                    'method': 'SDK_CALL',
                    'params': {"query": query, "num_results": num_results, "text": True}
                }
                self._send_logger.info(f"API_REQUEST - {call_id} - {json.dumps(api_request)}")
                
                # Raw vendor logging
                self._req_logger.info(f"RAW_REQUEST - {call_id} - exa.search_and_contents(query='{query}', num_results={num_results}, text=True)")
                
                # Perform search
                search_response = exa.search_and_contents(
                    query=query,
                    num_results=num_results,
                    text=True
                )
                
                # Raw vendor logging - response
                raw_response_data = {
                    'results_count': len(search_response.results),
                    'results': [
                        {
                            'id': getattr(r, 'id', None),
                            'title': r.title,
                            'url': r.url,
                            'score': getattr(r, 'score', None),
                            'published_date': getattr(r, 'published_date', None),
                            'author': getattr(r, 'author', None),
                            'text_length': len(r.text) if r.text else 0,
                            'text_preview': r.text[:200] + '...' if r.text and len(r.text) > 200 else r.text
                        }
                        for r in search_response.results
                    ]
                }
                self._resp_logger.info(f"RAW_RESPONSE - {call_id} - {json.dumps(raw_response_data)}")
                
                # Log external API response
                api_response = {
                    'provider': 'exa',
                    'endpoint': 'search_and_contents',
                    'status': 'success',
                    'results_count': len(search_response.results),
                    'total_chars': sum(len(r.text or "") for r in search_response.results)
                }
                self._rcv_logger.info(f"API_RESPONSE - {call_id} - {json.dumps(api_response)}")
                
                # Format results
                results = []
                for r in search_response.results:
                    results.append({
                        "title": r.title,
                        "url": r.url,
                        "text": r.text[:500] + "..." if r.text and len(r.text) > 500 else r.text or "",
                        "score": r.score
                    })
                
                result_text = f"Exa search for '{query}': {len(results)} results\n\n"
                for i, r in enumerate(results, 1):
                    result_text += f"{i}. {r['title']}\n{r['url']}\n{r['text']}\n\n"
                
                logger.debug(f"🔍 exa_search executed: query='{query}', found {len(results)} results")
                return {
                    "success": True,
                    "result": result_text.strip()
                }
                
            except Exception as e:
                # Raw vendor logging - error
                self._resp_logger.error(f"RAW_ERROR - {call_id} - Exception: {type(e).__name__}: {str(e)}")
                
                # Log external API error
                api_error = {
                    'provider': 'exa',
                    'endpoint': 'search_and_contents',
                    'status': 'error',
                    'error': str(e)
                }
                self._rcv_logger.error(f"API_ERROR - {call_id} - {json.dumps(api_error)}")
                
                logger.error(f"Exa search error: {e}")
                return {
                    "success": False,
                    "error": f"Exa search failed: {str(e)}"
                }
        
        # Use cache if available, otherwise perform search directly
        if self.simple_cache:
            logger.debug(f"🗃️ Using cache for exa_search: {cache_key}")
            result = self.simple_cache.get_or_compute(cache_key, perform_search, ttl=300, request_id=call_id)  # 5-minute TTL
        else:
            logger.debug(f"🔍 No cache available, performing direct exa_search")
            result = perform_search()
        
        return result
    
    def exa_research_sync_smart_cache(self, args: Dict[str, Any], call_id: str) -> Dict[str, Any]:
        """
        Execute Exa research with caching support.
        
        Args:
            args: Tool arguments containing query, max_sources, use_schema
            call_id: Request correlation ID for logging
            
        Returns:
            Tool execution result dictionary
        """
        query = args.get("query", "")
        max_sources = args.get("max_sources", 10)
        use_schema = args.get("use_schema", False)
        bypass_cache = args.get("bypass_cache", False)
        
        # Create deterministic cache key for this research query
        query_hash = hashlib.md5(query.encode()).hexdigest()[:8]
        cache_key = f"exa_research_{query_hash}_{max_sources}_{use_schema}"
        
        def perform_research():
            """Inner function to perform actual exa research (for caching)."""
            if not HAS_EXA or not HAS_REQUESTS or not os.getenv("EXA_API_KEY"):
                missing = []
                if not HAS_EXA:
                    missing.append("exa_py not installed")
                if not HAS_REQUESTS:
                    missing.append("requests not installed")
                if not os.getenv("EXA_API_KEY"):
                    missing.append("EXA_API_KEY not set")
                
                return ToolResponse(
                    content=f"Exa research unavailable: {', '.join(missing)}",
                    include_in_context=False,
                    needs_synthesis=False,
                    show_to_user=True,
                    store_as_document=False
                )
            
            try:
                exa_api_key = os.getenv("EXA_API_KEY")
                research_url = "https://api.exa.ai/research/v0/tasks"
                headers = {
                    "Authorization": f"Bearer {exa_api_key}",
                    "Content-Type": "application/json"
                }
                
                # Build research payload
                research_payload = {
                    "instructions": query
                }
                
                # Add optional schema for structured output
                if use_schema:
                    research_payload["output"] = {
                        "schema": {
                            "type": "object",
                            "properties": {
                                "summary": {"type": "string"},
                                "key_findings": {
                                    "type": "array",
                                    "items": {"type": "string"}
                                },
                                "sources": {
                                    "type": "array",
                                    "items": {
                                        "type": "object",
                                        "properties": {
                                            "title": {"type": "string"},
                                            "url": {"type": "string"},
                                            "snippet": {"type": "string"}
                                        }
                                    }
                                }
                            }
                        }
                    }
                
                # Raw vendor logging - request
                self._req_logger.info(f"RAW_REQUEST - {call_id} - POST {research_url} - Headers: {json.dumps(dict(headers))} - Payload: {json.dumps(research_payload)}")
                
                # Log external API request
                api_request = {
                    'provider': 'exa', 
                    'endpoint': 'research_create',
                    'url': research_url, 
                    'method': 'POST',
                    'payload': research_payload
                }
                self._send_logger.info(f"API_REQUEST - {call_id} - {json.dumps(api_request)}")
                
                # Submit research task
                response = requests.post(research_url, json=research_payload, headers=headers)
                
                # Raw vendor logging - response
                self._resp_logger.info(f"RAW_RESPONSE - {call_id} - {response.status_code} - Headers: {json.dumps(dict(response.headers))} - Body: {response.text}")
                
                # IMMEDIATE accounting hook - right next to actual API call
                if response.status_code in [200, 201] and hasattr(self, 'accounting') and self.accounting:
                    try:
                        acct_id = self.accounting.record_api_call(
                            provider="exa",
                            service_name="research_create",
                            api_calls=1,
                            request_id=call_id,
                            tool_args={'query': query, 'max_sources': max_sources}
                        )
                        logger.info(f"ACCT_RECORDED - {call_id} - exa_research_create - {acct_id}")
                    except Exception as e:
                        logger.error(f"ACCT_FAILED - {call_id} - exa_research_create - {e}")
                
                # Log external API response
                api_response = {
                    'provider': 'exa',
                    'endpoint': 'research_create', 
                    'status_code': response.status_code,
                    'response_size': len(response.text),
                    'response': response.text[:500] + '...' if len(response.text) > 500 else response.text
                }
                self._rcv_logger.info(f"API_RESPONSE - {call_id} - {json.dumps(api_response)}")
                
                logger.debug(f"Research API response: {response.status_code} - {response.text}")
                
                if response.status_code not in [200, 201]:
                    return ToolResponse(
                        content=f"Research API error: {response.status_code} - {response.text}",
                        include_in_context=False,
                        needs_synthesis=False,
                        show_to_user=True,
                        store_as_document=False
                    )
                
                task_data = response.json()
                task_id = task_data.get("task_id") or task_data.get("id")
                logger.debug(f"Research task started with ID: {task_id}")
                
                # Poll for completion (with timeout)
                max_wait = 900  # 15 minutes 
                poll_interval = 10  # 10 seconds
                waited = 0
                
                while waited < max_wait:
                    check_url = f"https://api.exa.ai/research/v0/tasks/{task_id}"
                    check_response = requests.get(check_url, headers=headers)
                    
                    # IMMEDIATE accounting hook for polling API call
                    if check_response.status_code == 200 and hasattr(self, 'accounting') and self.accounting:
                        try:
                            acct_id = self.accounting.record_api_call(
                                provider="exa",
                                service_name="research_poll",
                                api_calls=1,
                                request_id=call_id,
                                tool_args={'task_id': task_id, 'poll_attempt': int(waited/poll_interval) + 1}
                            )
                            logger.debug(f"ACCT_RECORDED - {call_id} - exa_research_poll - {acct_id}")
                        except Exception as e:
                            logger.error(f"ACCT_FAILED - {call_id} - exa_research_poll - {e}")
                    
                    # Raw vendor logging - status check (first and completion only)
                    if waited == 0:  # First check
                        self._req_logger.info(f"RAW_REQUEST - {call_id} - GET {check_url} - Headers: {json.dumps(dict(headers))}")
                        self._resp_logger.info(f"RAW_RESPONSE - {call_id} - FIRST_STATUS - {check_response.status_code} - {check_response.text}")
                    
                    if check_response.status_code == 200:
                        status_data = check_response.json()
                        task_status = status_data.get("status", "unknown")
                        
                        if task_status == "completed":
                            # Final completion logging
                            self._resp_logger.info(f"RAW_RESPONSE - {call_id} - COMPLETED - {check_response.status_code} - {check_response.text}")
                            
                            # Extract research results
                            research_results = status_data.get("data", status_data.get("result", {}))
                            
                            if isinstance(research_results, dict):
                                # Format structured results
                                result_text = f"Exa research for '{query}' completed:\n\n"
                                
                                # Handle generic research results structure
                                if research_results.get("summary"):
                                    result_text += f"Summary: {research_results['summary']}\n\n"
                                
                                if research_results.get("key_findings"):
                                    result_text += "Key Findings:\n"
                                    for i, finding in enumerate(research_results["key_findings"], 1):
                                        result_text += f"{i}. {finding}\n"
                                    result_text += "\n"
                                
                                # Handle any structured data fields (e.g. rocketShips, companies, etc.)
                                for key, value in research_results.items():
                                    if key in ["summary", "key_findings", "sources"]:
                                        continue  # Already handled above
                                    
                                    if isinstance(value, list) and value:
                                        result_text += f"{key.replace('_', ' ').title()}:\n"
                                        for i, item in enumerate(value, 1):
                                            if isinstance(item, dict):
                                                # Format structured item
                                                result_text += f"{i}. "
                                                if 'name' in item:
                                                    result_text += f"{item['name']}"
                                                if 'manufacturer' in item:
                                                    result_text += f" (by {item['manufacturer']})"
                                                if 'launchDate' in item:
                                                    result_text += f" - {item['launchDate']}"
                                                result_text += "\n"
                                                
                                                if 'description' in item:
                                                    result_text += f"   Description: {item['description']}\n"
                                                if 'reasoning' in item:
                                                    result_text += f"   Why notable: {item['reasoning']}\n"
                                                if 'sources' in item and item['sources']:
                                                    result_text += f"   Sources: {', '.join(item['sources'])}\n"
                                                result_text += "\n"
                                            else:
                                                result_text += f"{i}. {item}\n"
                                        result_text += "\n"
                                
                                # Handle legacy sources format
                                if research_results.get("sources"):
                                    result_text += "Additional Sources:\n"
                                    for i, source in enumerate(research_results["sources"], 1):
                                        if isinstance(source, dict):
                                            result_text += f"{i}. {source.get('title', 'Untitled')}\n"
                                            result_text += f"   {source.get('url', 'No URL')}\n"
                                            if source.get('snippet'):
                                                result_text += f"   {source['snippet']}\n"
                                        else:
                                            result_text += f"{i}. {source}\n"
                                        result_text += "\n"
                            else:
                                # Simple string result
                                result_text = f"Exa research for '{query}' completed:\n\n{research_results}"
                            
                            logger.debug(f"🔍 exa_research completed: query='{query}', waited {waited}s")
                            
                            # Create summary for context
                            summary = f"Exa research completed for '{query}': Found {len(research_results.get('rocketShips', research_results.get('companies', research_results.get('key_findings', []))))} items"
                            
                            # Return ToolResponse with document storage
                            return ToolResponse(
                                content=result_text.strip(),  # Full content goes to document
                                include_in_context=True,
                                needs_synthesis=False,
                                show_to_user=True,
                                store_as_document=True,
                                content_format="research_report",
                                metadata={
                                    "query": query,
                                    "max_sources": max_sources,
                                    "use_schema": use_schema,
                                    "summary": summary,
                                    "item_count": len(research_results.get('rocketShips', research_results.get('companies', research_results.get('key_findings', []))))
                                }
                            )
                        
                        elif task_status in ["failed", "error"]:
                            error_msg = status_data.get("error", "Research task failed")
                            return ToolResponse(
                                content=f"Research failed: {error_msg}",
                                include_in_context=False,
                                needs_synthesis=False,
                                show_to_user=True,
                                store_as_document=False
                            )
                    
                    # Wait before next poll
                    time.sleep(poll_interval)
                    waited += poll_interval
                
                # Timeout reached
                return ToolResponse(
                    content=f"Research timeout after {max_wait}s - task may still be running (ID: {task_id})",
                    include_in_context=False,
                    needs_synthesis=False,
                    show_to_user=True,
                    store_as_document=False
                )
                
            except Exception as e:
                # Raw vendor logging - error
                self._resp_logger.error(f"RAW_ERROR - {call_id} - Exception: {type(e).__name__}: {str(e)}")
                
                # Log external API error
                api_error = {
                    'provider': 'exa',
                    'endpoint': 'research',
                    'status': 'error',
                    'error': str(e)
                }
                self._rcv_logger.error(f"API_ERROR - {call_id} - {json.dumps(api_error)}")
                
                logger.error(f"Exa research error: {e}")
                return ToolResponse(
                    content=f"Exa research failed: {str(e)}",
                    include_in_context=False,
                    needs_synthesis=False,
                    show_to_user=True,
                    store_as_document=False
                )
        
        # Use cache if available and not bypassed, otherwise perform research directly
        if self.simple_cache and not bypass_cache:
            logger.debug(f"🗃️ Using cache for exa_research: {cache_key}")
            # Longer TTL for research since it's expensive
            result = self.simple_cache.get_or_compute(cache_key, perform_research, ttl=1800, request_id=call_id)  # 30-minute TTL
            
            # If result is a dict (from cache), reconstruct ToolResponse
            if isinstance(result, dict) and 'content' in result:
                result = ToolResponse(
                    content=result.get('content', ''),
                    include_in_context=result.get('include_in_context', True),
                    needs_synthesis=result.get('needs_synthesis', False),
                    show_to_user=result.get('show_to_user', True),
                    store_as_document=result.get('store_as_document', False),
                    document_id=result.get('document_id'),
                    content_format=result.get('content_format', 'text'),
                    metadata=result.get('metadata', {})
                )
        else:
            if bypass_cache:
                logger.info(f"🚫 Cache bypassed for exa_research: {cache_key}")
            else:
                logger.debug(f"🔍 No cache available, performing direct exa_research")
            result = perform_research()
            
            # Store result in cache even when bypassed (for future requests)
            if self.simple_cache and bypass_cache:
                logger.debug(f"💾 Storing bypassed result in cache: {cache_key}")
                self.simple_cache.put(cache_key, result, ttl=1800, entry_type="exa_research", request_id=call_id)
        
        return result
    
    def exa_research_async_background(self, args: Dict[str, Any], call_id: str) -> Dict[str, Any]:
        """
        Execute Exa research asynchronously in background.
        Returns immediately with task_id, research continues in background.
        
        Future implementation for deeper, longer research workflows.
        
        Args:
            args: Dictionary containing research parameters
            call_id: Unique identifier for this request
            
        Returns:
            Dict with task_id and status info for background tracking
        """
        # TODO: Implement async background research
        # - Start research task
        # - Return task_id immediately 
        # - Allow polling/webhook for completion
        return {
            "success": False,
            "error": "Async background research not yet implemented",
            "todo": "Returns task_id for background research tracking"
        }
    
    def exa_research_async_stream(self, args: Dict[str, Any], call_id: str) -> Dict[str, Any]:
        """
        Execute Exa research with streaming partial results.
        Streams partial results as research progresses.
        
        Future implementation for real-time research updates.
        
        Args:
            args: Dictionary containing research parameters  
            call_id: Unique identifier for this request
            
        Returns:
            AsyncIterator yielding partial research results
        """
        # TODO: Implement streaming research
        # - Start research task
        # - Stream intermediate results (sources found, analysis progress, etc.)
        # - Final stream contains complete report
        return {
            "success": False,
            "error": "Async streaming research not yet implemented", 
            "todo": "Returns AsyncIterator for progressive research results"
        }