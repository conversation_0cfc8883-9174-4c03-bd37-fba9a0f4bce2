"""
Gaia Ceto v2 Tools - Tool implementations and registry

This package contains tool implementations that can be used by the core
chat system. Tools are separate from core business logic to maintain
clean separation of concerns.

Architecture:
- Core defines the tool interface (CetoTool, CetoToolRegistry)
- Tools package implements specific tools
- Interfaces can expose tools via different protocols (MCP, HTTP, etc.)

This allows:
- Core remains lightweight and focused
- Tools can be added/removed without affecting core
- Different tool sets for different use cases
- Easy testing of tools in isolation
"""

from .simple_tools import (
    SimpleToolRegistry, 
    default_registry
)

__all__ = [
    'SimpleToolRegistry', 
    'default_registry'
]