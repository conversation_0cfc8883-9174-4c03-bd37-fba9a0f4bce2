"""
SimpleToolRegistry - Option A: Brutal Simplification

Replaces 4 abstraction layers with a simple dict-based registry.
No inheritance, no adapters, no enterprise patterns.
Just functions + schemas in a dict.
"""

import logging
from typing import Dict, Any, Callable, List
import json

logger = logging.getLogger(__name__)


class SimpleToolRegistry:
    """Dead simple tool registry. Function dict + schema dict. Done."""
    
    def __init__(self):
        self.tools = {}  # name -> {"func": callable, "schema": dict, "description": str}
        logger.info("SimpleToolRegistry initialized")
    
    def add(self, name: str, func: Callable, schema: Dict[str, Any], description: str = ""):
        """Add a tool. Just a function + schema."""
        self.tools[name] = {
            "func": func,
            "schema": schema,
            "description": description or f"Tool: {name}"
        }
        logger.debug(f"Added tool: {name}")
    
    def call(self, name: str, **kwargs) -> str:
        """Call a tool. Direct function call."""
        if name not in self.tools:
            raise ValueError(f"Tool '{name}' not found")
        
        try:
            result = self.tools[name]["func"](**kwargs)
            # Return result as-is, let the caller handle ToolResponse objects
            return result if result is not None else ""
        except Exception as e:
            logger.error(f"Tool {name} failed: {e}")
            raise
    
    def schema(self, name: str) -> Dict[str, Any]:
        """Get tool schema."""
        if name not in self.tools:
            raise ValueError(f"Tool '{name}' not found")
        return self.tools[name]["schema"]
    
    def list_tools(self) -> List[str]:
        """List available tool names."""
        return list(self.tools.keys())
    
    def list_schemas(self) -> Dict[str, Dict[str, Any]]:
        """Get all tools formatted for LLM tool calling."""
        result = {}
        for name, tool in self.tools.items():
            result[name] = {
                "name": name,
                "description": tool["description"],
                "schema": tool["schema"]
            }
        return result
    
    def exists(self, name: str) -> bool:
        """Check if tool exists."""
        return name in self.tools


# Global registry instance - no dependency injection needed
simple_registry = SimpleToolRegistry()


def register_tool(name: str, description: str = "", schema: Dict[str, Any] = None):
    """Decorator to register functions as tools."""
    def decorator(func: Callable):
        tool_schema = schema or {
            "type": "object",
            "properties": {},
            "required": []
        }
        simple_registry.add(name, func, tool_schema, description)
        return func
    return decorator


# Built-in tools
@register_tool(
    name="echostring",
    description="Echo the input text with some modifications",
    schema={
        "type": "object",
        "properties": {
            "text": {"type": "string", "description": "Text to echo"}
        },
        "required": ["text"]
    }
)
def echostring(text: str) -> str:
    """Echo text with modifications."""
    return f"Echo: {text} (modified)"


@register_tool(
    name="get_time", 
    description="Get current date and time",
    schema={
        "type": "object",
        "properties": {},
        "required": []
    }
)
def get_time() -> str:
    """Get current time."""
    from datetime import datetime
    return datetime.now().strftime("%Y-%m-%d %H:%M:%S")


@register_tool(
    name="add_numbers",
    description="Add two numbers together", 
    schema={
        "type": "object",
        "properties": {
            "a": {"type": "string", "description": "First number"},
            "b": {"type": "string", "description": "Second number"}
        },
        "required": ["a", "b"]
    }
)
def add_numbers(a: str, b: str) -> str:
    """Add two numbers."""
    try:
        return str(float(a) + float(b))
    except ValueError:
        return "Error: Invalid numbers"


@register_tool(
    name="weather",
    description="Get weather information for a location (simulated)",
    schema={
        "type": "object", 
        "properties": {
            "location": {"type": "string", "description": "Location to get weather for"}
        },
        "required": ["location"]
    }
)
def weather(location: str) -> str:
    """Simulated weather tool."""
    return f"Weather in {location}: Sunny, 72°F (simulated)"


# Add exa tools if available
def _add_exa_tools():
    """Add exa tools if module is available."""
    try:
        from tools.exa import ExaTools
        exa = ExaTools()
        
        simple_registry.add(
            "exa_search",
            exa.exa_search,
            {
                "type": "object",
                "properties": {
                    "query": {"type": "string", "description": "Search query"},
                    "num_results": {"type": "integer", "description": "Number of results to return", "default": 3}
                },
                "required": ["query"]
            },
            "Search the web using Exa AI search engine"
        )
        
        simple_registry.add(
            "exa_research", 
            exa.exa_research_sync_smart_cache,
            {
                "type": "object",
                "properties": {
                    "query": {"type": "string", "description": "Research query or instructions"},
                    "max_sources": {"type": "integer", "description": "Maximum number of sources to use", "default": 10},
                    "use_schema": {"type": "boolean", "description": "Use structured output schema", "default": False},
                    "bypass_cache": {"type": "boolean", "description": "Skip cache check and force fresh research", "default": False}
                },
                "required": ["query"]
            },
            "Conduct deep research using Exa AI research endpoint"
        )
        
        logger.info("Added exa tools to simple registry")
    except ImportError:
        logger.warning("Exa tools not available - skipping")

# Try to add exa tools
_add_exa_tools()

# Export the simple registry as default
default_registry = simple_registry