#!/usr/bin/env python3
"""
Debug script to test if completion method logging is working.
"""

import sys
from pathlib import Path

# Add current directory to path for imports
sys.path.insert(0, str(Path(__file__).parent))

def test_completion_logging():
    """Test if the completion method actually logs."""
    print("🔍 Testing LiteLLMWrapper.completion() logging...")
    
    # Clear existing logs
    log_dir = Path('/tmp/gaia_logs/ceto')
    if log_dir.exists():
        for log_file in log_dir.glob('*.log'):
            log_file.unlink()
    
    print("\n1. Creating LiteLLMWrapper...")
    from core.llm_wrapper import LiteLLMWrapper
    
    wrapper = LiteLLMWrapper()
    print(f"   Wrapper: {wrapper}")
    
    print("\n2. Testing completion call...")
    try:
        messages = [{"role": "user", "content": "Say exactly 'test response'"}]
        model = "gemini/gemini-2.5-flash-lite"
        request_id = "debug-completion-001"
        
        print(f"   Calling completion with model: {model}")
        print(f"   Messages: {messages}")
        print(f"   Request ID: {request_id}")
        
        result = wrapper.completion(
            model=model,
            messages=messages,
            request_id=request_id
        )
        
        print(f"   ✅ Completion result: {result}")
        print(f"   Success: {result.success}")
        if result.success:
            print(f"   Content: {result.content[:100]}...")
        else:
            print(f"   Error: {result.error}")
    
    except Exception as e:
        print(f"   ❌ Completion failed: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n3. Checking log files immediately after completion...")
    if log_dir.exists():
        log_files = list(log_dir.glob('*.log'))
        print(f"   📂 Directory: {log_dir}")
        print(f"   📄 Total files: {len(log_files)}")
        
        for log_file in sorted(log_files):
            size = log_file.stat().st_size
            print(f"      • {log_file.name} ({size} bytes)")
            if size > 0:
                content = log_file.read_text().strip()
                lines = content.split('\n')
                print(f"        Lines: {len(lines)}")
                for i, line in enumerate(lines[-3:], 1):  # Show last 3 lines
                    print(f"        [{-3+i}]: {line}")
                
                # Check for our request_id
                if request_id in content:
                    print(f"        ✅ Found request_id: {request_id}")
                else:
                    print(f"        ❌ Missing request_id: {request_id}")
    else:
        print("   ❌ Log directory doesn't exist")
    
    print("\n🎯 Completion logging test complete!")

if __name__ == "__main__":
    test_completion_logging()