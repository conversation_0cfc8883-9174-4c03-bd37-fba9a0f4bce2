#!/usr/bin/env python3
"""
Debug script to trace the exact chat manager flow.
"""

import sys
from pathlib import Path

# Add current directory to path for imports
sys.path.insert(0, str(Path(__file__).parent))

def test_chat_manager_flow():
    """Trace the exact chat manager call path."""
    print("🔍 Tracing chat manager call path...")
    
    # Clear existing logs
    log_dir = Path('/tmp/gaia_logs/ceto')
    if log_dir.exists():
        for log_file in log_dir.glob('*.log'):
            log_file.unlink()
    
    print("\n1. Creating components...")
    from core import create_llm_provider, create_chat_manager
    
    llm_provider = create_llm_provider('gemini')
    print(f"   LLM Provider: {llm_provider}")
    print(f"   Wrapper: {getattr(llm_provider, 'wrapper', None)}")
    
    chat_manager = create_chat_manager(llm_provider=llm_provider, storage_dir='/tmp/gaia_conversations')
    conversation_id = chat_manager.create_conversation(user_id='debug', title='Debug')
    
    print("\n2. Testing message_service directly...")
    conversation = chat_manager.conversation_service.get_conversation(conversation_id)
    print(f"   Conversation: {conversation}")
    
    # Call message_service.send_message directly
    test_request_id = "direct-message-service-001"
    print(f"   Calling message_service.send_message with request_id: {test_request_id}")
    
    try:
        result = chat_manager.message_service.send_message(
            conversation=conversation,
            message="Say 'direct test'",
            request_id=test_request_id
        )
        print(f"   ✅ Message service result: {result[:50]}...")
    except Exception as e:
        print(f"   ❌ Message service error: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n3. Checking logs after message_service call...")
    if log_dir.exists():
        log_files = list(log_dir.glob('*.log'))
        if log_files:
            for log_file in sorted(log_files):
                size = log_file.stat().st_size
                print(f"   • {log_file.name} ({size} bytes)")
                if size > 0 and test_request_id in log_file.read_text():
                    print(f"     ✅ Contains {test_request_id}")
        else:
            print("   ❌ No log files after message_service call")
    
    print("\n4. Testing llm_provider directly...")
    test_request_id_2 = "direct-provider-002"
    print(f"   Calling llm_provider.generate_response with request_id: {test_request_id_2}")
    
    try:
        from core.conversation import Message
        context = [Message('user', 'Hello', None).to_dict()]
        
        result = llm_provider.generate_response(
            prompt="Say 'provider test'",
            context=context,
            request_id=test_request_id_2
        )
        print(f"   ✅ Provider result: {result}")
    except Exception as e:
        print(f"   ❌ Provider error: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n5. Final log check...")
    if log_dir.exists():
        log_files = list(log_dir.glob('*.log'))
        if log_files:
            for log_file in sorted(log_files):
                size = log_file.stat().st_size
                print(f"   • {log_file.name} ({size} bytes)")
                if size > 0:
                    content = log_file.read_text()
                    if test_request_id in content:
                        print(f"     ✅ Contains {test_request_id}")
                    if test_request_id_2 in content:
                        print(f"     ✅ Contains {test_request_id_2}")
        else:
            print("   ❌ Still no log files!")
    
    print("\n🎯 Chat manager flow trace complete!")

if __name__ == "__main__":
    test_chat_manager_flow()