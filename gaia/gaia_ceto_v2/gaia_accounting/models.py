"""
Data models for the accounting system.
"""

import uuid
from dataclasses import dataclass, asdict
from datetime import datetime
from decimal import Decimal
from typing import List, Optional, Dict, Any


@dataclass
class StackFrame:
    """Represents a single stack frame for call attribution."""
    
    filename: str
    function_name: str
    line_number: int
    
    def __str__(self) -> str:
        return f"{self.filename}:{self.function_name}:{self.line_number}"


@dataclass 
class ServiceCall:
    """Complete record of a service call with cost and attribution data."""
    
    # Core identification (required fields)
    call_id: str
    timestamp: datetime
    service_type: str  # 'llm', 'api', 'mcp'
    provider: str      # 'openai', 'anthropic', 'firecrawl'
    model: str         # 'gpt-4', 'claude-3-5-sonnet'
    
    # Attribution (required fields)
    root_script: str
    call_stack: List[StackFrame]
    
    # Account identity (fields with defaults)
    project_slug: str = "gaia_ceto"       # Project identifier
    client_slug: str = "internal"         # Client identifier
    
    # Request correlation (optional field)
    request_id: Optional[str] = None      # Groups calls from single web request
    
    # Metrics (flexible per service type)
    input_tokens: Optional[int] = None
    output_tokens: Optional[int] = None
    api_calls: Optional[int] = None
    
    # Cost calculation
    cost_usd: Optional[Decimal] = None
    pricing_schedule: Optional[str] = None
    
    # Additional metadata
    metadata: Optional[Dict[str, Any]] = None
    
    @classmethod
    def create_llm_call(cls, provider: str, model: str, input_tokens: int, 
                       output_tokens: int, cost_usd: Decimal,
                       root_script: str, call_stack: List[StackFrame],
                       pricing_schedule: str = "2025-01-v1",
                       request_id: Optional[str] = None,
                       project_slug: str = "gaia_ceto",
                       client_slug: str = "internal") -> 'ServiceCall':
        """Create an LLM service call record."""
        return cls(
            call_id=str(uuid.uuid4()),
            timestamp=datetime.utcnow(),
            service_type="llm",
            provider=provider,
            model=model,
            request_id=request_id,
            project_slug=project_slug,
            client_slug=client_slug,
            root_script=root_script,
            call_stack=call_stack,
            input_tokens=input_tokens,
            output_tokens=output_tokens,
            cost_usd=cost_usd,
            pricing_schedule=pricing_schedule
        )
    
    @classmethod
    def create_api_call(cls, provider: str, service_name: str, api_calls: int,
                       cost_usd: Decimal, root_script: str, call_stack: List[StackFrame],
                       pricing_schedule: str = "2025-01-v1",
                       request_id: Optional[str] = None,
                       project_slug: str = "gaia_ceto",
                       client_slug: str = "internal") -> 'ServiceCall':
        """Create an API service call record."""
        return cls(
            call_id=str(uuid.uuid4()),
            timestamp=datetime.utcnow(),
            service_type="api",
            provider=provider,
            model=service_name,
            request_id=request_id,
            project_slug=project_slug,
            client_slug=client_slug,
            root_script=root_script,
            call_stack=call_stack,
            api_calls=api_calls,
            cost_usd=cost_usd,
            pricing_schedule=pricing_schedule
        )
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        data = asdict(self)
        
        # Convert datetime to ISO string
        data['timestamp'] = self.timestamp.isoformat()
        
        # Convert Decimal to float for JSON serialization
        if self.cost_usd is not None:
            data['cost_usd'] = float(self.cost_usd)
        
        # Convert stack frames to strings
        data['call_stack'] = [str(frame) for frame in self.call_stack]
        
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ServiceCall':
        """Create from dictionary (for deserialization)."""
        # Convert timestamp from ISO string
        if isinstance(data['timestamp'], str):
            data['timestamp'] = datetime.fromisoformat(data['timestamp'])
        
        # Convert cost back to Decimal
        if data.get('cost_usd') is not None:
            data['cost_usd'] = Decimal(str(data['cost_usd']))
        
        # Convert call_stack from strings back to StackFrame objects
        if 'call_stack' in data and isinstance(data['call_stack'], list):
            stack_frames = []
            for frame_str in data['call_stack']:
                if isinstance(frame_str, str):
                    # Parse "filename:function:line" format
                    parts = frame_str.split(':')
                    if len(parts) >= 3:
                        stack_frames.append(StackFrame(
                            filename=parts[0],
                            function_name=parts[1], 
                            line_number=int(parts[2])
                        ))
            data['call_stack'] = stack_frames
        
        return cls(**data)
    
    def get_total_tokens(self) -> int:
        """Get total tokens (input + output)."""
        return (self.input_tokens or 0) + (self.output_tokens or 0)
    
    def get_cost_per_1k_tokens(self) -> Optional[Decimal]:
        """Calculate cost per 1000 tokens."""
        total_tokens = self.get_total_tokens()
        if total_tokens > 0 and self.cost_usd is not None:
            return (self.cost_usd / total_tokens) * 1000
        return None