"""
Cost calculation for LLM and API calls.
"""

import json
import logging
from decimal import Decimal
from typing import Dict, Optional, Any
from pathlib import Path

logger = logging.getLogger(__name__)


class CostCalculator:
    """Handles cost calculation for various service types."""
    
    def __init__(self, pricing_schedule: str = "2025-01-v1"):
        self.pricing_schedule = pricing_schedule
        self._tokencost_available = self._check_tokencost()
        self._api_rates = self._load_api_rates()
    
    def _check_tokencost(self) -> bool:
        """Check if tokencost library is available."""
        try:
            import tokencost
            return True
        except ImportError:
            logger.warning("tokencost library not available - using fallback pricing")
            return False
    
    def _load_api_rates(self) -> Dict[str, Dict[str, float]]:
        """Load API pricing rates."""
        # Default API rates (can be overridden by config file)
        default_rates = {
            "firecrawl": {"per_call": 0.02},
            "mcp_server": {"per_call": 0.001},
            "weather": {"per_call": 0.001},
            "echostring": {"per_call": 0.0001}
        }
        
        # Try to load from config file
        try:
            config_path = Path(__file__).parent / "pricing_config.json"
            if config_path.exists():
                with open(config_path) as f:
                    config = json.load(f)
                    api_rates = config.get("api_rates", {})
                    default_rates.update(api_rates)
        except Exception as e:
            logger.warning(f"Could not load pricing config: {e}")
        
        return default_rates
    
    def calculate_llm_cost(self, provider: str, model: str, input_tokens: int, 
                          output_tokens: int, **kwargs) -> Optional[Decimal]:
        """
        Calculate cost for LLM calls.
        
        Uses tokencost library if available, otherwise fallback rates.
        """
        if self._tokencost_available:
            try:
                import tokencost
                
                # Calculate input and output costs separately
                input_cost = tokencost.calculate_cost_by_tokens(input_tokens, model, 'input')
                output_cost = tokencost.calculate_cost_by_tokens(output_tokens, model, 'output')
                
                total_cost = input_cost + output_cost
                return Decimal(str(total_cost)) if total_cost else None
                
            except Exception as e:
                logger.warning(f"tokencost calculation failed: {e}")
        
        # Fallback to manual rates
        return self._calculate_fallback_llm_cost(provider, model, input_tokens, output_tokens)
    
    def _calculate_fallback_llm_cost(self, provider: str, model: str, 
                                   input_tokens: int, output_tokens: int) -> Optional[Decimal]:
        """Fallback LLM cost calculation when tokencost is unavailable."""
        
        # Simplified pricing (per 1K tokens)
        fallback_rates = {
            "openai": {
                "gpt-4": {"input": 0.03, "output": 0.06},
                "gpt-4-turbo": {"input": 0.01, "output": 0.03},
                "gpt-3.5-turbo": {"input": 0.0015, "output": 0.002}
            },
            "anthropic": {
                "claude-3-5-sonnet": {"input": 0.003, "output": 0.015},
                "claude-3-5-haiku": {"input": 0.00025, "output": 0.00125}
            },
            "google": {
                "gemini-2.0-flash-exp": {"input": 0.000075, "output": 0.0003},
                "gemini-2.5-flash-lite": {"input": 0.000075, "output": 0.0003}
            },
            "gemini": {  # Alias for google models
                "gemini-2.0-flash-exp": {"input": 0.000075, "output": 0.0003},
                "gemini-2.5-flash-lite": {"input": 0.000075, "output": 0.0003}
            }
        }
        
        provider_rates = fallback_rates.get(provider.lower(), {})
        model_rates = provider_rates.get(model.lower(), {})
        
        if not model_rates:
            logger.warning(f"No fallback rates for {provider}/{model}")
            return None
        
        input_cost = Decimal(str(model_rates.get("input", 0))) * input_tokens / 1000
        output_cost = Decimal(str(model_rates.get("output", 0))) * output_tokens / 1000
        
        return input_cost + output_cost
    
    def calculate_api_cost(self, provider: str, service_name: str, 
                          api_calls: int = 1, **kwargs) -> Optional[Decimal]:
        """Calculate cost for API calls."""
        
        service_rates = self._api_rates.get(provider.lower(), {})
        if not service_rates:
            service_rates = self._api_rates.get(service_name.lower(), {})
        
        if not service_rates:
            logger.warning(f"No API rates found for {provider}/{service_name}")
            return None
        
        per_call_rate = service_rates.get("per_call", 0)
        return Decimal(str(per_call_rate)) * api_calls
    
    def get_pricing_info(self) -> Dict[str, Any]:
        """Get information about available pricing."""
        return {
            "pricing_schedule": self.pricing_schedule,
            "tokencost_available": self._tokencost_available,
            "api_providers": list(self._api_rates.keys())
        }