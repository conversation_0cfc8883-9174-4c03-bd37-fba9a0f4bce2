"""
Storage backends for accounting data.
"""

import json
import sqlite3
import logging
from abc import ABC, abstractmethod
from pathlib import Path
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta

from .models import ServiceCall

logger = logging.getLogger(__name__)


class StorageBackend(ABC):
    """Abstract base class for storage backends."""
    
    @abstractmethod
    def store_call(self, call: ServiceCall) -> None:
        """Store a service call."""
        pass
    
    @abstractmethod
    def get_calls(self, start_time: Optional[datetime] = None, 
                  end_time: Optional[datetime] = None,
                  service_type: Optional[str] = None,
                  provider: Optional[str] = None) -> List[ServiceCall]:
        """Retrieve service calls with optional filtering."""
        pass
    
    @abstractmethod  
    def get_call_count(self) -> int:
        """Get total number of stored calls."""
        pass
    
    @abstractmethod
    def clear_all_data(self) -> None:
        """Clear all stored data."""
        pass


class JSONLStorage(StorageBackend):
    """JSONL (JSON Lines) storage backend."""
    
    def __init__(self, data_dir: Path):
        self.data_dir = Path(data_dir)
        self.data_dir.mkdir(parents=True, exist_ok=True)
        self.calls_file = self.data_dir / "calls.jsonl"
    
    def store_call(self, call: ServiceCall) -> None:
        """Store a service call as JSON line."""
        try:
            with open(self.calls_file, 'a', encoding='utf-8') as f:
                json.dump(call.to_dict(), f, separators=(',', ':'))
                f.write('\n')
        except Exception as e:
            logger.error(f"Failed to store call to JSONL: {e}")
            raise
    
    def get_calls(self, start_time: Optional[datetime] = None, 
                  end_time: Optional[datetime] = None,
                  service_type: Optional[str] = None,
                  provider: Optional[str] = None) -> List[ServiceCall]:
        """Retrieve calls from JSONL file."""
        calls = []
        
        if not self.calls_file.exists():
            return calls
        
        try:
            with open(self.calls_file, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if not line:
                        continue
                    
                    try:
                        data = json.loads(line)
                        call = ServiceCall.from_dict(data)
                        
                        # Apply filters
                        if start_time and call.timestamp < start_time:
                            continue
                        if end_time and call.timestamp > end_time:
                            continue
                        if service_type and call.service_type != service_type:
                            continue
                        if provider and call.provider != provider:
                            continue
                        
                        calls.append(call)
                    except json.JSONDecodeError as e:
                        logger.warning(f"Invalid JSON line in calls file: {e}")
                        continue
                    except Exception as e:
                        logger.warning(f"Error parsing call data: {e}")
                        continue
        
        except Exception as e:
            logger.error(f"Failed to read calls from JSONL: {e}")
        
        return calls
    
    def get_call_count(self) -> int:
        """Get total number of lines in JSONL file."""
        if not self.calls_file.exists():
            return 0
        
        try:
            with open(self.calls_file, 'r', encoding='utf-8') as f:
                return sum(1 for line in f if line.strip())
        except Exception as e:
            logger.error(f"Failed to count calls in JSONL: {e}")
            return 0
    
    def clear_all_data(self) -> None:
        """Clear all JSONL data by removing the file."""
        try:
            if self.calls_file.exists():
                self.calls_file.unlink()
                logger.info(f"Cleared JSONL file: {self.calls_file}")
        except Exception as e:
            logger.error(f"Failed to clear JSONL data: {e}")
            raise


class SQLiteStorage(StorageBackend):
    """SQLite storage backend."""
    
    def __init__(self, data_dir: Path):
        self.data_dir = Path(data_dir)
        self.data_dir.mkdir(parents=True, exist_ok=True)
        self.db_path = self.data_dir / "accounting.db"
        self._init_db()
    
    def _init_db(self) -> None:
        """Initialize SQLite database and tables."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS service_calls (
                        call_id TEXT PRIMARY KEY,
                        timestamp TEXT NOT NULL,
                        service_type TEXT NOT NULL,
                        provider TEXT NOT NULL,
                        model TEXT NOT NULL,
                        root_script TEXT NOT NULL,
                        call_stack TEXT NOT NULL,
                        input_tokens INTEGER,
                        output_tokens INTEGER,
                        api_calls INTEGER,
                        cost_usd REAL,
                        pricing_schedule TEXT,
                        metadata TEXT
                    )
                """)
                
                # Create indexes for common queries
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_timestamp ON service_calls(timestamp)")
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_service_type ON service_calls(service_type)")
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_provider ON service_calls(provider)")
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_root_script ON service_calls(root_script)")
                
                conn.commit()
        except Exception as e:
            logger.error(f"Failed to initialize SQLite database: {e}")
            raise
    
    def store_call(self, call: ServiceCall) -> None:
        """Store a service call in SQLite."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    INSERT OR REPLACE INTO service_calls (
                        call_id, timestamp, service_type, provider, model,
                        root_script, call_stack, input_tokens, output_tokens,
                        api_calls, cost_usd, pricing_schedule, metadata
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    call.call_id,
                    call.timestamp.isoformat(),
                    call.service_type,
                    call.provider,
                    call.model,
                    call.root_script,
                    json.dumps([str(frame) for frame in call.call_stack]),
                    call.input_tokens,
                    call.output_tokens,
                    call.api_calls,
                    float(call.cost_usd) if call.cost_usd else None,
                    call.pricing_schedule,
                    json.dumps(call.metadata) if call.metadata else None
                ))
                conn.commit()
        except Exception as e:
            logger.error(f"Failed to store call to SQLite: {e}")
            raise
    
    def get_calls(self, start_time: Optional[datetime] = None, 
                  end_time: Optional[datetime] = None,
                  service_type: Optional[str] = None,
                  provider: Optional[str] = None) -> List[ServiceCall]:
        """Retrieve calls from SQLite with filtering."""
        calls = []
        
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # Build query with filters
                query = "SELECT * FROM service_calls WHERE 1=1"
                params = []
                
                if start_time:
                    query += " AND timestamp >= ?"
                    params.append(start_time.isoformat())
                if end_time:
                    query += " AND timestamp <= ?"
                    params.append(end_time.isoformat())
                if service_type:
                    query += " AND service_type = ?"
                    params.append(service_type)
                if provider:
                    query += " AND provider = ?"
                    params.append(provider)
                
                query += " ORDER BY timestamp DESC"
                
                cursor.execute(query, params)
                
                for row in cursor.fetchall():
                    try:
                        call_data = {
                            'call_id': row[0],
                            'timestamp': row[1],
                            'service_type': row[2],
                            'provider': row[3],
                            'model': row[4],
                            'root_script': row[5],
                            'call_stack': json.loads(row[6]) if row[6] else [],
                            'input_tokens': row[7],
                            'output_tokens': row[8],
                            'api_calls': row[9],
                            'cost_usd': row[10],
                            'pricing_schedule': row[11],
                            'metadata': json.loads(row[12]) if row[12] else None
                        }
                        calls.append(ServiceCall.from_dict(call_data))
                    except Exception as e:
                        logger.warning(f"Error parsing SQLite row: {e}")
                        continue
        
        except Exception as e:
            logger.error(f"Failed to retrieve calls from SQLite: {e}")
        
        return calls
    
    def get_call_count(self) -> int:
        """Get total number of calls in SQLite."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT COUNT(*) FROM service_calls")
                return cursor.fetchone()[0]
        except Exception as e:
            logger.error(f"Failed to count calls in SQLite: {e}")
            return 0
    
    def clear_all_data(self) -> None:
        """Clear all SQLite data."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("DELETE FROM service_calls")
                conn.commit()
                logger.info("Cleared all SQLite data")
        except Exception as e:
            logger.error(f"Failed to clear SQLite data: {e}")
            raise


class RocksDBStorage(StorageBackend):
    """RocksDB storage backend for high-performance scenarios."""
    
    def __init__(self, data_dir: Path):
        self.data_dir = Path(data_dir)
        self.data_dir.mkdir(parents=True, exist_ok=True)
        self.db_path = self.data_dir / "rocksdb"
        self._rocksdb_available = self._check_rocksdb()
        self.db = None
        
        if self._rocksdb_available:
            self._init_db()
    
    def _check_rocksdb(self) -> bool:
        """Check if RocksDB is available."""
        try:
            import rocksdb
            return True
        except ImportError:
            logger.warning("RocksDB not available - install with 'pip install python-rocksdb'")
            return False
    
    def _init_db(self) -> None:
        """Initialize RocksDB."""
        if not self._rocksdb_available:
            raise RuntimeError("RocksDB not available")
        
        try:
            import rocksdb
            
            opts = rocksdb.Options()
            opts.create_if_missing = True
            opts.max_open_files = 300000
            opts.write_buffer_size = 67108864
            opts.max_write_buffer_number = 3
            opts.target_file_size_base = 67108864
            
            self.db = rocksdb.DB(str(self.db_path), opts)
        except Exception as e:
            logger.error(f"Failed to initialize RocksDB: {e}")
            raise
    
    def store_call(self, call: ServiceCall) -> None:
        """Store a service call in RocksDB."""
        if not self.db:
            raise RuntimeError("RocksDB not initialized")
        
        try:
            # Use timestamp + call_id as key for chronological ordering
            key = f"{call.timestamp.isoformat()}:{call.call_id}"
            value = json.dumps(call.to_dict())
            
            self.db.put(key.encode('utf-8'), value.encode('utf-8'))
        except Exception as e:
            logger.error(f"Failed to store call to RocksDB: {e}")
            raise
    
    def get_calls(self, start_time: Optional[datetime] = None, 
                  end_time: Optional[datetime] = None,
                  service_type: Optional[str] = None,
                  provider: Optional[str] = None) -> List[ServiceCall]:
        """Retrieve calls from RocksDB with filtering."""
        if not self.db:
            return []
        
        calls = []
        
        try:
            # RocksDB iterator for range queries
            it = self.db.iterkeys()
            it.seek_to_first()
            
            for key in it:
                try:
                    key_str = key.decode('utf-8')
                    value = self.db.get(key)
                    if not value:
                        continue
                    
                    data = json.loads(value.decode('utf-8'))
                    call = ServiceCall.from_dict(data)
                    
                    # Apply filters
                    if start_time and call.timestamp < start_time:
                        continue
                    if end_time and call.timestamp > end_time:
                        continue
                    if service_type and call.service_type != service_type:
                        continue
                    if provider and call.provider != provider:
                        continue
                    
                    calls.append(call)
                except Exception as e:
                    logger.warning(f"Error parsing RocksDB entry: {e}")
                    continue
        
        except Exception as e:
            logger.error(f"Failed to retrieve calls from RocksDB: {e}")
        
        # Sort by timestamp (newest first)
        calls.sort(key=lambda x: x.timestamp, reverse=True)
        return calls
    
    def get_call_count(self) -> int:
        """Get approximate number of calls in RocksDB."""
        if not self.db:
            return 0
        
        try:
            count = 0
            it = self.db.iterkeys()
            it.seek_to_first()
            for _ in it:
                count += 1
            return count
        except Exception as e:
            logger.error(f"Failed to count calls in RocksDB: {e}")
            return 0
    
    def clear_all_data(self) -> None:
        """Clear all RocksDB data."""
        if not self.db:
            return
        
        try:
            # Delete all keys with the calls prefix
            it = self.db.iterkeys()
            it.seek(b"calls:")
            keys_to_delete = []
            
            for key in it:
                if key.startswith(b"calls:"):
                    keys_to_delete.append(key)
                else:
                    break
            
            for key in keys_to_delete:
                self.db.delete(key)
                
            logger.info(f"Cleared {len(keys_to_delete)} RocksDB entries")
        except Exception as e:
            logger.error(f"Failed to clear RocksDB data: {e}")
            raise


def create_storage_backend(backend_type: str, data_dir: Path) -> StorageBackend:
    """Factory function to create storage backends."""
    backend_type = backend_type.lower()
    
    if backend_type == "jsonl":
        return JSONLStorage(data_dir)
    elif backend_type == "sqlite":
        return SQLiteStorage(data_dir)
    elif backend_type == "rocksdb":
        return RocksDBStorage(data_dir)
    else:
        raise ValueError(f"Unknown storage backend: {backend_type}")