"""
Reporting and analytics for the accounting system.
"""

import logging
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from decimal import Decimal

from .system import AccountingSystem

logger = logging.getLogger(__name__)


class AccountingReporter:
    """Generates reports and analytics from accounting data."""
    
    def __init__(self, accounting_system: AccountingSystem):
        self.accounting = accounting_system
    
    def generate_summary(self, days: int = 7) -> Dict[str, Any]:
        """Generate cost and usage summary."""
        calls = self.accounting.get_calls(days=days)
        
        if not calls:
            return {
                "period_days": days,
                "total_calls": 0,
                "total_cost": 0.0,
                "by_service_type": {},
                "by_provider": {},
                "date_range": "No data"
            }
        
        # Calculate totals
        total_cost = sum((call.cost_usd or Decimal('0')) for call in calls)
        total_calls = len(calls)
        
        # Group by service type
        by_service = {}
        for call in calls:
            service_type = call.service_type
            if service_type not in by_service:
                by_service[service_type] = {"calls": 0, "cost": Decimal('0')}
            by_service[service_type]["calls"] += 1
            by_service[service_type]["cost"] += call.cost_usd or Decimal('0')
        
        # Group by provider
        by_provider = {}
        for call in calls:
            provider = call.provider
            if provider not in by_provider:
                by_provider[provider] = {"calls": 0, "cost": Decimal('0')}
            by_provider[provider]["calls"] += 1
            by_provider[provider]["cost"] += call.cost_usd or Decimal('0')
        
        # Date range
        dates = [call.timestamp for call in calls]
        date_range = f"{min(dates).date()} to {max(dates).date()}" if dates else "No data"
        
        return {
            "period_days": days,
            "total_calls": total_calls,
            "total_cost": float(total_cost),
            "by_service_type": {k: {"calls": v["calls"], "cost": float(v["cost"])} 
                              for k, v in by_service.items()},
            "by_provider": {k: {"calls": v["calls"], "cost": float(v["cost"])} 
                           for k, v in by_provider.items()},
            "date_range": date_range
        }
    
    def generate_script_breakdown(self, days: int = 7, top_n: int = 10) -> List[Dict[str, Any]]:
        """Generate cost breakdown by root script."""
        calls = self.accounting.get_calls(days=days)
        
        # Group by root script
        by_script = {}
        for call in calls:
            script = call.root_script
            if script not in by_script:
                by_script[script] = {
                    "calls": 0,
                    "cost": Decimal('0'),
                    "call_details": []
                }
            by_script[script]["calls"] += 1
            by_script[script]["cost"] += call.cost_usd or Decimal('0')
            by_script[script]["call_details"].append({
                "provider": call.provider,
                "model": call.model,
                "service_type": call.service_type,
                "cost": float(call.cost_usd or Decimal('0')),
                "call_stack": call.call_stack
            })
        
        # Sort by cost and return top N
        sorted_scripts = sorted(
            by_script.items(),
            key=lambda x: x[1]["cost"],
            reverse=True
        )[:top_n]
        
        return [
            {
                "root_script": script,
                "calls": data["calls"],
                "cost": float(data["cost"]),
                "call_details": data["call_details"]
            }
            for script, data in sorted_scripts
        ]
    
    def generate_model_analysis(self, days: int = 7) -> Dict[str, Any]:
        """Generate analysis by model/provider."""
        calls = self.accounting.get_calls(days=days)
        
        # Group by provider + model
        by_model = {}
        for call in calls:
            if call.service_type == "llm":
                key = f"{call.provider}/{call.model}"
                if key not in by_model:
                    by_model[key] = {
                        "calls": 0,
                        "total_tokens": 0,
                        "input_tokens": 0,
                        "output_tokens": 0,
                        "cost": Decimal('0')
                    }
                by_model[key]["calls"] += 1
                by_model[key]["input_tokens"] += call.input_tokens or 0
                by_model[key]["output_tokens"] += call.output_tokens or 0
                by_model[key]["total_tokens"] += call.get_total_tokens()
                by_model[key]["cost"] += call.cost_usd or Decimal('0')
        
        # Calculate efficiency metrics
        model_stats = {}
        for model, data in by_model.items():
            cost_per_1k = None
            if data["total_tokens"] > 0:
                cost_per_1k = float(data["cost"] / data["total_tokens"] * 1000)
            
            model_stats[model] = {
                "calls": data["calls"],
                "total_tokens": data["total_tokens"],
                "input_tokens": data["input_tokens"],
                "output_tokens": data["output_tokens"],
                "cost": float(data["cost"]),
                "avg_cost_per_call": float(data["cost"] / data["calls"]) if data["calls"] > 0 else 0,
                "cost_per_1k_tokens": cost_per_1k
            }
        
        return model_stats
    
    def print_summary_table(self, days: int = 7) -> str:
        """Generate a formatted text summary table."""
        try:
            from tabulate import tabulate
        except ImportError:
            logger.error("tabulate not available - install with 'pip install tabulate'")
            return "Tabulate library required for formatted output"
        
        summary = self.generate_summary(days)
        
        if summary["total_calls"] == 0:
            return f"📊 No accounting data found for the last {days} days"
        
        output = []
        output.append("📊 Accounting Summary")
        output.append("═" * 63)
        output.append("")
        output.append(f"💰 Total Cost: ${summary['total_cost']:.4f}")
        output.append(f"📞 Total Calls: {summary['total_calls']}")
        output.append(f"📅 Period: {summary['date_range']}")
        output.append("")
        
        # Service type breakdown
        if summary["by_service_type"]:
            output.append("By Service Type:")
            service_table = []
            for service, data in summary["by_service_type"].items():
                avg_cost = data["cost"] / data["calls"] if data["calls"] > 0 else 0
                service_table.append([
                    service.upper(),
                    data["calls"],
                    f"${data['cost']:.4f}",
                    f"${avg_cost:.4f}"
                ])
            
            output.append(tabulate(
                service_table,
                headers=["Service", "Calls", "Cost", "Avg/Call"],
                tablefmt="grid"
            ))
            output.append("")
        
        # Provider breakdown
        if summary["by_provider"]:
            output.append("By Provider:")
            provider_table = []
            for provider, data in summary["by_provider"].items():
                avg_cost = data["cost"] / data["calls"] if data["calls"] > 0 else 0
                provider_table.append([
                    provider,
                    data["calls"],
                    f"${data['cost']:.4f}",
                    f"${avg_cost:.4f}"
                ])
            
            output.append(tabulate(
                provider_table,
                headers=["Provider", "Calls", "Cost", "Avg/Call"],
                tablefmt="grid"
            ))
            output.append("")
        
        # Account Identity breakdown
        accounts = self.accounting.get_account_summary(days)
        if accounts:
            output.append("By Account Identity:")
            account_table = []
            for account_key, data in sorted(accounts.items(), key=lambda x: x[1]['total_cost'], reverse=True):
                avg_cost = data["total_cost"] / data["total_calls"] if data["total_calls"] > 0 else 0
                account_table.append([
                    account_key,
                    data["total_calls"],
                    f"${data['total_cost']:.4f}",
                    f"${avg_cost:.4f}"
                ])
            
            output.append(tabulate(
                account_table,
                headers=["Account", "Calls", "Cost", "Avg/Call"],
                tablefmt="grid"
            ))
        
        return "\n".join(output)
    
    def print_script_breakdown(self, days: int = 7, top_n: int = 5) -> str:
        """Generate a formatted script breakdown table."""
        try:
            from tabulate import tabulate
        except ImportError:
            return "Tabulate library required for formatted output"
        
        scripts = self.generate_script_breakdown(days, top_n)
        
        if not scripts:
            return f"📝 No script data found for the last {days} days"
        
        output = []
        output.append(f"📝 Cost by Root Script (Last {days} days)")
        output.append("═" * 63)
        output.append("")
        
        script_table = []
        for script_data in scripts:
            # Shorten script path for display
            script_name = script_data["root_script"].split("/")[-1]
            if len(script_name) > 40:
                script_name = "..." + script_name[-37:]
            
            script_table.append([
                script_name,
                script_data["calls"],
                f"${script_data['cost']:.4f}"
            ])
        
        output.append(tabulate(
            script_table,
            headers=["Root Script", "Calls", "Cost"],
            tablefmt="grid"
        ))
        
        # Add call stack details for top script
        if scripts:
            top_script = scripts[0]
            output.append("")
            output.append("🔍 Call Stack Details (Top Script):")
            
            unique_stacks = {}
            for detail in top_script["call_details"]:
                stack_str = " → ".join(str(frame) for frame in detail["call_stack"])
                if stack_str not in unique_stacks:
                    unique_stacks[stack_str] = {
                        "count": 0,
                        "cost": 0,
                        "provider": detail["provider"],
                        "model": detail["model"]
                    }
                unique_stacks[stack_str]["count"] += 1
                unique_stacks[stack_str]["cost"] += detail["cost"]
            
            for stack, data in list(unique_stacks.items())[:3]:  # Top 3 call stacks
                output.append(f"{stack}")
                output.append(f"  └─ {data['provider']}/{data['model']}: {data['count']} calls (${data['cost']:.4f})")
        
        return "\n".join(output)