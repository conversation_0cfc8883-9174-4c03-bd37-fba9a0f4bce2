"""
Main AccountingSystem class - the primary interface to the accounting system.
"""

import os
import logging
from pathlib import Path
from typing import Optional, Dict, Any
from decimal import Decimal

from .models import ServiceCall
from .attribution import StackAttributor
from .cost_calculation import CostCalculator
from .storage import create_storage_backend, StorageBackend
from gaia_ceto_v2.settings import GAIA_SETTINGS

logger = logging.getLogger(__name__)


class AccountingSystem:
    """
    Main accounting system for tracking LLM/API calls with cost attribution.
    
    Designed to be completely standalone with zero dependencies on gaia_ceto.
    """
    
    def __init__(self, data_dir: Optional[str] = None, 
                 storage_backend: str = "jsonl",
                 pricing_schedule: str = "2025-01-v1",
                 project_slug: str = "gaia_ceto",
                 client_slug: str = "internal"):
        """
        Initialize the accounting system.
        
        Args:
            data_dir: Directory for storing accounting data (default: /tmp/gaia_accounting)
            storage_backend: Storage backend type ('jsonl', 'sqlite', 'rocksdb')
            pricing_schedule: Pricing schedule version
            project_slug: Default project identifier (e.g., 'gaia_ceto')
            client_slug: Default client identifier (e.g., 'internal')
        """
        # Set default data directory
        if data_dir is None:
            data_dir = os.getenv('GAIA_ACCOUNTING_DIR', GAIA_SETTINGS.GAIA_ACCOUNTING_DIR)
        
        self.data_dir = Path(data_dir)
        self.storage_backend_type = storage_backend
        self.pricing_schedule = pricing_schedule
        self.default_project_slug = project_slug
        self.default_client_slug = client_slug
        
        # Initialize components
        self.cost_calculator = CostCalculator(pricing_schedule)
        self.attributor = StackAttributor()
        
        # Initialize storage backend
        try:
            self.storage: StorageBackend = create_storage_backend(storage_backend, self.data_dir)
            logger.info(f"Initialized accounting system: {data_dir} ({storage_backend})")
        except Exception as e:
            logger.error(f"Failed to initialize accounting system: {e}")
            raise
    
    def record_llm_call(self, provider: str, model: str, input_tokens: int, 
                       output_tokens: int, request_id: Optional[str] = None,
                       project_slug: Optional[str] = None, client_slug: Optional[str] = None,
                       **kwargs) -> Optional[str]:
        """
        Record an LLM call with automatic cost calculation and attribution.
        
        Args:
            provider: LLM provider (e.g., 'openai', 'anthropic', 'google')
            model: Model name (e.g., 'gpt-4', 'claude-3-5-sonnet')
            input_tokens: Number of input tokens
            output_tokens: Number of output tokens
            request_id: Optional request ID to correlate multiple calls
            project_slug: Project identifier (defaults to system default)
            client_slug: Client identifier (defaults to system default)
            **kwargs: Additional metadata
            
        Returns:
            Call ID if successful, None if failed
        """
        try:
            # Calculate cost
            cost = self.cost_calculator.calculate_llm_cost(
                provider, model, input_tokens, output_tokens, **kwargs
            )
            
            # Get call attribution (skip 1 frame to exclude this method)
            root_script, call_stack = self.attributor.get_call_attribution(skip_frames=1)
            
            # Create service call record
            call = ServiceCall.create_llm_call(
                provider=provider,
                model=model,
                input_tokens=input_tokens,
                output_tokens=output_tokens,
                cost_usd=cost or Decimal('0'),
                root_script=root_script,
                call_stack=call_stack,
                pricing_schedule=self.pricing_schedule,
                request_id=request_id,
                project_slug=project_slug or self.default_project_slug,
                client_slug=client_slug or self.default_client_slug
            )
            
            # Add metadata
            if kwargs:
                call.metadata = kwargs
            
            # Store the call
            self.storage.store_call(call)
            
            # Log summary
            logger.info(f"Recorded LLM call: {provider}/{model} "
                       f"{input_tokens}→{output_tokens} tokens ${cost or 0:.4f}")
            
            return call.call_id
            
        except Exception as e:
            logger.error(f"Failed to record LLM call: {e}")
            return None
    
    def record_api_call(self, provider: str, service_name: str, 
                       api_calls: int = 1, request_id: Optional[str] = None,
                       project_slug: Optional[str] = None, client_slug: Optional[str] = None,
                       **kwargs) -> Optional[str]:
        """
        Record an API call with automatic cost calculation and attribution.
        
        Args:
            provider: API provider (e.g., 'firecrawl', 'mcp_server')
            service_name: Service/endpoint name
            api_calls: Number of API calls made
            request_id: Optional request ID to correlate multiple calls
            project_slug: Project identifier (defaults to system default)
            client_slug: Client identifier (defaults to system default)
            **kwargs: Additional metadata
            
        Returns:
            Call ID if successful, None if failed
        """
        try:
            # Calculate cost
            cost = self.cost_calculator.calculate_api_cost(
                provider, service_name, api_calls, **kwargs
            )
            
            # Get call attribution (skip 1 frame to exclude this method)
            root_script, call_stack = self.attributor.get_call_attribution(skip_frames=1)
            
            # Create service call record
            call = ServiceCall.create_api_call(
                provider=provider,
                service_name=service_name,
                api_calls=api_calls,
                cost_usd=cost or Decimal('0'),
                root_script=root_script,
                call_stack=call_stack,
                pricing_schedule=self.pricing_schedule,
                request_id=request_id,
                project_slug=project_slug or self.default_project_slug,
                client_slug=client_slug or self.default_client_slug
            )
            
            # Add metadata
            if kwargs:
                call.metadata = kwargs
            
            # Store the call
            self.storage.store_call(call)
            
            # Log summary
            logger.info(f"Recorded API call: {provider}/{service_name} "
                       f"{api_calls} calls ${cost or 0:.4f}")
            
            return call.call_id
            
        except Exception as e:
            logger.error(f"Failed to record API call: {e}")
            return None
    
    def get_calls(self, days: int = 7, service_type: Optional[str] = None,
                  provider: Optional[str] = None):
        """Get recent calls with optional filtering."""
        from datetime import datetime, timedelta
        
        start_time = datetime.utcnow() - timedelta(days=days)
        return self.storage.get_calls(
            start_time=start_time,
            service_type=service_type,
            provider=provider
        )
    
    def get_total_cost(self, days: int = 7) -> Decimal:
        """Get total cost for the specified period."""
        calls = self.get_calls(days=days)
        return sum((call.cost_usd or Decimal('0')) for call in calls)
    
    def get_call_count(self) -> int:
        """Get total number of stored calls."""
        return self.storage.get_call_count()
    
    def get_calls_by_request(self, request_id: str):
        """Get all calls associated with a specific request ID."""
        calls = self.get_calls(days=30)  # Look back 30 days
        return [call for call in calls if call.request_id == request_id]
    
    def get_calls_by_account(self, project_slug: str = None, client_slug: str = None, days: int = 7):
        """Get calls filtered by account identity (project + client)."""
        calls = self.get_calls(days=days)
        
        filtered_calls = calls
        if project_slug is not None:
            filtered_calls = [call for call in filtered_calls if call.project_slug == project_slug]
        if client_slug is not None:
            filtered_calls = [call for call in filtered_calls if call.client_slug == client_slug]
            
        return filtered_calls
    
    def get_account_summary(self, days: int = 7) -> Dict[str, Dict[str, Any]]:
        """Get cost summary grouped by account identity (project_slug + client_slug)."""
        calls = self.get_calls(days=days)
        
        # Group by account identity
        accounts = {}
        for call in calls:
            account_key = f"{call.project_slug}/{call.client_slug}"
            
            if account_key not in accounts:
                accounts[account_key] = {
                    'project_slug': call.project_slug,
                    'client_slug': call.client_slug,
                    'total_cost': Decimal('0'),
                    'total_calls': 0,
                    'llm_calls': 0,
                    'api_calls': 0,
                    'llm_cost': Decimal('0'),
                    'api_cost': Decimal('0')
                }
            
            account = accounts[account_key]
            account['total_cost'] += (call.cost_usd or Decimal('0'))
            account['total_calls'] += 1
            
            if call.service_type == 'llm':
                account['llm_calls'] += 1
                account['llm_cost'] += (call.cost_usd or Decimal('0'))
            else:
                account['api_calls'] += 1
                account['api_cost'] += (call.cost_usd or Decimal('0'))
        
        return accounts
    
    def to_dataframe(self):
        """Convert recent calls to pandas DataFrame for analysis."""
        try:
            import pandas as pd
            
            calls = self.get_calls(days=30)  # Last 30 days
            if not calls:
                # Return empty DataFrame with correct schema
                return pd.DataFrame(columns=[
                    'call_id', 'timestamp', 'service_type', 'provider', 'model',
                    'root_script', 'call_stack', 'input_tokens', 'output_tokens',
                    'api_calls', 'cost_usd', 'pricing_schedule'
                ])
            
            # Convert to list of dicts
            data = []
            for call in calls:
                call_dict = call.to_dict()
                # Format call_stack as string for DataFrame
                call_dict['call_stack'] = self.attributor.format_call_stack(call.call_stack)
                data.append(call_dict)
            
            return pd.DataFrame(data)
            
        except ImportError:
            logger.error("pandas not available - install with 'pip install pandas'")
            raise
        except Exception as e:
            logger.error(f"Failed to create DataFrame: {e}")
            raise
    
    def export_to_csv(self, output_path: str, days: int = 30) -> bool:
        """Export accounting data to CSV."""
        try:
            df = self.to_dataframe()
            if df.empty:
                logger.warning("No data to export")
                return False
            
            # Filter by days
            if days < 30:
                from datetime import datetime, timedelta
                cutoff = datetime.utcnow() - timedelta(days=days)
                df = df[pd.to_datetime(df['timestamp']) >= cutoff]
            
            df.to_csv(output_path, index=False)
            logger.info(f"Exported {len(df)} records to {output_path}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to export to CSV: {e}")
            return False
    
    def get_system_info(self) -> Dict[str, Any]:
        """Get information about the accounting system."""
        return {
            "data_dir": str(self.data_dir),
            "storage_backend": self.storage_backend_type,
            "pricing_schedule": self.pricing_schedule,
            "total_calls": self.get_call_count(),
            "total_cost_7d": float(self.get_total_cost(days=7)),
            "pricing_info": self.cost_calculator.get_pricing_info()
        }
    
    def clear_all_data(self) -> int:
        """Clear all accounting data."""
        try:
            count = self.storage.get_call_count()
            self.storage.clear_all_data()
            logger.info(f"Cleared {count} accounting records")
            return count
        except Exception as e:
            logger.error(f"Failed to clear accounting data: {e}")
            raise