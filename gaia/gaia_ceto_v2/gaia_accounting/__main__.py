"""
CLI interface for gaia_accounting.

Usage:
    python -m gaia_accounting summary --days 7
    python -m gaia_accounting scripts --top 10
    python -m gaia_accounting models --provider openai
    python -m gaia_accounting export --format csv --output costs.csv
"""

import argparse
import sys
import logging
from pathlib import Path

from .system import AccountingSystem
from .reporting import AccountingReporter

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def cmd_summary(args):
    """Generate cost and usage summary."""
    try:
        accounting = AccountingSystem(
            data_dir=args.accounting_dir,
            storage_backend=args.backend
        )
        reporter = AccountingReporter(accounting)
        
        if args.csv:
            # Output CSV format with account breakdown
            import csv
            import sys
            
            summary = reporter.generate_summary(args.days)
            accounts = accounting.get_account_summary(args.days)
            
            writer = csv.writer(sys.stdout)
            writer.writerow(['period_days', 'total_calls', 'total_cost'])
            writer.writerow([summary['period_days'], summary['total_calls'], f"{summary['total_cost']:.4f}"])
            writer.writerow([])  # Empty line
            writer.writerow(['account', 'calls', 'cost', 'avg_cost'])
            
            for account_key, data in sorted(accounts.items(), key=lambda x: x[1]['total_cost'], reverse=True):
                avg_cost = data["total_cost"] / data["total_calls"] if data["total_calls"] > 0 else 0
                writer.writerow([account_key, data["total_calls"], f"{data['total_cost']:.4f}", f"{avg_cost:.4f}"])
        else:
            # Pretty table format
            output = reporter.print_summary_table(args.days)
            print(output)
    
    except Exception as e:
        logger.error(f"Failed to generate summary: {e}")
        sys.exit(1)


def cmd_scripts(args):
    """Generate cost breakdown by script."""
    try:
        accounting = AccountingSystem(
            data_dir=args.accounting_dir,
            storage_backend=args.backend
        )
        reporter = AccountingReporter(accounting)
        
        if args.csv:
            # CSV format
            scripts = reporter.generate_script_breakdown(args.days, args.top)
            print("root_script,calls,cost")
            for script_data in scripts:
                script_name = script_data["root_script"].split("/")[-1]
                print(f"{script_name},{script_data['calls']},{script_data['cost']}")
        else:
            # Pretty table format
            output = reporter.print_script_breakdown(args.days, args.top)
            print(output)
    
    except Exception as e:
        logger.error(f"Failed to generate script breakdown: {e}")
        sys.exit(1)


def cmd_models(args):
    """Generate model analysis."""
    try:
        accounting = AccountingSystem(
            data_dir=args.accounting_dir,
            storage_backend=args.backend
        )
        reporter = AccountingReporter(accounting)
        
        model_stats = reporter.generate_model_analysis(args.days)
        
        if args.provider:
            # Filter by provider
            model_stats = {k: v for k, v in model_stats.items() if args.provider.lower() in k.lower()}
        
        if args.csv:
            # CSV format
            print("model,calls,total_tokens,input_tokens,output_tokens,cost,avg_cost_per_call,cost_per_1k_tokens")
            for model, stats in model_stats.items():
                print(f"{model},{stats['calls']},{stats['total_tokens']},{stats['input_tokens']},"
                      f"{stats['output_tokens']},{stats['cost']},{stats['avg_cost_per_call']},"
                      f"{stats['cost_per_1k_tokens'] or 'N/A'}")
        else:
            # Pretty format
            try:
                from tabulate import tabulate
                
                table_data = []
                for model, stats in model_stats.items():
                    table_data.append([
                        model,
                        stats['calls'],
                        f"{stats['total_tokens']:,}",
                        f"${stats['cost']:.4f}",
                        f"${stats['avg_cost_per_call']:.4f}",
                        f"${stats['cost_per_1k_tokens']:.4f}" if stats['cost_per_1k_tokens'] else "N/A"
                    ])
                
                print("🤖 Model Analysis")
                print("═" * 80)
                print(tabulate(
                    table_data,
                    headers=["Model", "Calls", "Tokens", "Cost", "Avg/Call", "$/1K Tokens"],
                    tablefmt="grid"
                ))
            except ImportError:
                print("Model Analysis (tabulate not available):")
                for model, stats in model_stats.items():
                    print(f"{model}: {stats['calls']} calls, ${stats['cost']:.4f}")
    
    except Exception as e:
        logger.error(f"Failed to generate model analysis: {e}")
        sys.exit(1)


def cmd_export(args):
    """Export accounting data to CSV."""
    try:
        accounting = AccountingSystem(
            data_dir=args.accounting_dir,
            storage_backend=args.backend
        )
        
        if args.format.lower() == 'csv':
            success = accounting.export_to_csv(args.output, args.days)
            if success:
                print(f"✅ Exported accounting data to {args.output}")
            else:
                print("❌ Export failed")
                sys.exit(1)
        else:
            print(f"Unsupported format: {args.format}")
            sys.exit(1)
    
    except Exception as e:
        logger.error(f"Failed to export data: {e}")
        sys.exit(1)


def cmd_clear(args):
    """Clear accounting data."""
    try:
        accounting = AccountingSystem(
            data_dir=args.accounting_dir,
            storage_backend=args.backend
        )
        
        if args.confirm:
            count = accounting.clear_all_data()
            print(f"✅ Cleared {count} accounting records")
        else:
            print("❌ Use --confirm to actually clear data")
            
    except Exception as e:
        logger.error(f"Failed to clear accounting data: {e}")
        sys.exit(1)


def cmd_info(args):
    """Show accounting system information."""
    try:
        accounting = AccountingSystem(
            data_dir=args.accounting_dir,
            storage_backend=args.backend
        )
        
        info = accounting.get_system_info()
        
        print("🔧 Accounting System Information")
        print("═" * 40)
        print(f"Data Directory: {info['data_dir']}")
        print(f"Storage Backend: {info['storage_backend']}")
        print(f"Pricing Schedule: {info['pricing_schedule']}")
        print(f"Total Calls: {info['total_calls']:,}")
        print(f"7-Day Cost: ${info['total_cost_7d']:.4f}")
        print("")
        print("Pricing Info:")
        pricing = info['pricing_info']
        print(f"  - TokenCost Available: {pricing['tokencost_available']}")
        print(f"  - API Providers: {', '.join(pricing['api_providers'])}")
    
    except Exception as e:
        logger.error(f"Failed to get system info: {e}")
        sys.exit(1)


def cmd_accounts(args):
    """Show account identity breakdown."""
    try:
        accounting = AccountingSystem(
            data_dir=args.accounting_dir,
            storage_backend=args.backend
        )
        
        if args.csv:
            # CSV output
            import csv
            import sys
            
            accounts = accounting.get_account_summary(args.days)
            
            writer = csv.writer(sys.stdout)
            writer.writerow(['Account', 'Project', 'Client', 'Total Cost', 'Total Calls', 'LLM Calls', 'API Calls', 'LLM Cost', 'API Cost'])
            
            for account_key, data in accounts.items():
                writer.writerow([
                    account_key,
                    data['project_slug'],
                    data['client_slug'],
                    f"{data['total_cost']:.4f}",
                    data['total_calls'],
                    data['llm_calls'],
                    data['api_calls'],
                    f"{data['llm_cost']:.4f}",
                    f"{data['api_cost']:.4f}"
                ])
        else:
            # Table output
            accounts = accounting.get_account_summary(args.days)
            
            print("📊 Account Identity Breakdown")
            print("═" * 80)
            print(f"📅 Period: Last {args.days} days")
            if args.project:
                print(f"🏷️  Project Filter: {args.project}")
            if args.client:
                print(f"👤 Client Filter: {args.client}")
            print()
            
            if not accounts:
                print("No data found for the specified period.")
                return
            
            # Filter if requested
            if args.project or args.client:
                filtered_accounts = {}
                for account_key, data in accounts.items():
                    if args.project and data['project_slug'] != args.project:
                        continue
                    if args.client and data['client_slug'] != args.client:
                        continue
                    filtered_accounts[account_key] = data
                accounts = filtered_accounts
            
            if not accounts:
                print(f"No accounts found matching filters.")
                return
            
            # Sort by total cost descending
            sorted_accounts = sorted(accounts.items(), key=lambda x: x[1]['total_cost'], reverse=True)
            
            print(f"{'Account Identity':<25} {'Total Cost':<12} {'Calls':<6} {'LLM':<6} {'API':<6}")
            print("─" * 60)
            
            total_cost = 0
            total_calls = 0
            for account_key, data in sorted_accounts:
                total_cost += data['total_cost']
                total_calls += data['total_calls']
                
                print(f"{account_key:<25} ${data['total_cost']:<11.4f} {data['total_calls']:<6} "
                      f"{data['llm_calls']:<6} {data['api_calls']:<6}")
            
            print("─" * 60)
            print(f"{'TOTAL':<25} ${total_cost:<11.4f} {total_calls:<6}")
    
    except Exception as e:
        logger.error(f"Failed to generate account breakdown: {e}")
        sys.exit(1)


def main():
    """Main CLI entry point."""
    parser = argparse.ArgumentParser(
        description="Gaia Accounting System CLI",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python -m gaia_accounting summary --days 7
  python -m gaia_accounting scripts --top 10 --csv
  python -m gaia_accounting models --provider openai
  python -m gaia_accounting accounts --days 7 --project gaia_ceto
  python -m gaia_accounting export --format csv --output costs.csv
  python -m gaia_accounting info
        """
    )
    
    # Global arguments
    parser.add_argument('--accounting-dir', 
                       default=None,
                       help='Accounting data directory (default: /tmp/gaia_accounting)')
    parser.add_argument('--backend', 
                       choices=['jsonl', 'sqlite', 'rocksdb'],
                       default='jsonl',
                       help='Storage backend (default: jsonl)')
    parser.add_argument('--verbose', '-v',
                       action='store_true',
                       help='Enable verbose logging')
    
    # Subcommands
    subparsers = parser.add_subparsers(dest='command', help='Available commands')
    
    # Summary command
    summary_parser = subparsers.add_parser('summary', help='Generate cost summary')
    summary_parser.add_argument('--days', type=int, default=7, help='Number of days to analyze')
    summary_parser.add_argument('--csv', action='store_true', help='Output in CSV format')
    
    # Scripts command
    scripts_parser = subparsers.add_parser('scripts', help='Cost breakdown by script')
    scripts_parser.add_argument('--days', type=int, default=7, help='Number of days to analyze')
    scripts_parser.add_argument('--top', type=int, default=10, help='Show top N scripts')
    scripts_parser.add_argument('--csv', action='store_true', help='Output in CSV format')
    
    # Models command
    models_parser = subparsers.add_parser('models', help='Model analysis')
    models_parser.add_argument('--days', type=int, default=7, help='Number of days to analyze')
    models_parser.add_argument('--provider', help='Filter by provider')
    models_parser.add_argument('--csv', action='store_true', help='Output in CSV format')
    
    # Export command
    export_parser = subparsers.add_parser('export', help='Export data')
    export_parser.add_argument('--format', choices=['csv'], default='csv', help='Export format')
    export_parser.add_argument('--output', required=True, help='Output file path')
    export_parser.add_argument('--days', type=int, default=30, help='Number of days to export')
    
    # Accounts command
    accounts_parser = subparsers.add_parser('accounts', help='Account identity breakdown')
    accounts_parser.add_argument('--days', type=int, default=7, help='Number of days to analyze')
    accounts_parser.add_argument('--project', help='Filter by project slug')
    accounts_parser.add_argument('--client', help='Filter by client slug')
    accounts_parser.add_argument('--csv', action='store_true', help='Output in CSV format')
    
    # Info command
    info_parser = subparsers.add_parser('info', help='Show system information')
    
    # Clear command
    clear_parser = subparsers.add_parser('clear', help='Clear accounting data')
    clear_parser.add_argument('--confirm', action='store_true', help='Confirm data deletion')
    
    # Parse arguments
    args = parser.parse_args()
    
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # Execute command
    if args.command == 'summary':
        cmd_summary(args)
    elif args.command == 'scripts':
        cmd_scripts(args)
    elif args.command == 'models':
        cmd_models(args)
    elif args.command == 'export':
        cmd_export(args)
    elif args.command == 'accounts':
        cmd_accounts(args)
    elif args.command == 'info':
        cmd_info(args)
    elif args.command == 'clear':
        cmd_clear(args)
    else:
        parser.print_help()
        sys.exit(1)


if __name__ == '__main__':
    main()