"""
Stack attribution for capturing call origins.
"""

import inspect
import os
from pathlib import Path
from typing import List

from .models import StackFrame


class StackAttributor:
    """Captures stack traces for call attribution."""
    
    @staticmethod
    def get_call_attribution(skip_frames: int = 0) -> tuple[str, List[StackFrame]]:
        """
        Capture root script and call stack for attribution.
        
        Args:
            skip_frames: Number of frames to skip (to exclude attribution internals)
            
        Returns:
            Tuple of (root_script_path, list_of_stack_frames)
        """
        # Get the current stack
        stack = inspect.stack()
        
        # Skip internal frames (this function + skip_frames)
        relevant_frames = stack[1 + skip_frames:]
        
        # Find the root script (bottommost frame)
        root_script = "unknown"
        if relevant_frames:
            root_frame = relevant_frames[-1]
            root_script = os.path.abspath(root_frame.filename)
        
        # Capture last 3 meaningful frames for call stack
        meaningful_frames = []
        for frame_info in relevant_frames[-3:]:
            # Skip frames from system libraries or internals
            if StackAttributor._is_meaningful_frame(frame_info):
                meaningful_frames.append(StackFrame(
                    filename=os.path.basename(frame_info.filename),
                    function_name=frame_info.function,
                    line_number=frame_info.lineno
                ))
        
        return root_script, meaningful_frames
    
    @staticmethod
    def _is_meaningful_frame(frame_info) -> bool:
        """
        Determine if a stack frame is meaningful for attribution.
        
        Filters out system libraries, site-packages, etc.
        """
        filename = frame_info.filename
        
        # Skip system/library frames
        skip_patterns = [
            '/usr/lib/python',
            '/usr/local/lib/python', 
            'site-packages/',
            '/opt/conda/',
            '/home/<USER>/lib/python',
            '<frozen',
            '<built-in'
        ]
        
        for pattern in skip_patterns:
            if pattern in filename:
                return False
        
        # Skip internal accounting frames
        if 'gaia_accounting' in filename:
            return False
            
        return True
    
    @staticmethod
    def format_call_stack(call_stack: List[StackFrame]) -> str:
        """Format call stack as readable string."""
        if not call_stack:
            return "unknown"
            
        return " → ".join(str(frame) for frame in call_stack)