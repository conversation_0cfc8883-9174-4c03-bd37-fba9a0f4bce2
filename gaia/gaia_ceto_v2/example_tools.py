#!/usr/bin/env python3
"""
Example tools using Level 0035 ToolResponse

Shows the four main patterns in simple implementations.
"""

from core.tool_response import ToolResponse


def exa_research(query: str) -> ToolResponse:
    """Research tool - formatted output, always stored as document."""
    # Simulate research
    results = f"""Research Results for '{query}':

1. Falcon 9 (by SpaceX)
   Description: Partially reusable rocket with high success rate
   Sources: https://spacex.com/vehicles/falcon-9

2. Falcon Heavy (by SpaceX)  
   Description: Super heavy-lift vehicle with reusable boosters
   Sources: https://spacex.com/vehicles/falcon-heavy

3. Space Shuttle (by NASA)
   Description: First reusable spacecraft system (1981-2011)
   Sources: https://nasa.gov/mission_pages/shuttle/
"""
    
    # All research stored as documents per tool configuration
    return ToolResponse(
        content=results,
        include_in_context=True,         # User might ask about specific items
        needs_synthesis=False,           # Already well formatted
        store_as_document=True,          # All research stored as docs
        content_format="research_report"
    )


def generate_chart(chart_type: str, data: list) -> ToolResponse:
    """Chart generator - SVG output needs processing."""
    # Simulate SVG generation
    svg_content = f"""<svg width="400" height="300">
    <rect width="100" height="{data[0]*10}" x="50" y="200" fill="blue"/>
    <rect width="100" height="{data[1]*10}" x="150" y="200" fill="red"/>
    <rect width="100" height="{data[2]*10}" x="250" y="200" fill="green"/>
    <text x="200" y="20" text-anchor="middle">{chart_type} Chart</text>
</svg>"""
    
    # SVG is noise for LLM context, but needs explanation
    return ToolResponse(
        content=svg_content,
        include_in_context=False,       # SVG pollutes context
        needs_synthesis=True,           # LLM should describe chart
        show_to_user=False,             # Don't show raw SVG text
        store_as_document=True,         # Store for rendering
        content_format="svg",
        metadata={
            'chart_type': chart_type,
            'data_points': len(data),
            'render_hint': 'chart'
        }
    )


def weather_lookup(location: str) -> ToolResponse:
    """Simple lookup - stored as document per tool configuration."""
    # Simulate weather API
    weather_data = f"Weather in {location}: 22°C, sunny, 65% humidity"
    
    # Even simple weather stored as document per tool configuration
    return ToolResponse(
        content=weather_data,
        include_in_context=True,        # Available for context
        needs_synthesis=False,          # Already formatted
        store_as_document=True,         # Weather tool configured to store docs
        content_format="weather_status"
    )


def large_dataset_analysis(dataset_name: str) -> ToolResponse:
    """Large dataset tool - store full data, show summary."""
    # Simulate large dataset processing
    full_data = f"""Dataset Analysis: {dataset_name}

Raw Data:
{chr(10).join([f"Row {i}: value_{i}, metric_{i*2}, score_{i*3}" for i in range(1000)])}

Statistical Summary:
- Total rows: 1000
- Average score: 1500
- Max value: 999
- Min value: 1

Insights:
- Linear growth pattern detected
- No outliers found
- Data quality: Excellent
"""
    
    # Create summary for display
    summary = f"""Dataset '{dataset_name}' analyzed: 1000 rows processed
- Average score: 1500
- Pattern: Linear growth
- Quality: Excellent
- Full analysis stored for reference"""
    
    return ToolResponse(
        content=summary,                # Show summary
        include_in_context=True,        # User will ask follow-ups about data
        needs_synthesis=False,          # Summary already formatted
        store_as_document=True,         # Store full analysis
        content_format="dataset_analysis",
        metadata={
            'dataset': dataset_name,
            'row_count': 1000,
            'full_size': len(full_data)
        }
    )


# Legacy tool for compatibility testing
def old_weather_tool(location: str) -> str:
    """Old-style tool returning plain string."""
    return f"Temperature in {location} is 20°C"


if __name__ == "__main__":
    # Test all patterns
    print("🧪 Testing Level 0035 Tool Response Patterns\n")
    
    from core.tool_response import handle_tool_response
    
    # Test 1: Research tool
    print("1. Research Tool:")
    research_result = exa_research("rockets")
    processed = handle_tool_response("exa_research", research_result)
    print(f"   Context: {'Yes' if processed['context_content'] else 'No'}")
    print(f"   Synthesis: {'Yes' if processed['needs_synthesis'] else 'No'}")
    print(f"   Content: {processed['content'][:100]}...")
    print()
    
    # Test 2: Chart tool  
    print("2. Chart Tool:")
    chart_result = generate_chart("Sales", [10, 15, 12])
    processed = handle_tool_response("generate_chart", chart_result)
    print(f"   Context: {'Yes' if processed['context_content'] else 'No'}")
    print(f"   Synthesis: {'Yes' if processed['needs_synthesis'] else 'No'}")
    print(f"   Document: {processed['document_id']}")
    print(f"   Show User: {'Yes' if processed['content'] else 'No'}")
    print()
    
    # Test 3: Weather tool
    print("3. Weather Tool:")
    weather_result = weather_lookup("Paris")
    processed = handle_tool_response("weather_lookup", weather_result)
    print(f"   Context: {'Yes' if processed['context_content'] else 'No'}")
    print(f"   Synthesis: {'Yes' if processed['needs_synthesis'] else 'No'}")
    print(f"   Content: {processed['content']}")
    print()
    
    # Test 4: Large dataset
    print("4. Dataset Tool:")
    dataset_result = large_dataset_analysis("sales_2024")
    processed = handle_tool_response("dataset_analysis", dataset_result)
    print(f"   Context: {'Yes' if processed['context_content'] else 'No'}")
    print(f"   Document: {processed['document_id']}")
    print(f"   Content: {processed['content']}")
    print()
    
    # Test 5: Legacy tool
    print("5. Legacy Tool:")
    legacy_result = old_weather_tool("London")
    processed = handle_tool_response("old_weather", legacy_result)
    print(f"   Context: {'Yes' if processed['context_content'] else 'No'}")
    print(f"   Content: {processed['content']}")
    
    print("\n✅ All patterns tested successfully!")