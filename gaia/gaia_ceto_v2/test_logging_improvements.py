#!/usr/bin/env python3
"""Test the new logging improvements for flag violations"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import subprocess
import time

# Run test with research
print("Running research test to check for FLAG_VIOLATION and MIXED_OUTPUT warnings...")
process = subprocess.Popen(
    [sys.executable, '-m', 'gaia_ceto_v2.terminal_chat', '--verbose', '--provider', 'gemini', '--with-mcp'],
    stdin=subprocess.PIPE,
    stdout=subprocess.PIPE,
    stderr=subprocess.PIPE,
    text=True,
    cwd=os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
)

# Send query and wait
stdout, stderr = process.communicate("research rocket ships\nexit\n", timeout=30)

# Check for our new log messages
print("\n=== Checking for FLAG_VIOLATION ===")
if "FLAG_VIOLATION" in stderr:
    for line in stderr.split('\n'):
        if "FLAG_VIOLATION" in line:
            print(f"FOUND: {line}")
else:
    print("NOT FOUND - Flag violation not logged")

print("\n=== Checking for MIXED_OUTPUT ===")
if "MIXED_OUTPUT" in stderr:
    for line in stderr.split('\n'):
        if "MIXED_OUTPUT" in line:
            print(f"FOUND: {line}")
else:
    print("NOT FOUND - Mixed output not logged")

print("\n=== Checking for DISPLAY_SUMMARY ===")
if "DISPLAY_SUMMARY" in stderr:
    for line in stderr.split('\n'):
        if "DISPLAY_SUMMARY" in line:
            print(f"FOUND: {line}")
else:
    print("NOT FOUND - Display summary not logged")