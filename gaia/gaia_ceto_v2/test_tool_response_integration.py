#!/usr/bin/env python3
"""
Test Level 0035 integration with existing systems

Simple test to verify ToolResponse works with chat manager.
"""

from core.tool_response import ToolResponse, handle_tool_response, get_document


def test_integration():
    """Test that ToolResponse integrates properly."""
    print("🧪 Testing Level 0035 Integration\n")
    
    # Test 1: Legacy string tool (backward compatibility)
    print("1. Legacy String Tool:")
    legacy_result = "Simple weather: 22°C, sunny"
    processed = handle_tool_response("weather", legacy_result)
    print(f"   ✓ Content: {processed['content']}")
    print(f"   ✓ Context: {'Yes' if processed['context_content'] else 'No'}")
    print()
    
    # Test 2: Research tool with context
    print("2. Research Tool (Include in Context):")
    research = ToolResponse(
        content="Falcon 9: Reusable rocket by SpaceX with 95% success rate",
        include_in_context=True,
        needs_synthesis=False
    )
    processed = handle_tool_response("research", research)
    print(f"   ✓ Content: {processed['content']}")
    print(f"   ✓ Context: {processed['context_content'][:50]}...")
    print()
    
    # Test 3: Chart tool with document storage
    print("3. Chart Tool (Store Document):")
    chart = ToolResponse(
        content='<svg><rect width="100" height="50"/></svg>',
        include_in_context=False,
        needs_synthesis=True,
        show_to_user=False,
        store_as_document=True,
        content_format="svg"
    )
    processed = handle_tool_response("chart", chart)
    print(f"   ✓ Document ID: {processed['document_id']}")
    print(f"   ✓ Needs Synthesis: {processed['needs_synthesis']}")
    print(f"   ✓ Show User: {'Yes' if processed['content'] else 'No'}")
    
    # Test document retrieval
    if processed['document_id']:
        doc_content = get_document(processed['document_id'])
        print(f"   ✓ Retrieved Doc: {doc_content[:30]}...")
    print()
    
    # Test 4: Large dataset with summary
    print("4. Dataset Tool (Summary + Storage):")
    large_data = "x" * 5000  # Large content
    summary = "Dataset analyzed: 1000 rows, average score 85.3"
    
    dataset = ToolResponse(
        content=summary,  # Show summary
        include_in_context=True,
        store_as_document=True,
        content_format="dataset",
        metadata={'full_size': len(large_data), 'summary': True}
    )
    
    # Simulate storing the actual large data
    dataset.content = large_data  # Temporarily set full content for storage
    processed = handle_tool_response("dataset", dataset)
    dataset.content = summary  # Reset to summary
    
    print(f"   ✓ Summary: {summary}")
    print(f"   ✓ Document: {processed['document_id']}")
    print(f"   ✓ Context: {'Yes' if processed['context_content'] else 'No'}")
    print()
    
    print("✅ Integration test completed successfully!")
    print("📝 Documents stored in: /tmp/gaia_docs/")


def test_chat_manager_integration():
    """Test mock integration with chat manager pattern."""
    print("\n🔗 Testing Chat Manager Integration Pattern")
    
    # Simulate what chat manager does
    def simulate_tool_execution(tool_name: str, tool_result):
        """Simulate chat manager tool handling."""
        processed = handle_tool_response(tool_name, tool_result)
        
        # Simulate context addition
        context_added = False
        if processed['context_content']:
            print(f"   📝 Added to context: {len(processed['context_content'])} chars")
            context_added = True
        
        # Simulate synthesis decision
        needs_synthesis = processed['needs_synthesis']
        if needs_synthesis:
            print(f"   🧠 Synthesis required for {tool_name}")
        
        # Simulate user display
        if processed['content']:
            print(f"   👁️ Show user: {processed['content'][:50]}...")
        else:
            print(f"   👁️ Show user: [Document {processed['document_id']}]")
        
        return processed
    
    # Test different tool types
    tools_to_test = [
        ("weather", "Paris: 22°C, sunny"),  # Legacy string
        ("research", ToolResponse(
            content="Research complete: 5 sources found",
            include_in_context=True,
            needs_synthesis=False
        )),
        ("chart", ToolResponse(
            content="<svg>chart data</svg>",
            include_in_context=False,
            needs_synthesis=True,
            show_to_user=False,
            store_as_document=True,
            content_format="svg"
        ))
    ]
    
    for tool_name, tool_result in tools_to_test:
        print(f"\n🔧 Processing {tool_name}:")
        simulate_tool_execution(tool_name, tool_result)
    
    print("\n✅ Chat manager integration pattern verified!")


if __name__ == "__main__":
    test_integration()
    test_chat_manager_integration()