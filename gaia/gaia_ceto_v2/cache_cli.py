#!/usr/bin/env python3
"""
Cache Control CLI - Simple command line interface for cache management

Usage:
    python cache_cli.py list                    # List all cache entries
    python cache_cli.py clear                   # Clear all cache entries
    python cache_cli.py stats                   # Show cache statistics
    python cache_cli.py get <key>               # Get specific cache entry
    python cache_cli.py delete <key>            # Delete specific cache entry
    python cache_cli.py expired                 # List expired entries
"""

import sys
import json
import argparse
from pathlib import Path
from datetime import datetime
from gaia_ceto_v2.core.cache import Cache
from gaia_ceto_v2.settings import GAIA_SETTINGS

DEFAULT_CACHE_DIR = GAIA_SETTINGS.GAIA_CACHE_DIR

def format_timestamp(ts):
    """Format timestamp for human reading."""
    return datetime.fromtimestamp(ts).strftime('%Y-%m-%d %H:%M:%S')

def format_size(size_bytes):
    """Format file size for human reading."""
    if size_bytes < 1024:
        return f"{size_bytes}B"
    elif size_bytes < 1024**2:
        return f"{size_bytes/1024:.1f}KB"
    else:
        return f"{size_bytes/(1024**2):.1f}MB"

def list_cache_entries(cache_dir):
    """List all cache entries with details."""
    cache = Cache(cache_dir)
    
    # Collect all cache files from all subfolders
    all_cache_files = []
    for folder_name, folder_path in cache.subfolders.items():
        for cache_file in folder_path.glob("*.json"):
            all_cache_files.append((cache_file, folder_name))
    
    if not all_cache_files:
        print("Cache is empty")
        return
    
    print(f"Cache directory: {cache.base_cache_dir}")
    print(f"Found {len(all_cache_files)} cache entries:\n")
    print(f"{'Key':<20} {'Folder':<12} {'Type':<10} {'Created':<19} {'TTL':<8} {'Size':<8} {'Status'}")
    print("-" * 110)
    
    for cache_file, folder_name in sorted(all_cache_files):
        try:
            with open(cache_file, 'r') as f:
                entry_dict = json.load(f)
            
            key = entry_dict.get('cache_key', cache_file.stem)
            entry_type = entry_dict.get('entry_type', 'unknown')
            created = entry_dict.get('created_at', 0)
            ttl = entry_dict.get('ttl_seconds', 0)
            expires = created + ttl
            size = cache_file.stat().st_size
            
            # Check if expired
            now = datetime.now().timestamp()
            status = "EXPIRED" if now > expires else "VALID"
            
            print(f"{key:<20} {folder_name:<12} {entry_type:<10} {format_timestamp(created):<19} {ttl:<8} {format_size(size):<8} {status}")
            
        except Exception as e:
            print(f"{cache_file.name:<16} ERROR: {e}")

def show_cache_stats(cache_dir):
    """Show cache statistics."""
    cache = Cache(cache_dir)
    # Collect all cache files from all subfolders
    cache_files = []
    for folder_path in cache.subfolders.values():
        cache_files.extend(folder_path.glob("*.json"))
    
    if not cache_files:
        print("Cache is empty")
        return
    
    total_size = sum(f.stat().st_size for f in cache_files)
    now = datetime.now().timestamp()
    
    valid_count = 0
    expired_count = 0
    types = {}
    
    for cache_file in cache_files:
        try:
            with open(cache_file, 'r') as f:
                entry_dict = json.load(f)
            
            created = entry_dict.get('created_at', 0)
            ttl = entry_dict.get('ttl_seconds', 0)
            entry_type = entry_dict.get('entry_type', 'unknown')
            
            if now > (created + ttl):
                expired_count += 1
            else:
                valid_count += 1
            
            types[entry_type] = types.get(entry_type, 0) + 1
            
        except Exception:
            expired_count += 1
    
    print(f"Cache Statistics:")
    print(f"  Directory: {cache.base_cache_dir}")
    print(f"  Total entries: {len(cache_files)}")
    print(f"  Valid entries: {valid_count}")
    print(f"  Expired entries: {expired_count}")
    print(f"  Total size: {format_size(total_size)}")
    print(f"  Entry types: {dict(types)}")

def get_cache_entry(cache_dir, key):
    """Get and display specific cache entry."""
    cache = Cache(cache_dir)
    
    # Try to get the entry
    value = cache.get(key)
    if value is None:
        print(f"Cache key '{key}' not found or expired")
        return
    
    # Also show metadata
    cache_file = cache._get_cache_file(key)
    if cache_file.exists():
        with open(cache_file, 'r') as f:
            entry_dict = json.load(f)
        
        print(f"Cache Key: {key}")
        print(f"Entry Type: {entry_dict.get('entry_type', 'unknown')}")
        print(f"Created: {format_timestamp(entry_dict.get('created_at', 0))}")
        print(f"TTL: {entry_dict.get('ttl_seconds', 0)} seconds")
        print(f"Expires In: {entry_dict.get('expires_in', 0):.1f} seconds")
        print(f"Size: {format_size(cache_file.stat().st_size)}")
        print("\nData:")
        if isinstance(value, dict):
            print(json.dumps(value, indent=2))
        else:
            print(str(value))

def delete_cache_entry(cache_dir, key):
    """Delete specific cache entry."""
    cache = Cache(cache_dir)
    cache_file = cache._get_cache_file(key)
    
    if cache_file.exists():
        cache_file.unlink()
        print(f"Deleted cache entry: {key}")
    else:
        print(f"Cache key '{key}' not found")

def clear_cache(cache_dir, pattern=None, category=None):
    """Clear cache entries with optional filtering."""
    import fnmatch
    
    cache = Cache(cache_dir)
    
    if not pattern and not category:
        # Clear all (original behavior)
        count = cache.clear()
        print(f"Cleared {count} cache entries")
        return
    
    # Handle subfolder-based clearing
    if category:
        subfolder_map = {
            'research': 'exa_research',
            'search': 'exa_search', 
            'general': 'general',
            'exa': None,  # Special case - clear both exa subfolders
            'all': None   # Clear everything
        }
        
        if category == 'exa':
            # Clear both exa subfolders
            count = cache.clear('exa_research') + cache.clear('exa_search')
            print(f"Cleared {count} exa cache entries")
            return
        elif category == 'all':
            count = cache.clear()
            print(f"Cleared {count} cache entries from all folders")
            return
        elif category in subfolder_map:
            subfolder = subfolder_map[category]
            count = cache.clear(subfolder)
            print(f"Cleared {count} cache entries from {category} folder")
            return
    
    # Filter files based on criteria
    files_to_delete = []
    
    for cache_file in cache_files:
        should_delete = False
        
        # Check pattern matching
        if pattern:
            cache_key = cache_file.stem
            if fnmatch.fnmatch(cache_key, pattern):
                should_delete = True
        
        # Check category matching
        if category:
            try:
                with open(cache_file, 'r') as f:
                    entry_dict = json.load(f)
                
                entry_type = entry_dict.get('entry_type', 'unknown')
                cache_key = entry_dict.get('cache_key', cache_file.stem)
                
                # Map categories to patterns/types
                if category == 'research' and (cache_key.startswith('exa_research_') or entry_type == 'exa_research'):
                    should_delete = True
                elif category == 'search' and (cache_key.startswith('exa_search_') or entry_type == 'exa_search'):
                    should_delete = True
                elif category == 'exa' and (cache_key.startswith('exa_') or entry_type.startswith('exa_')):
                    should_delete = True
                elif category == 'all':
                    should_delete = True
                    
            except Exception:
                # If can't read entry, skip it
                continue
        
        if should_delete:
            files_to_delete.append(cache_file)
    
    # Delete filtered files
    count = 0
    for cache_file in files_to_delete:
        try:
            cache_file.unlink()
            count += 1
        except Exception as e:
            print(f"Failed to delete {cache_file.name}: {e}")
    
    if pattern:
        print(f"Cleared {count} cache entries matching pattern '{pattern}'")
    elif category:
        print(f"Cleared {count} cache entries in category '{category}'")

def list_expired_entries(cache_dir):
    """List only expired cache entries."""
    cache = Cache(cache_dir)
    # Collect all cache files from all subfolders
    cache_files = []
    for folder_path in cache.subfolders.values():
        cache_files.extend(folder_path.glob("*.json"))
    now = datetime.now().timestamp()
    
    expired_files = []
    for cache_file in cache_files:
        try:
            with open(cache_file, 'r') as f:
                entry_dict = json.load(f)
            
            created = entry_dict.get('created_at', 0)
            ttl = entry_dict.get('ttl_seconds', 0)
            
            if now > (created + ttl):
                expired_files.append((cache_file, entry_dict))
        except Exception:
            expired_files.append((cache_file, {}))
    
    if not expired_files:
        print("No expired cache entries found")
        return
    
    print(f"Found {len(expired_files)} expired entries:\n")
    print(f"{'Key':<16} {'Type':<10} {'Expired Since':<15} {'Size':<8}")
    print("-" * 60)
    
    for cache_file, entry_dict in expired_files:
        key = entry_dict.get('cache_key', cache_file.stem)
        entry_type = entry_dict.get('entry_type', 'unknown')
        created = entry_dict.get('created_at', 0)
        ttl = entry_dict.get('ttl_seconds', 0)
        expired_since = now - (created + ttl)
        size = cache_file.stat().st_size
        
        print(f"{key:<16} {entry_type:<10} {expired_since/3600:.1f}h ago       {format_size(size):<8}")

def main():
    parser = argparse.ArgumentParser(description='Cache Control CLI')
    parser.add_argument('--cache-dir', default=DEFAULT_CACHE_DIR, help='Cache directory path')
    
    subparsers = parser.add_subparsers(dest='command', help='Available commands')
    
    subparsers.add_parser('list', help='List all cache entries')
    subparsers.add_parser('stats', help='Show cache statistics')
    subparsers.add_parser('expired', help='List expired entries')
    
    clear_parser = subparsers.add_parser('clear', help='Clear cache entries')
    clear_parser.add_argument('--pattern', help='Clear entries matching pattern (e.g., "exa_research_*")')
    clear_parser.add_argument('--category', choices=['research', 'search', 'exa', 'all'], 
                             help='Clear entries by category')
    
    get_parser = subparsers.add_parser('get', help='Get specific cache entry')
    get_parser.add_argument('key', help='Cache key to retrieve')
    
    delete_parser = subparsers.add_parser('delete', help='Delete specific cache entry')
    delete_parser.add_argument('key', help='Cache key to delete')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    cache_dir = Path(args.cache_dir)
    
    if args.command == 'list':
        list_cache_entries(cache_dir)
    elif args.command == 'stats':
        show_cache_stats(cache_dir)
    elif args.command == 'clear':
        clear_cache(cache_dir, pattern=getattr(args, 'pattern', None), category=getattr(args, 'category', None))
    elif args.command == 'get':
        get_cache_entry(cache_dir, args.key)
    elif args.command == 'delete':
        delete_cache_entry(cache_dir, args.key)
    elif args.command == 'expired':
        list_expired_entries(cache_dir)
    else:
        parser.print_help()

if __name__ == '__main__':
    main()