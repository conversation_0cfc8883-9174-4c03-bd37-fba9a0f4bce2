#!/usr/bin/env python3
"""Simple log management CLI that uses the logging system's actual directories."""

import argparse
import sys
from pathlib import Path
from gaia_ceto_v2.settings import GAIA_SETTINGS

def get_log_directories():
    """Get the actual log directories used by the logging system."""
    # These are the directories the logging system actually uses
    return [
        GAIA_SETTINGS.GAIA_LOGS_DIR,  # Main logging directory from logging_factory.py
        GAIA_SETTINGS.GAIA_DOCS_DIR,  # Research reports and temp docs
        "./test_logs",                # Local test logs
        "./tests/test_logs",          # Test suite logs
    ]

def is_truth_system_file(file_path):
    """Check if a file is part of the truth system and should NEVER be cleared.
    
    The truth system REGISTERS, RECORDS, and ARCHIVES truth violations permanently.
    These are critical forensic records, not temporary logs.
    """
    path_str = str(file_path).lower()
    # Protect any files with 'truth' in the path
    if 'truth' in path_str:
        return True
    # Protect specific truth system directories and files
    protected_patterns = [
        'truth_data',
        'violations.json',
        'interference_incidents.json', 
        'interference_patterns.json',
        'prompt_versions.json',
        'active_config.json'
    ]
    return any(pattern in path_str for pattern in protected_patterns)

def clear_logs():
    """Clear all log files by truncating to empty. NEVER clears truth system files."""
    log_dirs = get_log_directories()
    
    count = 0
    protected_count = 0
    
    for log_dir in log_dirs:
        path = Path(log_dir)
        if path.exists():
            print(f"Scanning {path}...")
            # Clear .log files (truncate to empty to preserve file handles)
            for log_file in path.rglob("*.log"):
                if is_truth_system_file(log_file):
                    print(f"  🛡️  PROTECTED (truth system ARCHIVES): {log_file}")
                    protected_count += 1
                    continue
                log_file.write_text("")
                print(f"  Cleared: {log_file}")
                count += 1
            # Remove .json research reports and other temp files
            for json_file in path.rglob("research_report_*.json"):
                if is_truth_system_file(json_file):
                    print(f"  🛡️  PROTECTED (truth system RECORDS): {json_file}")
                    protected_count += 1
                    continue
                json_file.unlink()
                print(f"  Removed: {json_file}")
                count += 1
            # Remove any other temp files
            for temp_file in path.rglob("temp_*"):
                if temp_file.is_file():
                    if is_truth_system_file(temp_file):
                        print(f"  🛡️  PROTECTED (truth system ARCHIVES): {temp_file}")
                        protected_count += 1
                        continue
                    temp_file.unlink()
                    print(f"  Removed: {temp_file}")
                    count += 1
    
    print(f"Cleared {count} files total")
    if protected_count > 0:
        print(f"🛡️  Protected {protected_count} truth system ARCHIVES (NEVER cleared - permanent forensic records)")
    return count

def main():
    parser = argparse.ArgumentParser(description='Log Management CLI')
    parser.add_argument('command', choices=['clear'], help='Command to execute')
    
    args = parser.parse_args()
    
    if args.command == 'clear':
        clear_logs()
    else:
        parser.print_help()
        sys.exit(1)

if __name__ == '__main__':
    main()