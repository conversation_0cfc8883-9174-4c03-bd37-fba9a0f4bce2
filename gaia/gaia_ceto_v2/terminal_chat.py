#!/usr/bin/env python3
"""
Terminal Chat - Interactive chat interface for gaia_ceto_v2

Supports multiple LLM providers with clean terminal interface.
Demonstrates Level 0010 requirements: MockLLM + fast LLM (Gemini 2.5 flash)
"""

import sys
import os

# Add parent directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import argparse
import logging
from pathlib import Path

try:
    import readline
    import atexit
    READLINE_AVAILABLE = True
except ImportError:
    READLINE_AVAILABLE = False

from gaia_ceto_v2.core import create_chat_manager, create_llm_provider
from gaia_ceto_v2.settings import GAIA_SETTINGS


def setup_readline():
    """Setup readline for bash-like editing capabilities."""
    if not READLINE_AVAILABLE:
        return
    
    # Set up history file
    history_file = os.path.expanduser('~/.gaia_ceto_history')
    
    try:
        # Load existing history
        readline.read_history_file(history_file)
    except FileNotFoundError:
        pass  # No history file yet
    except Exception:
        pass  # Ignore other errors
    
    # Configure readline behavior
    readline.set_history_length(1000)
    
    # Enable tab completion (basic)
    readline.parse_and_bind('tab: complete')
    
    # Bash-like keybindings
    readline.parse_and_bind('"\\e[A": history-search-backward')  # Up arrow
    readline.parse_and_bind('"\\e[B": history-search-forward')   # Down arrow
    readline.parse_and_bind('"\\e[C": forward-char')             # Right arrow
    readline.parse_and_bind('"\\e[D": backward-char')            # Left arrow
    readline.parse_and_bind('"\\e[1;5C": forward-word')          # Ctrl+Right
    readline.parse_and_bind('"\\e[1;5D": backward-word')         # Ctrl+Left
    readline.parse_and_bind('"\\C-a": beginning-of-line')        # Ctrl+A
    readline.parse_and_bind('"\\C-e": end-of-line')              # Ctrl+E
    readline.parse_and_bind('"\\C-k": kill-line')                # Ctrl+K
    readline.parse_and_bind('"\\C-u": unix-line-discard')        # Ctrl+U
    readline.parse_and_bind('"\\C-w": unix-word-rubout')         # Ctrl+W
    
    # Save history on exit
    def save_history():
        try:
            readline.write_history_file(history_file)
        except Exception:
            pass  # Ignore errors
    
    atexit.register(save_history)


def setup_command_completion(chat_manager=None):
    """Setup tab completion for commands and tools."""
    if not READLINE_AVAILABLE:
        return
    
    # Basic commands
    commands = ['exit', 'quit', 'help', 'stats', 'clear', 'prompt', 'tools']
    
    # Add available tools if chat_manager is provided
    if chat_manager:
        try:
            available_tools = chat_manager.get_available_tools()
            if available_tools:
                commands.extend(available_tools)
        except Exception:
            pass  # Ignore errors getting tools
    
    def completer(text, state):
        """Tab completion function."""
        matches = [cmd for cmd in commands if cmd.startswith(text)]
        try:
            return matches[state]
        except IndexError:
            return None
    
    readline.set_completer(completer)


def get_user_input(prompt_text: str = "\n💬 You: ") -> str:
    """Get user input with readline support and graceful fallback."""
    if READLINE_AVAILABLE:
        try:
            return input(prompt_text).strip()
        except (KeyboardInterrupt, EOFError):
            raise  # Re-raise these for proper handling
        except Exception:
            # Fallback to basic input on any readline errors
            return input(prompt_text).strip()
    else:
        return input(prompt_text).strip()


def setup_logging(verbose: bool = False):
    """Setup logging for terminal chat."""
    level = logging.DEBUG if verbose else logging.WARNING
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )


def get_provider_info(provider_name: str) -> tuple:
    """Get provider display info and warnings."""
    if provider_name == 'mock':
        return "🤖 MockLLM", "⚠️  USING MOCK LLM - Responses are simulated, not real AI"
    elif provider_name == 'gemini':
        has_key = bool(os.getenv('GEMINI_API_KEY'))
        if has_key:
            return "✨ Gemini 2.5 Flash Lite", "🔑 Using real Gemini API"
        else:
            return "✨ Gemini 2.5 Flash Lite", "⚠️  No GEMINI_API_KEY - will use fallback responses"
    elif provider_name == 'openai':
        has_key = bool(os.getenv('OPENAI_API_KEY'))
        return "🧠 OpenAI GPT", "🔑 API Key required" if not has_key else "🔑 Using OpenAI API"
    elif provider_name == 'anthropic':
        has_key = bool(os.getenv('ANTHROPIC_API_KEY'))
        return "🎭 Claude", "🔑 API Key required" if not has_key else "🔑 Using Anthropic API"
    else:
        return f"🔧 {provider_name}", "Custom provider"


def print_banner(provider_name: str, system_prompt: str = None, chat_manager=None, cache_dir=None):
    """Print chat session banner."""
    provider_display, provider_status = get_provider_info(provider_name)
    
    print("=" * 60)
    print("🚀 GAIA CETO v2 - Terminal Chat")
    print("=" * 60)
    print(f"Provider: {provider_display}")
    print(f"Status:   {provider_status}")
    if system_prompt:
        print(f"System:   🎭 Using '{system_prompt}' prompt")
    
    # Show caching information
    if cache_dir:
        print(f"Cache:    💾 TTL caching enabled ({cache_dir})")
    else:
        print("Cache:    ❌ TTL caching disabled")
    
    # Show tool information
    if chat_manager:
        available_tools = chat_manager.get_available_tools()
        if available_tools:
            print(f"Tools:    🔧 {len(available_tools)} tools available: {', '.join(available_tools)}")
        else:
            print("Tools:    ❌ No tools available")
    
    # Show readline status
    if READLINE_AVAILABLE:
        print("Input:    ⌨️  Bash-like editing enabled (Ctrl+A/E, arrows, history)")
    else:
        print("Input:    ⚠️  Basic input (install readline for bash-like editing)")
    
    print()
    print("Commands:")
    print("  exit, quit    - Exit chat")
    print("  help          - Show this help")
    print("  stats         - Show conversation statistics")
    print("  clear         - Clear conversation history")
    print("  prompt        - Show full system prompt being used")
    if chat_manager and chat_manager.get_available_tools():
        print("  tools         - List available tools")
        print("  call <tool> <args> - Execute a tool")
    print()
    print("💡 Level 0010 Testing:")
    print("  - MockLLM: Fast testing with simulated responses")
    print("  - Gemini:  Real AI responses (requires API key)")
    print("  - System Prompts: --system-prompt <key> or --list-prompts")
    if cache_dir:
        print("💡 Level 0008b Cache:")
        print("  - TTL-based caching for LLM calls (1 hour) and tools (5 min)")
        print("  - Responses cached by input hash with automatic expiration")
    if chat_manager and chat_manager.get_available_tools():
        print("💡 Level 0030 Tools:")
        print("  - Use 'call toolname args' to execute tools")
        print("  - Tools are executed before LLM processing")
    if READLINE_AVAILABLE:
        print("💡 Bash-like Editing:")
        print("  - Up/Down: History navigation")
        print("  - Ctrl+Left/Right: Word navigation")
        print("  - Ctrl+A/E: Beginning/End of line")
        print("  - Ctrl+K/U/W: Kill line/line-discard/word-rubout")
    print()
    print("Ready to chat! Type your message and press Enter.")
    print("-" * 60)


def print_help():
    """Print help information."""
    print("\n📖 Terminal Chat Help:")
    print("  exit, quit       - Exit the chat session") 
    print("  help             - Show this help message")
    print("  stats            - Display conversation statistics")
    print("  clear            - Clear conversation history")
    print("  prompt           - Show the full system prompt being used")
    print("  echostring <msg> - Test tool integration (if available)")
    print("  get_time         - Test time tool (if available)")
    
    if READLINE_AVAILABLE:
        print("\n⌨️  Bash-like Editing Keys:")
        print("  Up/Down arrows   - Navigate command history")
        print("  Left/Right arrows - Move cursor")
        print("  Ctrl+Left/Right  - Move by word")
        print("  Ctrl+A           - Beginning of line")
        print("  Ctrl+E           - End of line")
        print("  Ctrl+K           - Kill from cursor to end")
        print("  Ctrl+U           - Kill entire line")
        print("  Ctrl+W           - Kill word before cursor")
    print()


def main():
    """Main terminal chat function."""
    parser = argparse.ArgumentParser(
        description='Terminal Chat for gaia_ceto_v2',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python terminal_chat.py                    # Use MockLLM (fast testing)
  python terminal_chat.py --provider gemini  # Use Gemini 2.5 flash
  python terminal_chat.py --provider openai  # Use OpenAI GPT
  
Environment Variables:
  GEMINI_API_KEY     - For Gemini provider
  OPENAI_API_KEY     - For OpenAI provider  
  ANTHROPIC_API_KEY  - For Claude provider
        """
    )
    
    parser.add_argument('--provider', default='mock',
                       choices=['mock', 'gemini', 'openai', 'anthropic'],
                       help='LLM provider to use (default: mock)')
    parser.add_argument('--system-prompt', default=None,
                       help='System prompt key or custom text (default: none)')
    parser.add_argument('--list-prompts', action='store_true',
                       help='List available system prompts and exit')
    parser.add_argument('--verbose', '-v', action='store_true',
                       help='Enable verbose logging')
    parser.add_argument('--storage-dir', default=GAIA_SETTINGS.GAIA_CONVERSATIONS_DIR,
                       help='Directory for conversation storage')
    parser.add_argument('--with-mcp', action='store_true',
                       help='Enable MCP tool integration')
    parser.add_argument('--mcp-server', default=None,
                       help='MCP server URL (default: http://localhost:9000/mcp/)')
    parser.add_argument('--cache-dir', default=GAIA_SETTINGS.GAIA_CACHE_DIR,
                       help='Cache directory for TTL-based caching (default: /tmp/gaia_cache)')
    parser.add_argument('--no-cache', action='store_true',
                       help='Disable TTL-based caching')
    parser.add_argument('--accounting-dir', default=GAIA_SETTINGS.GAIA_ACCOUNTING_DIR,
                       help='Directory for accounting data (default: /tmp/gaia_accounting)')
    parser.add_argument('--no-accounting', action='store_true',
                       help='Disable accounting system')
    
    args = parser.parse_args()
    
    # Setup logging
    setup_logging(args.verbose)
    
    # Setup readline for bash-like editing
    setup_readline()
    
    try:
        # Create chat manager with integrated provider selection
        print("Setting up chat manager...")
        cache_dir = None if args.no_cache else args.cache_dir
        
        # Level 0008a: Initialize accounting system if enabled
        accounting_system = None
        if not args.no_accounting:
            try:
                from gaia_accounting import AccountingSystem
                accounting_system = AccountingSystem(data_dir=args.accounting_dir)
            except ImportError:
                print("⚠️  gaia_accounting not available - accounting disabled")
            except Exception as e:
                print(f"⚠️  Failed to initialize accounting system: {e}")
        
        # Use Gemini tool calling if provider is gemini and MCP is enabled
        use_gemini_tools = (args.provider == 'gemini' and args.with_mcp)
        
        if use_gemini_tools:
            print("🚀 Using Gemini with native tool calling integration")
            chat_manager = create_chat_manager(
                storage_dir=args.storage_dir,
                use_gemini_tools=True,
                with_mcp=args.with_mcp,
                mcp_server_url=args.mcp_server,
                cache_dir=cache_dir,
                accounting_system=accounting_system
            )
        else:
            # Create LLM provider the traditional way
            print("Initializing LLM provider...")
            llm_provider = create_llm_provider(args.provider, accounting_system=accounting_system)
            
            chat_manager = create_chat_manager(
                storage_dir=args.storage_dir,
                llm_provider=llm_provider,
                with_mcp=args.with_mcp,
                mcp_server_url=args.mcp_server,
                cache_dir=cache_dir,
                accounting_system=accounting_system
            )
        
        # Handle --list-prompts
        if args.list_prompts:
            prompts = chat_manager.get_available_system_prompts()
            print("\n📋 Available System Prompts:")
            for key in sorted(prompts.keys()):
                print(f"  {key}")
            return 0
        
        # Create conversation with system prompt
        user_id = "terminal_user"
        conversation_id = chat_manager.create_conversation(
            user_id=user_id, 
            title=f"Terminal Chat ({args.provider})",
            system_prompt=args.system_prompt
        )
        
        # Print banner
        print_banner(args.provider, args.system_prompt, chat_manager, cache_dir)
        
        # Setup command completion with available tools
        setup_command_completion(chat_manager)
        
        # Chat loop
        while True:
            try:
                # Get user input with readline support
                user_message = get_user_input()
                
                if not user_message:
                    continue
                
                # Handle commands
                if user_message.lower() in ['exit', 'quit']:
                    print("\n👋 Goodbye!")
                    break
                elif user_message.lower() == 'help':
                    print_help()
                    continue
                elif user_message.lower() == 'stats':
                    stats = chat_manager.get_conversation_stats(conversation_id)
                    if stats:
                        print("\n📊 Conversation Statistics:")
                        print(f"  Messages: {stats.get('total_messages', 0)}")
                        print(f"  Characters: {stats.get('total_characters', 0)}")
                        print(f"  Duration: {stats.get('conversation_duration_seconds', 0):.1f}s")
                    continue
                elif user_message.lower() == 'clear':
                    success = chat_manager.clear_conversation_history(conversation_id)
                    if success:
                        print("✅ Conversation history cleared")
                    else:
                        print("❌ Failed to clear conversation history")
                    continue
                elif user_message.lower() == 'prompt':
                    system_prompt = chat_manager.get_conversation_system_prompt(conversation_id)
                    if system_prompt:
                        print("\n🎭 Full System Prompt:")
                        print("=" * 60)
                        print(system_prompt)
                        print("=" * 60)
                    else:
                        print("❌ No system prompt found for this conversation")
                    continue
                elif user_message.lower() == 'tools':
                    available_tools = chat_manager.get_available_tools()
                    if available_tools:
                        print(f"\n🔧 Available Tools ({len(available_tools)}):")
                        for tool in available_tools:
                            print(f"  • {tool}")
                        print("\nUsage: call <tool_name> <arguments>")
                    else:
                        print("❌ No tools available")
                    continue
                
                # Send message to LLM
                print("🤔 Thinking...")
                assistant_response = chat_manager.send_message(conversation_id, user_message)
                
                # Print response with nice formatting
                print(f"\n🤖 Assistant: {assistant_response}")
                
            except (KeyboardInterrupt, EOFError):
                print("\n\n👋 Goodbye!")
                break
            except Exception as e:
                print(f"\n❌ Error: {e}")
                if args.verbose:
                    import traceback
                    traceback.print_exc()
                print("💡 Try 'help' for available commands")
                
    except Exception as e:
        print(f"❌ Failed to initialize chat: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        return 1
    
    return 0


if __name__ == '__main__':
    sys.exit(main())