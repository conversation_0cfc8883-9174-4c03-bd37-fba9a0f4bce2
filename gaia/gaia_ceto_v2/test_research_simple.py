#!/usr/bin/env python3
"""Simple test for research functionality"""

import subprocess
import sys

# Run a simple query
process = subprocess.Popen(
    [sys.executable, 'terminal_chat.py', '--provider', 'gemini', '--with-mcp'],
    stdin=subprocess.PIPE,
    stdout=subprocess.PIPE,
    stderr=subprocess.PIPE,
    text=True
)

# Send query and wait
stdout, stderr = process.communicate("research rocket ships\nexit\n", timeout=20)

# Print output
print("=== STDOUT ===")
print(stdout[-2000:] if len(stdout) > 2000 else stdout)  # Last 2000 chars
print("\n=== STDERR ===")
print(stderr[-1000:] if len(stderr) > 1000 else stderr)  # Last 1000 chars