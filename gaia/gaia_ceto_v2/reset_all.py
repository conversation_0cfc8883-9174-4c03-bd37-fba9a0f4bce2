#!/usr/bin/env python3
"""
Unified reset script using centralized settings.
Replaces multiple CLI calls with single Python process.
"""

import shutil
import glob
import os
import sys
from pathlib import Path

# Import centralized settings
from gaia_ceto_v2.settings import GAIA_SETTINGS


def reset_all(verbose=False):
    """Reset all GAIA backend data directories."""
    
    # Get all configured paths
    paths = GAIA_SETTINGS.get_all_paths()
    
    if verbose:
        print("🗑️  Resetting backend with configuration:")
        GAIA_SETTINGS.print_config()
        print()
    
    total_count = 0
    
    # Clear cache directory
    cache_dir = Path(GAIA_SETTINGS.GAIA_CACHE_DIR)
    if cache_dir.exists():
        count = 0
        for item in cache_dir.glob("**/*.json"):
            item.unlink()
            count += 1
        total_count += count
        if verbose:
            print(f"✓ Cleared {count} cache files from {cache_dir}")
    
    # Clear logs - preserve directory structure but empty files
    logs_dir = Path(GAIA_SETTINGS.GAIA_LOGS_DIR)
    if logs_dir.exists():
        count = 0
        for log_file in logs_dir.glob("*.log"):
            # Empty the file instead of deleting
            open(log_file, 'w').close()
            count += 1
        total_count += count
        if verbose:
            print(f"✓ Cleared {count} log files from {logs_dir}")
    
    # Clear documents
    docs_dir = Path(GAIA_SETTINGS.GAIA_DOCS_DIR)
    if docs_dir.exists():
        count = 0
        for doc in docs_dir.glob("*.json"):
            doc.unlink()
            count += 1
        total_count += count
        if verbose:
            print(f"✓ Cleared {count} documents from {docs_dir}")
    
    # Clear conversations
    conv_dir = Path(GAIA_SETTINGS.GAIA_CONVERSATIONS_DIR)
    if conv_dir.exists():
        # Remove entire conversations subdirectory tree
        conv_subdir = conv_dir / "conversations"
        if conv_subdir.exists():
            shutil.rmtree(conv_subdir)
            conv_subdir.mkdir()
            if verbose:
                print(f"✓ Cleared conversations from {conv_dir}")
    
    # Clear accounting
    acc_dir = Path(GAIA_SETTINGS.GAIA_ACCOUNTING_DIR)
    if acc_dir.exists():
        acc_file = acc_dir / "calls.jsonl"
        if acc_file.exists():
            # Empty the file
            open(acc_file, 'w').close()
            if verbose:
                print(f"✓ Cleared accounting records from {acc_file}")
    
    # Ensure all directories exist (in case any were missing)
    GAIA_SETTINGS.ensure_directories()
    
    print(f"✅ Backend reset complete! Cleared {total_count} files total.")
    return total_count


def main():
    """CLI entry point."""
    verbose = "--verbose" in sys.argv or "-v" in sys.argv
    
    # Check if running from correct directory
    if not Path("gaia_ceto_v2").exists():
        print("⚠️  Please run this script from the /usr/local/agfunder/agbase_admin/gaia directory")
        sys.exit(1)
    
    try:
        reset_all(verbose=verbose)
    except Exception as e:
        print(f"❌ Error during reset: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()