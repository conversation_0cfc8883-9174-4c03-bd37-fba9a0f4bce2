#!/usr/bin/env python3
"""
Test script for Gemini native tool calling integration.

This script tests the complete integration of Gemini with MCP tools
using the new tool-capable LLM interface.
"""

import os
import sys
import logging
from typing import Dict, Any

# Add the core module to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'core'))

from core.chat_manager import create_chat_manager, create_gemini_tool_llm
from core.gemini_tool_provider import GeminiToolLLM
from core.tool_calling_interface import ToolCapableLLM

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_gemini_tool_provider():
    """Test basic Gemini tool provider functionality."""
    print("🧪 Testing Gemini Tool Provider...")
    
    try:
        # Create Gemini tool LLM
        gemini_llm = create_gemini_tool_llm()
        
        if gemini_llm is None:
            print("❌ Failed to create Gemini tool LLM - check GEMINI_API_KEY")
            return False
        
        print(f"✅ Created Gemini LLM: {gemini_llm.get_model_name()}")
        print(f"✅ Supports tools: {gemini_llm.supports_tools()}")
        print(f"✅ Is ToolCapableLLM: {isinstance(gemini_llm, ToolCapableLLM)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing Gemini provider: {e}")
        return False

def test_gemini_with_mcp():
    """Test Gemini integration with MCP tools."""
    print("\n🛠️  Testing Gemini + MCP Integration...")
    
    try:
        # Create chat manager with Gemini tools and MCP
        chat_manager = create_chat_manager(
            storage_dir='/tmp/test_gemini_tools',
            use_gemini_tools=True,
            with_mcp=True,
            mcp_server_url="http://localhost:9000/mcp/",
            cache_dir='/tmp/test_cache'
        )
        
        print(f"✅ Created chat manager with Gemini tools")
        print(f"✅ LLM provider: {type(chat_manager.message_service.llm_provider).__name__}")
        print(f"✅ Tool handler: {type(chat_manager.tool_handler).__name__}")
        
        # Check available tools
        available_tools = chat_manager.get_available_tools()
        print(f"✅ Available tools: {available_tools}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing Gemini + MCP: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_tool_calling_flow():
    """Test the complete tool calling flow."""
    print("\n🔄 Testing Tool Calling Flow...")
    
    try:
        # Create test conversation
        chat_manager = create_chat_manager(
            storage_dir='/tmp/test_gemini_tools',
            use_gemini_tools=True,
            with_mcp=True,
            mcp_server_url="http://localhost:9000/mcp/"
        )
        
        user_id = "test_user"
        conversation_id = chat_manager.create_conversation(
            user_id=user_id, 
            title="Gemini Tool Test"
        )
        
        print(f"✅ Created conversation: {conversation_id}")
        
        # Test message with potential tool use
        test_message = "Can you echo 'Hello from Gemini tools'?"
        
        print(f"📝 Sending message: {test_message}")
        response = chat_manager.send_message(conversation_id, test_message)
        
        print(f"🤖 Response: {response}")
        
        # Get conversation stats
        stats = chat_manager.get_conversation_stats(conversation_id)
        if stats:
            print(f"📊 Stats: {stats}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing tool calling flow: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_environment():
    """Check if environment is properly configured."""
    print("🔧 Checking Environment...")
    
    # Check for Gemini API key
    api_key = os.getenv('GEMINI_API_KEY') or os.getenv('GOOGLE_API_KEY')
    if not api_key:
        print("⚠️  No GEMINI_API_KEY or GOOGLE_API_KEY found")
        print("   Set one to test actual Gemini integration")
        return False
    else:
        print("✅ Found Gemini API key")
    
    # Check for MCP server (optional)
    print("💡 To test MCP integration, ensure echostring server is running:")
    print("   cd gaia_sprites && python -m gaia_sprite_mcp.sprite_mcp_server_base --port 9000")
    
    return True

def main():
    """Main test runner."""
    print("🚀 Gemini Native Tool Calling Integration Test")
    print("=" * 50)
    
    # Check environment
    env_ok = check_environment()
    
    # Run tests
    tests = [
        ("Gemini Tool Provider", test_gemini_tool_provider),
        ("Gemini + MCP Integration", test_gemini_with_mcp),
    ]
    
    if env_ok:
        tests.append(("Tool Calling Flow", test_tool_calling_flow))
    
    results = {}
    for test_name, test_func in tests:
        print(f"\n{'=' * 20} {test_name} {'=' * 20}")
        results[test_name] = test_func()
    
    # Summary
    print(f"\n{'=' * 50}")
    print("📋 Test Summary:")
    
    passed = sum(results.values())
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  {status} {test_name}")
    
    print(f"\n🎯 Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Gemini tool calling is working.")
    else:
        print("⚠️  Some tests failed. Check the output above.")
    
    return passed == total

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)