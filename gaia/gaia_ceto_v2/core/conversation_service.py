"""
ConversationService - Handles conversation CRUD operations.
"""

import logging
from typing import Dict, List, Any, Optional
from datetime import datetime

from .conversation import Conversation
from .storage_interface import ConversationStorage
from .conversation_cache import ConversationCache

logger = logging.getLogger(__name__)


class ConversationService:
    """Focused service for conversation management operations."""
    
    def __init__(self, repository: ConversationStorage, cache: ConversationCache):
        self.repository = repository
        self.cache = cache
    
    def create_conversation(self, user_id: str, title: str = None, **metadata) -> str:
        """Create a new conversation."""
        conversation = Conversation(user_id=user_id, title=title, metadata=metadata)
        
        if not self.repository.save(conversation):
            raise RuntimeError("Failed to save conversation to repository")
        
        self.cache.put(conversation)
        logger.info(f"Created conversation {conversation.conversation_id} for user {user_id}")
        return conversation.conversation_id
    
    def get_conversation(self, conversation_id: str) -> Optional[Conversation]:
        """Get conversation from cache or repository."""
        conversation = self.cache.get(conversation_id)
        if conversation:
            return conversation
        
        conversation = self.repository.load(conversation_id)
        if conversation:
            self.cache.put(conversation)
        
        return conversation
    
    def save_conversation(self, conversation: Conversation) -> bool:
        """Save conversation to repository and update cache."""
        success = self.repository.save(conversation)
        if success:
            self.cache.put(conversation)
        return success
    
    def list_conversations(self, user_id: str = None) -> List[Dict[str, Any]]:
        """List conversations, optionally filtered by user."""
        return self.repository.list_conversations(user_id=user_id)
    
    def delete_conversation(self, conversation_id: str) -> bool:
        """Delete a conversation."""
        self.cache.invalidate(conversation_id)
        return self.repository.delete(conversation_id)
    
    def update_conversation_title(self, conversation_id: str, new_title: str) -> bool:
        """Update the title of a conversation."""
        conversation = self.get_conversation(conversation_id)
        if not conversation:
            return False
        
        conversation.title = new_title
        conversation.updated_at = datetime.now().isoformat()
        return self.save_conversation(conversation)
    
    def clear_conversation_history(self, conversation_id: str, keep_system_messages: bool = True) -> bool:
        """Clear all messages from a conversation."""
        conversation = self.get_conversation(conversation_id)
        if not conversation:
            return False
        
        if keep_system_messages:
            system_messages = [msg for msg in conversation.messages if msg.role == 'system']
            conversation.messages = system_messages
        else:
            conversation.clear_messages()
        
        return self.save_conversation(conversation)