"""
Conversation Cache - Memory management for active conversations.

Extracted from ChatManager to follow Single Responsibility Principle.
Handles caching, eviction, and memory management independently.
"""

import logging
import time
from typing import Dict, Optional
from collections import OrderedDict

from .conversation import Conversation

logger = logging.getLogger(__name__)


class ConversationCache:
    """LRU cache for active conversations with TTL support.
    
    Manages conversation objects in memory to avoid repeated storage I/O.
    Implements LRU eviction and TTL-based expiration.
    """
    
    def __init__(self, max_size: int = 100, ttl_seconds: int = 3600):
        """Initialize conversation cache.
        
        Args:
            max_size: Maximum number of conversations to cache
            ttl_seconds: Time-to-live for cached conversations (default: 1 hour)
        """
        self.max_size = max_size
        self.ttl_seconds = ttl_seconds
        
        # Use OrderedDict for LRU behavior
        self._cache: OrderedDict[str, Dict] = OrderedDict()
        self._access_times: Dict[str, float] = {}
        
        logger.info(f"ConversationCache initialized: max_size={max_size}, ttl={ttl_seconds}s")
    
    def get(self, conv_id: str) -> Optional[Conversation]:
        """Get conversation from cache.
        
        Args:
            conv_id: Conversation ID to retrieve
            
        Returns:
            Cached conversation or None if not found/expired
        """
        current_time = time.time()
        
        # Check if conversation exists and is not expired
        if conv_id not in self._cache:
            return None
        
        access_time = self._access_times.get(conv_id, 0)
        if current_time - access_time > self.ttl_seconds:
            # Expired - remove from cache
            self._remove(conv_id)
            logger.debug(f"Conversation {conv_id} expired from cache")
            return None
        
        # Move to end (most recently used) and update access time
        conversation_data = self._cache[conv_id]
        self._cache.move_to_end(conv_id)
        self._access_times[conv_id] = current_time
        
        logger.debug(f"Cache hit for conversation {conv_id}")
        return conversation_data['conversation']
    
    def put(self, conversation: Conversation) -> None:
        """Add or update conversation in cache.
        
        Args:
            conversation: Conversation object to cache
        """
        conv_id = conversation.conversation_id
        current_time = time.time()
        
        # If already exists, update it
        if conv_id in self._cache:
            self._cache[conv_id] = {
                'conversation': conversation,
                'cached_at': current_time
            }
            self._cache.move_to_end(conv_id)
            self._access_times[conv_id] = current_time
            logger.debug(f"Updated conversation {conv_id} in cache")
            return
        
        # Check if cache is full
        if len(self._cache) >= self.max_size:
            # Remove least recently used item
            lru_conv_id = next(iter(self._cache))
            self._remove(lru_conv_id)
            logger.debug(f"Evicted LRU conversation {lru_conv_id} from cache")
        
        # Add new conversation
        self._cache[conv_id] = {
            'conversation': conversation,
            'cached_at': current_time
        }
        self._access_times[conv_id] = current_time
        
        logger.debug(f"Added conversation {conv_id} to cache")
    
    def invalidate(self, conv_id: str) -> None:
        """Remove specific conversation from cache.
        
        Args:
            conv_id: Conversation ID to remove
        """
        if conv_id in self._cache:
            self._remove(conv_id)
            logger.debug(f"Invalidated conversation {conv_id} from cache")
    
    def clear_expired(self) -> int:
        """Remove all expired conversations from cache.
        
        Returns:
            Number of conversations removed
        """
        current_time = time.time()
        expired_ids = []
        
        for conv_id, access_time in self._access_times.items():
            if current_time - access_time > self.ttl_seconds:
                expired_ids.append(conv_id)
        
        for conv_id in expired_ids:
            self._remove(conv_id)
        
        if expired_ids:
            logger.info(f"Cleared {len(expired_ids)} expired conversations from cache")
        
        return len(expired_ids)
    
    def clear_all(self) -> None:
        """Clear all conversations from cache."""
        count = len(self._cache)
        self._cache.clear()
        self._access_times.clear()
        logger.info(f"Cleared all {count} conversations from cache")
    
    def get_stats(self) -> Dict[str, any]:
        """Get cache statistics.
        
        Returns:
            Dictionary with cache metrics
        """
        current_time = time.time()
        expired_count = sum(
            1 for access_time in self._access_times.values()
            if current_time - access_time > self.ttl_seconds
        )
        
        return {
            'total_cached': len(self._cache),
            'max_size': self.max_size,
            'cache_utilization': len(self._cache) / self.max_size if self.max_size > 0 else 0,
            'expired_count': expired_count,
            'ttl_seconds': self.ttl_seconds
        }
    
    def _remove(self, conv_id: str) -> None:
        """Internal method to remove conversation from all tracking structures."""
        self._cache.pop(conv_id, None)
        self._access_times.pop(conv_id, None)


# Factory function removed - use ConversationCache(max_size, ttl_seconds) directly