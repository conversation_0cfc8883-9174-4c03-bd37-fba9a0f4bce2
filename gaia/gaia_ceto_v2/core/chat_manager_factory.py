"""
ChatManagerFactory - Handles complex ChatManager creation.

Separates the complex factory logic from the ChatManager orchestrator.
This reduces ChatManager from 560 lines to a focused orchestrator.
"""

import logging
from typing import Optional, Tuple
from pathlib import Path

from gaia_ceto_v2.core.chat_manager import Chat<PERSON>anager
from gaia_ceto_v2.core.conversation_service import ConversationService
from gaia_ceto_v2.core.message_service import MessageService
from gaia_ceto_v2.core.system_prompt_service import SystemPromptService
from gaia_ceto_v2.core.chat_statistics import ChatStatistics
from gaia_ceto_v2.core.file_storage import FileConversationRepository
from gaia_ceto_v2.core.conversation_cache import ConversationCache
from gaia_ceto_v2.core.llm_providers import LL<PERSON>rovider, MockLLM
from gaia_ceto_v2.core.cache import Cache
from gaia_ceto_v2.tools.tools import ToolRegistry, default_registry
from gaia_ceto_v2.settings import GAIA_SETTINGS

logger = logging.getLogger(__name__)


class ChatManagerFactory:
    """Factory for creating ChatManager instances with various configurations."""
    
    @staticmethod
    def create(storage_dir: str = None,
               llm_provider: LLMProvider = None,
               cache_size: int = 100,
               cache_ttl: int = 3600,
               stats_retention_days: int = 30,
               with_mcp: bool = False,
               mcp_server_url: str = None,
               cache_dir: str = None,
               use_gemini_tools: bool = False,
               accounting_system=None,
               **kwargs) -> ChatManager:
        """Create a ChatManager with default configuration.
        
        Args:
            storage_dir: Directory for file storage (defaults to GAIA_SETTINGS)
            llm_provider: LLM provider to use (defaults to MockLLM)
            cache_size: Maximum conversations to cache
            cache_ttl: Cache TTL in seconds
            stats_retention_days: Days to retain statistics
            with_mcp: Enable MCP tool integration
            mcp_server_url: MCP server URL
            cache_dir: Directory for TTL cache
            use_gemini_tools: Enable Gemini with native tool calling
            accounting_system: Optional AccountingSystem
            **kwargs: Additional parameters for ChatManager
            
        Returns:
            Configured ChatManager instance
        """
        # Use settings if not provided
        storage_dir = storage_dir or GAIA_SETTINGS.GAIA_CONVERSATIONS_DIR
        
        # Create storage components
        repository, cache, statistics = ChatManagerFactory._create_storage_components(
            storage_dir, cache_size, cache_ttl, stats_retention_days
        )
        
        # Create TTL cache if specified
        simple_cache = ChatManagerFactory._create_ttl_cache(cache_dir)
        
        # Create or configure LLM provider
        if llm_provider is None:
            llm_provider = ChatManagerFactory._create_default_llm_provider(
                use_gemini_tools, with_mcp, mcp_server_url, simple_cache
            )
        else:
            ChatManagerFactory._configure_llm_provider(llm_provider, simple_cache)
        
        # Setup tool registry
        tool_registry = ChatManagerFactory._setup_tool_registry(
            with_mcp, mcp_server_url, simple_cache, accounting_system
        )
        
        return ChatManager(
            repository=repository,
            llm_provider=llm_provider,
            cache=cache,
            statistics=statistics,
            tool_registry=tool_registry,
            **kwargs
        )
    
    @staticmethod
    def create_with_mcp(mcp_server_url: str = "http://localhost:9000/mcp/",
                        **kwargs) -> ChatManager:
        """Convenience method to create ChatManager with MCP integration."""
        return ChatManagerFactory.create(
            with_mcp=True,
            mcp_server_url=mcp_server_url,
            **kwargs
        )
    
    @staticmethod
    def create_with_gemini(model_name: str = "gemini-2.0-flash-exp",
                           **kwargs) -> ChatManager:
        """Convenience method to create ChatManager with Gemini LLM."""
        return ChatManagerFactory.create(
            use_gemini_tools=True,
            **kwargs
        )
    
    @staticmethod
    def _create_storage_components(storage_dir: str, cache_size: int, 
                                   cache_ttl: int, stats_retention_days: int) -> Tuple:
        """Create storage-related components."""
        repository = FileConversationRepository(storage_dir)
        cache = ConversationCache(max_size=cache_size, ttl_seconds=cache_ttl)
        statistics = ChatStatistics(retention_days=stats_retention_days)
        
        return repository, cache, statistics
    
    @staticmethod
    def _create_ttl_cache(cache_dir: Optional[str]):
        """Create TTL cache if directory specified."""
        if not cache_dir:
            return None
            
        try:
            simple_cache = Cache(cache_dir, default_ttl=3600)
            logger.info(f"TTL cache enabled: {cache_dir}")
            return simple_cache
        except Exception as e:
            logger.warning(f"Failed to create TTL cache: {e}")
            return None
    
    @staticmethod
    def _create_default_llm_provider(use_gemini_tools: bool, with_mcp: bool,
                                    mcp_server_url: Optional[str], simple_cache):
        """Create default LLM provider based on configuration."""
        if use_gemini_tools:
            # Try Gemini with native tool calling
            llm_provider = ChatManagerFactory._create_gemini_tool_llm(simple_cache=simple_cache)
            if llm_provider:
                return llm_provider
                
            logger.warning("Failed to create Gemini tool LLM, falling back to MockLLM")
        
        # Create MockLLM with optional MCP tools
        from .llm_providers import MockLLM
        tool_registry = None
        
        if with_mcp:
            tool_registry = ChatManagerFactory._create_mcp_registry_adapter(
                mcp_server_url or "http://localhost:9000/mcp/", 
                simple_cache
            )
        
        return MockLLM(tool_registry=tool_registry)
    
    @staticmethod
    def _configure_llm_provider(llm_provider, simple_cache):
        """Configure existing LLM provider with cache if supported."""
        if hasattr(llm_provider, 'simple_cache') and simple_cache:
            llm_provider.simple_cache = simple_cache
    
    @staticmethod
    def _create_mcp_registry_adapter(server_url: str, simple_cache):
        """Create MCP tool registry adapter."""
        try:
            from .mcp_tool_registry_adapter import MCPToolRegistryAdapter
            logger.info("Created MCP tool registry adapter")
            return MCPToolRegistryAdapter(server_url=server_url, simple_cache=simple_cache)
        except ImportError:
            logger.warning("MCP tool registry adapter not available")
            return None
    
    @staticmethod
    def _setup_tool_registry(with_mcp: bool, mcp_server_url: Optional[str],
                            simple_cache, accounting_system):
        """Setup tool registry and optionally populate with MCP tools."""
        tool_registry = default_registry
        
        if not with_mcp:
            return tool_registry
        
        # Load MCP tools
        url = mcp_server_url or "http://localhost:9000/mcp/"
        try:
            from .mcp_integration import MCPToolHandler
            mcp_handler = MCPToolHandler(
                server_url=url, 
                simple_cache=simple_cache, 
                accounting_system=accounting_system
            )
            
            from .tool_adapter import register_mcp_tools_in_registry
            register_mcp_tools_in_registry(tool_registry, mcp_handler)
            logger.info(f"MCP tools registered from: {url}")
        except Exception as e:
            logger.warning(f"Failed to register MCP tools: {e}")
        
        return tool_registry
    
    @staticmethod
    def _create_gemini_tool_llm(model_name: str = "gemini-2.0-flash-exp", 
                                simple_cache=None, **kwargs):
        """Create a Gemini LLM with native tool calling support."""
        try:
            from .gemini_tool_provider import GeminiToolLLM
            return GeminiToolLLM(model_name=model_name, simple_cache=simple_cache, **kwargs)
        except ImportError:
            logger.warning("Gemini tool provider not available")
            return None


# Convenience function for backward compatibility
def create_chat_manager(**kwargs) -> ChatManager:
    """Create a ChatManager with default configuration.
    
    This function is kept for backward compatibility.
    New code should use ChatManagerFactory.create() directly.
    """
    return ChatManagerFactory.create(**kwargs)