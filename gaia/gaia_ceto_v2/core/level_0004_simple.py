"""
Level 0004: Simple Stopwatch Timer and Latency Log

Minimal timing functions for debug and logging.
Single timing log with request_id support.
"""

import json
import time
import uuid
from datetime import datetime
from pathlib import Path
from typing import Optional, Dict, Any
import logging

from .level_0003_logging import FlushingFileHandler


# Global timing logger - created once
_timing_logger: Optional[logging.Logger] = None


def _get_timing_logger(log_dir: str = '/tmp/gaia_logs/ceto') -> logging.Logger:
    """Get or create timing logger."""
    global _timing_logger
    
    if _timing_logger is None:
        Path(log_dir).mkdir(parents=True, exist_ok=True)
        
        unique_id = str(uuid.uuid4())[:8]
        _timing_logger = logging.getLogger(f'timing.{unique_id}')
        _timing_logger.setLevel(logging.INFO)
        _timing_logger.handlers.clear()
        
        handler = FlushingFileHandler(Path(log_dir) / 'timing.log')
        handler.setFormatter(logging.Formatter('%(asctime)s - %(message)s'))
        _timing_logger.addHandler(handler)
        
        _timing_logger.info("INIT - Level 0004 timing log initialized")
        handler.flush()
    
    return _timing_logger


def log_timing(request_id: str, operation: str, duration_ms: float, 
               metadata: Optional[Dict[str, Any]] = None):
    """
    Log timing entry to centralized timing log.
    
    Args:
        request_id: Request ID for correlation
        operation: Description of timed operation  
        duration_ms: Duration in milliseconds
        metadata: Optional additional data
    """
    logger = _get_timing_logger()
    
    # Create log entry
    log_data = {
        'request_id': request_id,
        'operation': operation,
        'duration_ms': round(duration_ms, 1),
        'timestamp': datetime.now().isoformat()
    }
    
    if metadata:
        log_data.update(metadata)
    
    # Single timing log entry
    logger.info(f"TIMING - {request_id} - {operation} - {duration_ms:.1f}ms - {json.dumps(log_data)}")


def time_start() -> float:
    """Start timing - returns start time."""
    return time.time()


def time_end(start_time: float) -> float:
    """End timing - returns duration in milliseconds."""
    return (time.time() - start_time) * 1000


def generate_timing_id(prefix: str = "tm") -> str:
    """Generate timing request ID."""
    timestamp = datetime.now().strftime('%H%M%S')
    unique_id = str(uuid.uuid4())[:6]
    return f"{prefix}_{timestamp}_{unique_id}"


# Context manager for simple timing
class timer:
    """Simple timing context manager."""
    
    def __init__(self, operation: str, request_id: Optional[str] = None, **metadata):
        self.operation = operation
        self.request_id = request_id or generate_timing_id()
        self.metadata = metadata
        self.start_time = None
    
    def __enter__(self):
        self.start_time = time_start()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        duration_ms = time_end(self.start_time)
        
        # Add error info if exception occurred
        if exc_type:
            self.metadata.update({
                'error': True,
                'error_type': exc_type.__name__,
                'error_message': str(exc_val)
            })
        
        log_timing(self.request_id, self.operation, duration_ms, self.metadata)


# Simple integration with existing patterns
def log_existing_timing(request_id: str, operation: str, start_time: datetime, 
                       metadata: Optional[Dict[str, Any]] = None):
    """
    Log timing from existing datetime-based timing pattern.
    
    For existing code using: start_time = datetime.now()
    """
    duration_ms = (datetime.now() - start_time).total_seconds() * 1000
    log_timing(request_id, operation, duration_ms, metadata)