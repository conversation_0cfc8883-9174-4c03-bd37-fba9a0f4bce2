"""
Clean MCP Integration - Streamlined version with delegated tool handling

This replaces the monolithic mcp_integration.py with proper separation of concerns.
"""

import logging
import json
import os
from typing import List, Dict, Any, Optional
import sys
from pathlib import Path

# Simple absolute imports
from gaia_ceto_v2.tools.tools import echostring as core_echostring
from gaia_ceto_v2.tools.exa import ExaTools

# Tool response imports
from gaia_ceto_v2.core.tool_response import handle_tool_response, ToolResponse

logger = logging.getLogger(__name__)

# Level 0020: Setup dedicated log files using centralized factory
from .logging_factory import create_mcp_loggers, create_vendor_loggers, create_accounting_loggers

# Initialize Level 0020 loggers for MCP calls
_mcp_wrap_logger, _mcp_send_logger, _mcp_rcv_logger = create_mcp_loggers()

# Initialize vendor loggers for raw external API logging
_exa_req_logger, _exa_resp_logger = create_vendor_loggers('exa')

# Initialize accounting loggers 
_acct_logger, _acct_send_logger, _acct_rcv_logger = create_accounting_loggers()


class MCPToolHandler:
    """Clean MCP tool handler with delegated tool implementations."""
    
    def __init__(self, server_url: str = "http://localhost:9000/mcp/", 
                 simple_cache=None, accounting_system=None):
        """Initialize MCP handler.
        
        Args:
            server_url: MCP server URL
            simple_cache: Optional cache for tool calls
            accounting_system: Level 0008a AccountingSystem for API call tracking
        """
        self.server_url = server_url
        self.simple_cache = simple_cache
        self.accounting = accounting_system  # Level 0008a accounting
        self._tools_cache = []
        
        # Initialize Exa tools with logging, cache, and accounting support
        logger_instances = (_mcp_send_logger, _mcp_rcv_logger, _exa_req_logger, _exa_resp_logger)
        self.exa_tools = ExaTools(simple_cache=simple_cache, logger_instances=logger_instances, accounting=accounting_system)
        
        logger.info(f"MCP handler initialized for {server_url}")
        if accounting_system:
            logger.info("MCP handler initialized with accounting system enabled")
    
    def get_available_tools(self) -> List[str]:
        """Get list of available tools from MCP server."""
        try:
            # Return basic tool list for now
            return ["echostring", "get_time", "weather", "exa_search", "exa_research"]
        except Exception as e:
            logger.error(f"Failed to get available tools: {e}")
            return ["echostring"]  # Fallback to basic tools
    
    def execute_single_tool(self, tool_name: str, args: Dict[str, Any], request_id: str = None) -> Dict[str, Any]:
        """Execute a single MCP tool.
        
        Args:
            tool_name: Name of tool to execute
            args: Tool arguments
            request_id: Optional request ID for correlation
            
        Returns:
            Tool execution result
        """
        import uuid
        import time
        from datetime import datetime
        
        # Generate call_id for logging correlation
        call_id = request_id or str(uuid.uuid4())
        
        # Enhanced debug logging for tool execution
        logger.debug(f"🔧 Tool execution request:")
        logger.debug(f"   Tool: {tool_name}")
        logger.debug(f"   Args: {args}")
        logger.debug(f"   Call ID: {call_id}")
        if not args:
            logger.debug("   ⚠️  Warning: No arguments provided!")
        
        try:
            # Route to appropriate tool implementation
            if tool_name == "echostring":
                # Simple echo tool
                text = args.get("text", "")
                result = {
                    "success": True,
                    "result": core_echostring(text)
                }
                
            elif tool_name == "get_time":
                # Simple time tool
                result = {
                    "success": True,
                    "result": f"Current time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
                }
                
            elif tool_name == "weather":
                # Simple weather simulation
                location = args.get("location", "Unknown")
                result = {
                    "success": True,
                    "result": f"Weather in {location}: Sunny, 22°C (simulated)"
                }
                
            elif tool_name == "exa_search":
                # Delegate to dedicated Exa tools module
                result = self.exa_tools.exa_search(args, call_id)
                
            elif tool_name == "exa_research":
                # Delegate to dedicated Exa tools module
                result = self.exa_tools.exa_research_sync_smart_cache(args, call_id)
                
            else:
                # Unknown tool
                result = {
                    "success": False,
                    "error": f"Unknown tool: {tool_name}"
                }
            
            # Handle ToolResponse objects
            if isinstance(result, ToolResponse):
                processed_result = handle_tool_response(tool_name, result)
                # Check if ToolResponse indicates an error
                is_error = (hasattr(result, 'content') and 
                           isinstance(result.content, str) and 
                           result.content.startswith('Research API error:'))
                # Convert to legacy format for backward compatibility
                legacy_result = {
                    "success": not is_error,
                    "result": processed_result['content'] or processed_result['context_content'],
                    "document_id": processed_result.get('document_id'),
                    "metadata": processed_result.get('metadata', {})
                }
                if is_error:
                    legacy_result['error'] = result.content
                result = legacy_result
            elif not isinstance(result, dict):
                # If result is not a dict or ToolResponse, something went wrong
                result = {
                    "success": False,
                    "error": f"Invalid tool result type: {type(result)}"
                }
            
            # Level 0008a: Accounting now handled directly in tool implementation (closer to actual API calls)
            _acct_logger.info(f"TOOL_COMPLETED - {call_id} - tool={tool_name} success={result.get('success')} - accounting handled in tool layer")
            
            return result
            
        except Exception as e:
            logger.error(f"Tool execution error for {tool_name}: {e}")
            return {
                "success": False,
                "error": f"Tool execution failed: {str(e)}"
            }
    
    def get_available_tools_with_schema(self) -> List[Dict[str, Any]]:
        """Get available tools with their JSON schemas."""
        tools = [
            {
                "name": "echostring",
                "description": "Echo the input text with some modifications",
                "inputSchema": {
                    "type": "object",
                    "properties": {
                        "text": {
                            "type": "string",
                            "description": "Text to echo"
                        }
                    },
                    "required": ["text"]
                }
            },
            {
                "name": "get_time", 
                "description": "Get current date and time",
                "inputSchema": {
                    "type": "object",
                    "properties": {},
                    "required": []
                }
            },
            {
                "name": "weather",
                "description": "Get weather information for a location (simulated)",
                "inputSchema": {
                    "type": "object",
                    "properties": {
                        "location": {
                            "type": "string",
                            "description": "Location to get weather for"
                        }
                    },
                    "required": ["location"]
                }
            },
            {
                "name": "exa_search",
                "description": "Search the web for information. Pass the user's search term as the query parameter.",
                "inputSchema": {
                    "type": "object",
                    "properties": {
                        "query": {
                            "type": "string",
                            "description": "The search term or question (e.g., 'latest rocket launches', 'SpaceX news')"
                        },
                        "num_results": {
                            "type": "integer", 
                            "description": "Number of results to return",
                            "default": 3
                        }
                    },
                    "required": ["query"]
                }
            },
            {
                "name": "exa_research",
                "description": "Conduct deep research on a topic. Pass the user's research topic as the query parameter.",
                "inputSchema": {
                    "type": "object",
                    "properties": {
                        "query": {
                            "type": "string",
                            "description": "The research topic or question to investigate (e.g., 'rocket ships', 'climate change solutions')"
                        },
                        "max_sources": {
                            "type": "integer",
                            "description": "Maximum number of sources to use",
                            "default": 10
                        },
                        "use_schema": {
                            "type": "boolean", 
                            "description": "Use structured output schema",
                            "default": False
                        },
                        "bypass_cache": {
                            "type": "boolean",
                            "description": "Skip cache check and force fresh research (still stores result for future use)",
                            "default": False
                        }
                    },
                    "required": ["query"]
                }
            }
        ]
        
        return tools