"""
Simplified LiteLL<PERSON> Wrapper using decorators instead of inheritance.

Replaces 400+ lines of logging complexity with simple decorators.
"""

import logging
from datetime import datetime
from typing import Any, Dict, List, Optional

import litellm
from .llm_errors import LLMResponse, classify_litellm_error
from .logging_decorators import log_llm_operation, log_cached_operation

logger = logging.getLogger(__name__)


class SimpleLiteLLMWrapper:
    """A clean wrapper for litellm using decorators for logging."""

    def __init__(self,
                 cache=None,
                 cost_tracker=None,
                 simple_cache=None,
                 accounting_system=None):
        """
        Initialize the wrapper.
        
        Args:
            cache: Legacy caching mechanism
            cost_tracker: Track costs of LLM calls
            simple_cache: TTL cache instance
            accounting_system: Cost tracking system
        """
        self.cache = cache
        self.cost_tracker = cost_tracker
        self.simple_cache = simple_cache
        self.accounting = accounting_system

    @log_llm_operation("completion")
    def completion(self,
                   model: str,
                   messages: List[Dict[str, str]],
                   request_id: str = None,
                   **kwargs: Any) -> LLMResponse:
        """
        Call the LLM with automatic logging via decorator.
        
        Args:
            model: Model name (e.g., 'gpt-3.5-turbo')
            messages: Conversation messages
            request_id: Optional request ID for correlation
            **kwargs: Additional litellm arguments
            
        Returns:
            LLMResponse object
        """
        # Check cache if available
        if self.simple_cache:
            cache_key = self._generate_cache_key(model, messages, **kwargs)
            cached = self.simple_cache.get(cache_key)
            if cached:
                logger.info(f"🗃️ Cache hit for {model}")
                return cached
        
        try:
            # Make the actual LLM call
            response = litellm.completion(
                model=model,
                messages=messages,
                **kwargs
            )
            
            # Extract response text
            response_text = response.choices[0].message.content
            
            # Cache if available
            if self.simple_cache:
                self.simple_cache.set(cache_key, response_text, ttl=3600)
            
            # Track costs if available
            if self.accounting and request_id:
                self._track_costs(model, messages, response, request_id)
            
            return LLMResponse(
                success=True,
                content=response_text,
                model=model,
                raw_response=response
            )
            
        except Exception as e:
            error_type = classify_litellm_error(e)
            return LLMResponse(
                success=False,
                content="",
                error=str(e),
                error_type=error_type,
                model=model
            )

    @log_llm_operation("stream_completion")
    def stream_completion(self,
                         model: str,
                         messages: List[Dict[str, str]],
                         request_id: str = None,
                         **kwargs: Any):
        """
        Stream LLM responses with automatic logging.
        
        Yields response chunks as they arrive.
        """
        try:
            # Force streaming
            kwargs['stream'] = True
            
            # Make streaming call
            response = litellm.completion(
                model=model,
                messages=messages,
                **kwargs
            )
            
            # Yield chunks
            for chunk in response:
                yield chunk
                
        except Exception as e:
            logger.error(f"Streaming error: {e}")
            raise

    def _generate_cache_key(self, model: str, messages: List[Dict], **kwargs) -> str:
        """Generate a cache key from request parameters."""
        import hashlib
        import json
        
        # Create deterministic string from inputs
        key_data = {
            'model': model,
            'messages': messages,
            'temperature': kwargs.get('temperature', 1.0),
            'max_tokens': kwargs.get('max_tokens')
        }
        
        key_str = json.dumps(key_data, sort_keys=True)
        return hashlib.md5(key_str.encode()).hexdigest()

    def _track_costs(self, model: str, messages: List[Dict], response: Any, request_id: str):
        """Track costs using accounting system."""
        if not self.accounting:
            return
            
        try:
            # Calculate tokens (simplified)
            prompt_tokens = sum(len(m.get('content', '')) // 4 for m in messages)
            completion_tokens = len(response.choices[0].message.content) // 4
            
            self.accounting.record_transaction(
                request_id=request_id,
                service='llm',
                model=model,
                input_tokens=prompt_tokens,
                output_tokens=completion_tokens,
                cost=0.0  # Would calculate based on model pricing
            )
        except Exception as e:
            logger.warning(f"Cost tracking failed: {e}")


# Factory function
def create_llm_wrapper(**kwargs) -> SimpleLiteLLMWrapper:
    """Create an LLM wrapper with the specified configuration."""
    return SimpleLiteLLMWrapper(**kwargs)