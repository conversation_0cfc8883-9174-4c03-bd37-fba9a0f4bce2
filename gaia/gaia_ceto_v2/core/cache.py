"""
Clean cache implementation - Level 0008b TTL file cache.
Eliminates strategy explosion and adapter complexity.
"""

import json
import hashlib
import time
import logging
import uuid
from pathlib import Path
from typing import Optional, Any, Dict, Union, Callable
from dataclasses import dataclass, asdict

logger = logging.getLogger(__name__)

# Cache-specific loggers for dedicated logging
try:
    from .logging_factory import create_vendor_loggers
    _cache_read_logger, _cache_write_logger = create_vendor_loggers('cache')
except ImportError:
    _cache_read_logger = _cache_write_logger = logger

@dataclass
class CacheEntry:
    """Cache entry with TTL and metadata."""
    data: Any
    created_at: float
    ttl_seconds: int
    cache_key: str
    entry_type: str  # 'llm', 'tool', 'api'
    
    @property
    def is_expired(self) -> bool:
        """Check if cache entry has expired."""
        return time.time() - self.created_at > self.ttl_seconds
    
    @property
    def expires_in(self) -> float:
        """Seconds until expiration (negative if expired)."""
        return (self.created_at + self.ttl_seconds) - time.time()

class Cache:
    """Simple TTL file cache - Level 0008b compliant."""
    
    def __init__(self, cache_dir: Union[str, Path], default_ttl: int = 3600):
        """
        Initialize cache with subfolder structure.
        
        Args:
            cache_dir: Base directory to store cache files
            default_ttl: Default TTL in seconds (1 hour)
        """
        self.base_cache_dir = Path(cache_dir)
        self.default_ttl = default_ttl
        
        # Create base cache directory
        self.base_cache_dir.mkdir(parents=True, exist_ok=True)
        
        # Create subfolders for different cache types
        self.subfolders = {
            'exa_search': self.base_cache_dir / 'exa_search',
            'exa_research': self.base_cache_dir / 'exa_research', 
            'general': self.base_cache_dir / 'general'
        }
        
        # Create all subfolders
        for subfolder in self.subfolders.values():
            subfolder.mkdir(parents=True, exist_ok=True)
        
        logger.info(f"Cache initialized: {self.base_cache_dir} with subfolders (TTL: {default_ttl}s)")
    
    def _generate_cache_key(self, data: Dict) -> str:
        """Generate cache key from data dictionary."""
        sorted_data = json.dumps(data, sort_keys=True, ensure_ascii=True)
        return hashlib.sha256(sorted_data.encode('utf-8')).hexdigest()[:16]
    
    def _get_cache_file(self, cache_key: str, entry_type: str = "general") -> Path:
        """Get cache file path for given key in appropriate subfolder."""
        # Determine subfolder based on entry type or key prefix
        if entry_type == "exa_search" or cache_key.startswith("exa_search_"):
            cache_dir = self.subfolders['exa_search']
            # Remove prefix from key since folder provides context
            clean_key = cache_key.replace("exa_search_", "") if cache_key.startswith("exa_search_") else cache_key
        elif entry_type == "exa_research" or cache_key.startswith("exa_research_"):
            cache_dir = self.subfolders['exa_research']
            # Remove prefix from key since folder provides context
            clean_key = cache_key.replace("exa_research_", "") if cache_key.startswith("exa_research_") else cache_key
        else:
            cache_dir = self.subfolders['general']
            clean_key = cache_key
        
        return cache_dir / f"{clean_key}.json"
    
    def get(self, cache_key: str, request_id: str = None, entry_type: str = "general") -> Optional[Any]:
        """Get cached value by key."""
        cache_file = self._get_cache_file(cache_key, entry_type)
        
        # Generate request_id if not provided
        if request_id is None:
            request_id = str(uuid.uuid4())
        
        if not cache_file.exists():
            # Log cache miss
            _cache_read_logger.info(f"CACHE_MISS - {request_id} - {time.time()} - {cache_key} - file_not_found")
            return None
        
        try:
            with open(cache_file, 'r') as f:
                entry_dict = json.load(f)
                entry = CacheEntry(**entry_dict)
            
            if entry.is_expired:
                cache_file.unlink(missing_ok=True)
                # Log cache miss due to expiration
                _cache_read_logger.info(f"CACHE_MISS - {request_id} - {time.time()} - {cache_key} - expired")
                return None
            
            # Log cache hit with truncated value
            value_preview = str(entry.data)[:100] + "..." if len(str(entry.data)) > 100 else str(entry.data)
            _cache_read_logger.info(f"CACHE_HIT - {request_id} - {time.time()} - {cache_key} - {value_preview}")
            
            return entry.data
            
        except (json.JSONDecodeError, KeyError, OSError) as e:
            logger.warning(f"Cache read error for {cache_key}: {e}")
            cache_file.unlink(missing_ok=True)
            # Log cache error
            _cache_read_logger.info(f"CACHE_ERROR - {request_id} - {time.time()} - {cache_key} - {str(e)}")
            return None
    
    def put(self, cache_key: str, data: Any, ttl: Optional[int] = None, entry_type: str = "unknown", request_id: str = None) -> None:
        """Store data in cache."""
        ttl = ttl or self.default_ttl
        
        # Generate request_id if not provided
        if request_id is None:
            request_id = str(uuid.uuid4())
        
        entry = CacheEntry(
            data=data,
            created_at=time.time(),
            ttl_seconds=ttl,
            cache_key=cache_key,
            entry_type=entry_type
        )
        
        cache_file = self._get_cache_file(cache_key, entry_type)
        
        try:
            with open(cache_file, 'w') as f:
                json.dump(asdict(entry), f, indent=2)
            
            # Log cache write with truncated value
            value_preview = str(data)[:100] + "..." if len(str(data)) > 100 else str(data)
            _cache_write_logger.info(f"CACHE_WRITE - {request_id} - {time.time()} - {cache_key} - {value_preview}")
            
        except OSError as e:
            logger.error(f"Cache write error for {cache_key}: {e}")
            # Log cache write error
            _cache_write_logger.info(f"CACHE_WRITE_ERROR - {request_id} - {time.time()} - {cache_key} - {str(e)}")
    
    def get_or_compute(self, key_data: Union[str, Dict], compute_func: Callable, ttl: Optional[int] = None, entry_type: str = "unknown", request_id: str = None) -> Any:
        """Get cached value or compute and cache it.
        
        Args:
            key_data: Either a string cache key OR a dict to generate cache key from
            compute_func: Function to call if cache miss
            ttl: TTL in seconds
            entry_type: Type of cache entry
            request_id: Optional request ID for logging
        """
        # Support both string keys and dict-based key generation
        if isinstance(key_data, str):
            cache_key = key_data
        else:
            cache_key = self._generate_cache_key(key_data)
        
        # Generate request_id if not provided
        if request_id is None:
            request_id = str(uuid.uuid4())
        
        # Try cache first
        cached_value = self.get(cache_key, request_id=request_id, entry_type=entry_type)
        if cached_value is not None:
            return cached_value
        
        # Cache miss - compute value
        computed_value = compute_func()
        
        # Store in cache
        self.put(cache_key, computed_value, ttl=ttl, entry_type=entry_type, request_id=request_id)
        
        return computed_value
    
    def clear(self, subfolder: str = None) -> int:
        """Clear cache entries, optionally from specific subfolder."""
        count = 0
        
        if subfolder and subfolder in self.subfolders:
            # Clear specific subfolder
            cache_dir = self.subfolders[subfolder]
            for cache_file in cache_dir.glob("*.json"):
                cache_file.unlink()
                count += 1
            logger.info(f"Cache cleared: {count} entries from {subfolder}")
        else:
            # Clear all subfolders
            for folder_name, cache_dir in self.subfolders.items():
                for cache_file in cache_dir.glob("*.json"):
                    cache_file.unlink()
                    count += 1
            logger.info(f"Cache cleared: {count} entries from all folders")
        
        return count

# Convenience functions
def create_tool_cache_key(tool_name: str, args: Dict[str, Any]) -> Dict[str, Any]:
    """Create cache key for tool execution results."""
    return {
        'type': 'tool',
        'tool_name': tool_name,
        'args': args
    }

def create_llm_cache_key(provider: str, model: str, messages: list, **kwargs) -> Dict[str, Any]:
    """Create cache key for LLM responses."""
    return {
        'type': 'llm',
        'provider': provider,
        'model': model,
        'messages': messages,
        'kwargs': kwargs
    }

def cache_llm_call(cache, provider: str, model: str, messages: list, 
                   tools: list = None, func_to_call = None, **kwargs):
    """Universal LLM caching wrapper."""
    
    # Generate key from clean data
    clean_data = {
        'model': model,
        'messages': [{'role': m.get('role'), 'content': m.get('content')} for m in messages],
        'tools': sorted([{'name': t.get('name'), 'schema': t.get('schema', {})} 
                        for t in (tools or [])], key=lambda x: x['name'])
    }
    
    cache_key = cache._generate_cache_key(clean_data)
    
    # Try cache first
    cached = cache.get(cache_key, entry_type=provider)
    if cached:
        return cached
    
    # Call function and cache result
    result = func_to_call()
    cache.put(cache_key, result, entry_type=provider)
    return result

# Backward compatibility alias
SimpleCache = Cache