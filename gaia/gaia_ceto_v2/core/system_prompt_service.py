"""
SystemPromptService - Handles system prompt management.
"""

import json
import logging
from pathlib import Path
from typing import Dict

logger = logging.getLogger(__name__)


class SystemPromptService:
    """Focused service for system prompt management."""
    
    def __init__(self, system_prompts_file: str = "prompt_sys.json"):
        self.base_prompt, self.system_prompts = self._load_system_prompts(system_prompts_file)
    
    def _load_system_prompts(self, prompts_file: str) -> tuple:
        """Load system prompts from JSON file."""
        try:
            prompts_path = Path(prompts_file)
            if not prompts_path.exists():
                logger.warning(f"System prompts file not found: {prompts_file}")
                return "You are a helpful AI assistant.", {"default": "Provide clear, accurate responses."}
            
            with open(prompts_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            base_prompt = data.get('base_prompt', "You are a helpful AI assistant.")
            
            prompts = {}
            for key, info in data.get('prompts', {}).items():
                prompts[key] = info.get('prompt', '')
            
            logger.info(f"Loaded base prompt + {len(prompts)} persona prompts")
            return base_prompt, prompts
            
        except Exception as e:
            logger.error(f"Error loading system prompts: {e}")
            return "You are a helpful AI assistant.", {"default": "Provide clear, accurate responses."}
    
    def get_system_prompt_text(self, prompt_key_or_text: str) -> str:
        """Get combined system prompt (base + persona) by key or return text directly."""
        if prompt_key_or_text in self.system_prompts:
            persona_prompt = self.system_prompts[prompt_key_or_text]
            return f"{self.base_prompt}\n\n{persona_prompt}"
        
        # If not a known key, treat as direct text and still prepend base
        if prompt_key_or_text and prompt_key_or_text != self.base_prompt:
            return f"{self.base_prompt}\n\n{prompt_key_or_text}"
        
        # Just base prompt if empty or already the base
        return self.base_prompt
    
    def get_available_system_prompts(self) -> Dict[str, str]:
        """Get available system prompt keys and names."""
        return {key: f"System prompt: {key}" for key in self.system_prompts.keys()}