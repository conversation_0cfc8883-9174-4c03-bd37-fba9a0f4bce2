"""
Conversation data model - Pure data structure with no business logic.

This module provides a clean, simple data model for chat conversations
without any dependencies on storage, LLMs, or external services.
"""

import json
import uuid
from datetime import datetime
from typing import Dict, List, Any, Optional


class Message:
    """A single message in a conversation."""
    
    def __init__(self, role: str, content: str, timestamp: str = None):
        """Initialize a message.
        
        Args:
            role: The role of the message sender ('user', 'assistant', 'system')
            content: The message content
            timestamp: ISO timestamp string, auto-generated if not provided
        """
        self.role = role
        self.content = content
        self.timestamp = timestamp or datetime.now().isoformat()
    
    def to_dict(self) -> Dict[str, str]:
        """Convert message to dictionary for serialization."""
        return {
            'role': self.role,
            'content': self.content,
            'timestamp': self.timestamp
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, str]) -> 'Message':
        """Create message from dictionary."""
        # Filter out any unexpected keys like 'metadata'
        return cls(
            role=data['role'],
            content=data['content'],
            timestamp=data.get('timestamp')
        )
    
    def __repr__(self) -> str:
        return f"Message(role='{self.role}', content='{self.content[:50]}...', timestamp='{self.timestamp}')"


class Conversation:
    """A conversation containing multiple messages.
    
    This is a pure data model with no business logic - it only handles
    the data structure and serialization of conversations.
    """
    
    def __init__(self, 
                 conversation_id: str = None,
                 user_id: str = None, 
                 title: str = None,
                 metadata: Dict[str, Any] = None):
        """Initialize a conversation.
        
        Args:
            conversation_id: Unique identifier, auto-generated if not provided
            user_id: ID of the user who owns this conversation
            title: Human-readable title for the conversation
            metadata: Additional metadata dictionary
        """
        self.conversation_id = conversation_id or str(uuid.uuid4())
        self.user_id = user_id
        self.title = title or f"Chat {datetime.now().strftime('%H:%M')}"
        self.metadata = metadata or {}
        self.messages: List[Message] = []
        self.created_at = datetime.now().isoformat()
        self.updated_at = self.created_at
    
    def add_message(self, role: str, content: str) -> None:
        """Add a message to the conversation.
        
        Args:
            role: The role of the message sender ('user', 'assistant', 'system')
            content: The message content
        """
        message = Message(role, content)
        self.messages.append(message)
        self.updated_at = datetime.now().isoformat()
    
    def get_messages(self, limit: int = None) -> List[Dict[str, str]]:
        """Get messages as dictionaries, optionally limited to recent ones.
        
        Args:
            limit: Maximum number of recent messages to return
            
        Returns:
            List of message dictionaries
        """
        messages = self.messages
        if limit:
            messages = messages[-limit:]  # Get the most recent messages
        
        return [msg.to_dict() for msg in messages]
    
    def get_message_count(self) -> int:
        """Get the total number of messages in this conversation."""
        return len(self.messages)
    
    def get_last_message(self) -> Optional[Dict[str, str]]:
        """Get the last message in the conversation."""
        if not self.messages:
            return None
        return self.messages[-1].to_dict()
    
    def clear_messages(self) -> None:
        """Remove all messages from the conversation."""
        self.messages.clear()
        self.updated_at = datetime.now().isoformat()
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert conversation to dictionary for serialization.
        
        Returns:
            Dictionary representation suitable for JSON serialization
        """
        return {
            'conversation_id': self.conversation_id,
            'user_id': self.user_id,
            'title': self.title,
            'metadata': self.metadata,
            'messages': [msg.to_dict() for msg in self.messages],
            'created_at': self.created_at,
            'updated_at': self.updated_at
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Conversation':
        """Create conversation from dictionary.
        
        Args:
            data: Dictionary containing conversation data
            
        Returns:
            Conversation instance
        """
        # Create conversation with basic info
        conv = cls(
            conversation_id=data.get('conversation_id'),
            user_id=data.get('user_id'),
            title=data.get('title'),
            metadata=data.get('metadata', {})
        )
        
        # Set timestamps if provided
        conv.created_at = data.get('created_at', conv.created_at)
        conv.updated_at = data.get('updated_at', conv.updated_at)
        
        # Add messages
        messages_data = data.get('messages', [])
        for msg_data in messages_data:
            conv.messages.append(Message.from_dict(msg_data))
        
        return conv
    
    def to_json(self) -> str:
        """Convert conversation to JSON string."""
        return json.dumps(self.to_dict(), indent=2)
    
    @classmethod
    def from_json(cls, json_str: str) -> 'Conversation':
        """Create conversation from JSON string."""
        data = json.loads(json_str)
        return cls.from_dict(data)
    
    def __repr__(self) -> str:
        return (f"Conversation(id='{self.conversation_id}', "
                f"user_id='{self.user_id}', "
                f"title='{self.title}', "
                f"messages={len(self.messages)})")
    
    def __str__(self) -> str:
        return f"Conversation '{self.title}' ({len(self.messages)} messages)"


def create_conversation(user_id: str, title: str = None, **metadata) -> Conversation:
    """Create a new conversation with metadata.
    
    Simple factory function for backward compatibility with tests.
    LEVELS.md compliant: thin wrapper, no complex logic.
    
    Args:
        user_id: User ID for the conversation
        title: Optional conversation title
        **metadata: Additional metadata as keyword arguments
        
    Returns:
        New Conversation instance
    """
    return Conversation(
        user_id=user_id,
        title=title or "Untitled Conversation",
        metadata=metadata
    )


def conversation_summary(conv: Conversation) -> Dict[str, Any]:
    """Get a summary of conversation metadata without full message content.
    
    Args:
        conv: Conversation to summarize
        
    Returns:
        Dictionary with conversation summary
    """
    last_msg = conv.get_last_message()
    return {
        'conversation_id': conv.conversation_id,
        'user_id': conv.user_id,
        'title': conv.title,
        'message_count': conv.get_message_count(),
        'created_at': conv.created_at,
        'updated_at': conv.updated_at,
        'last_message_role': last_msg['role'] if last_msg else None,
        'last_message_preview': last_msg['content'][:100] + '...' if last_msg and len(last_msg['content']) > 100 else last_msg['content'] if last_msg else None
    }