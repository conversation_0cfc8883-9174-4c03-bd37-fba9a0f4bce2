"""
Chat Statistics - Metrics collection and monitoring for conversations.

Extracted from ChatManager to follow Single Responsibility Principle.
Handles all statistics, monitoring, and analytics independently.
"""

import logging
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from collections import defaultdict, Counter

from .conversation import Conversation

logger = logging.getLogger(__name__)


class ChatStatistics:
    """Statistics collector for chat system monitoring.
    
    Tracks metrics, usage patterns, and system health independently
    from business logic. Provides insights for monitoring and optimization.
    """
    
    def __init__(self, retention_days: int = 30):
        """Initialize statistics collector.
        
        Args:
            retention_days: How long to keep detailed statistics
        """
        self.retention_days = retention_days
        
        # Metrics storage
        self._message_counts = defaultdict(int)  # {date: count}
        self._conversation_counts = defaultdict(int)  # {date: count}
        self._user_activity = defaultdict(list)  # {user_id: [timestamps]}
        self._response_times = []  # [(timestamp, duration_ms)]
        self._error_counts = defaultdict(int)  # {error_type: count}
        
        # Real-time counters
        self._total_messages = 0
        self._total_conversations = 0
        self._active_users = set()
        
        logger.info(f"ChatStatistics initialized with {retention_days} day retention")
    
    def record_message(self, conv_id: str, role: str, user_id: str = None, 
                      response_time_ms: float = None) -> None:
        """Record a message for statistics.
        
        Args:
            conv_id: Conversation ID
            role: Message role (user/assistant/system)
            user_id: Optional user ID for user tracking
            response_time_ms: Optional response time in milliseconds
        """
        current_time = time.time()
        date_key = datetime.fromtimestamp(current_time).strftime('%Y-%m-%d')
        
        # Update counters
        self._total_messages += 1
        self._message_counts[date_key] += 1
        
        # Track user activity
        if user_id:
            self._user_activity[user_id].append(current_time)
            self._active_users.add(user_id)
        
        # Track response times for assistant messages
        if role == 'assistant' and response_time_ms is not None:
            self._response_times.append((current_time, response_time_ms))
        
        # Clean old data periodically
        if self._total_messages % 100 == 0:  # Every 100 messages
            self._cleanup_old_data()
        
        logger.debug(f"Recorded {role} message for conversation {conv_id}")
    
    def record_conversation_created(self, conv_id: str, user_id: str = None) -> None:
        """Record creation of a new conversation.
        
        Args:
            conv_id: Conversation ID
            user_id: Optional user ID
        """
        current_time = time.time()
        date_key = datetime.fromtimestamp(current_time).strftime('%Y-%m-%d')
        
        self._total_conversations += 1
        self._conversation_counts[date_key] += 1
        
        if user_id:
            self._active_users.add(user_id)
        
        logger.debug(f"Recorded conversation creation: {conv_id}")
    
    def record_error(self, error_type: str, details: str = None) -> None:
        """Record an error for monitoring.
        
        Args:
            error_type: Type/category of error
            details: Optional error details
        """
        self._error_counts[error_type] += 1
        logger.warning(f"Recorded error: {error_type} - {details}")
    
    def get_conversation_stats(self, conversation: Conversation) -> Optional[Dict[str, Any]]:
        """Get detailed statistics for a specific conversation.
        
        Args:
            conversation: Conversation object to analyze
            
        Returns:
            Dictionary with conversation-specific statistics
        """
        if not conversation:
            return None
        
        try:
            messages = conversation.get_messages()
            
            # Message analysis
            role_counts = Counter(msg['role'] for msg in messages)
            total_chars = sum(len(msg['content']) for msg in messages)
            avg_msg_length = total_chars / len(messages) if messages else 0
            
            # Time analysis
            first_msg_time = messages[0]['timestamp'] if messages else None
            last_msg_time = messages[-1]['timestamp'] if messages else None
            duration = None
            
            if first_msg_time and last_msg_time:
                try:
                    first_dt = datetime.fromisoformat(first_msg_time.replace('Z', '+00:00'))
                    last_dt = datetime.fromisoformat(last_msg_time.replace('Z', '+00:00'))
                    duration = (last_dt - first_dt).total_seconds()
                except Exception:
                    duration = None
            
            return {
                'conversation_id': conversation.conversation_id,
                'total_messages': len(messages),
                'user_messages': role_counts.get('user', 0),
                'assistant_messages': role_counts.get('assistant', 0),
                'system_messages': role_counts.get('system', 0),
                'total_characters': total_chars,
                'avg_message_length': round(avg_msg_length, 2),
                'conversation_duration_seconds': duration,
                'created_at': conversation.created_at,
                'updated_at': conversation.updated_at,
                'title': conversation.title,
                'user_id': getattr(conversation, 'user_id', None)
            }
            
        except Exception as e:
            logger.error(f"Error calculating conversation stats: {e}")
            return None
    
    def get_system_stats(self) -> Dict[str, Any]:
        """Get overall system statistics.
        
        Returns:
            Dictionary with system-wide statistics
        """
        current_time = time.time()
        
        # Calculate recent activity (last 24 hours)
        yesterday = current_time - 86400
        recent_messages = len([
            t for t, _ in self._response_times 
            if t > yesterday
        ])
        
        # Calculate average response time
        recent_response_times = [
            duration for timestamp, duration in self._response_times
            if timestamp > yesterday
        ]
        avg_response_time = (
            sum(recent_response_times) / len(recent_response_times)
            if recent_response_times else 0
        )
        
        # User activity analysis
        active_users_today = len([
            user_id for user_id, timestamps in self._user_activity.items()
            if any(t > yesterday for t in timestamps)
        ])
        
        return {
            'total_messages': self._total_messages,
            'total_conversations': self._total_conversations,
            'total_unique_users': len(self._active_users),
            'active_users_24h': active_users_today,
            'messages_24h': recent_messages,
            'avg_response_time_ms': round(avg_response_time, 2),
            'error_counts': dict(self._error_counts),
            'system_health': self._calculate_health_score(),
            'timestamp': datetime.now().isoformat()
        }
    
    def get_daily_stats(self, days: int = 7) -> List[Dict[str, Any]]:
        """Get daily statistics for the specified number of days.
        
        Args:
            days: Number of days to include
            
        Returns:
            List of daily statistics dictionaries
        """
        results = []
        base_date = datetime.now()
        
        for i in range(days):
            date = base_date - timedelta(days=i)
            date_key = date.strftime('%Y-%m-%d')
            
            results.append({
                'date': date_key,
                'messages': self._message_counts.get(date_key, 0),
                'conversations': self._conversation_counts.get(date_key, 0)
            })
        
        return results
    
    def get_user_stats(self, user_id: str) -> Optional[Dict[str, Any]]:
        """Get statistics for a specific user.
        
        Args:
            user_id: User ID to analyze
            
        Returns:
            Dictionary with user-specific statistics
        """
        if user_id not in self._user_activity:
            return None
        
        timestamps = self._user_activity[user_id]
        current_time = time.time()
        
        # Activity analysis
        recent_activity = [t for t in timestamps if current_time - t < 86400]
        total_sessions = len(timestamps)
        
        # First and last activity
        first_activity = min(timestamps) if timestamps else None
        last_activity = max(timestamps) if timestamps else None
        
        return {
            'user_id': user_id,
            'total_sessions': total_sessions,
            'sessions_24h': len(recent_activity),
            'first_activity': datetime.fromtimestamp(first_activity).isoformat() if first_activity else None,
            'last_activity': datetime.fromtimestamp(last_activity).isoformat() if last_activity else None,
            'days_active': len(set(
                datetime.fromtimestamp(t).strftime('%Y-%m-%d') 
                for t in timestamps
            ))
        }
    
    def _calculate_health_score(self) -> float:
        """Calculate system health score (0-100).
        
        Returns:
            Health score as percentage
        """
        score = 100.0
        
        # Deduct points for errors
        total_errors = sum(self._error_counts.values())
        if total_errors > 0:
            error_rate = total_errors / max(self._total_messages, 1)
            score -= min(error_rate * 1000, 50)  # Max 50 point deduction
        
        # Deduct points for slow response times
        if self._response_times:
            recent_times = [
                duration for timestamp, duration in self._response_times[-100:]
            ]
            avg_time = sum(recent_times) / len(recent_times)
            if avg_time > 5000:  # 5 seconds
                score -= min((avg_time - 5000) / 1000, 30)  # Max 30 point deduction
        
        return max(0.0, min(100.0, score))
    
    def _cleanup_old_data(self) -> None:
        """Remove data older than retention period."""
        cutoff_time = time.time() - (self.retention_days * 86400)
        
        # Clean response times
        self._response_times = [
            (timestamp, duration) for timestamp, duration in self._response_times
            if timestamp > cutoff_time
        ]
        
        # Clean user activity
        for user_id in list(self._user_activity.keys()):
            self._user_activity[user_id] = [
                timestamp for timestamp in self._user_activity[user_id]
                if timestamp > cutoff_time
            ]
            if not self._user_activity[user_id]:
                del self._user_activity[user_id]
        
        logger.debug("Cleaned up old statistics data")


# Factory function for easy initialization
# Factory function removed - use ChatStatistics(retention_days) directly