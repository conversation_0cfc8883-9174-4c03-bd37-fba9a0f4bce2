"""
Google Gemini LLM Provider with Native Function Calling Support.

This module implements the Gemini provider with native function calling
capabilities, allowing the LLM to intelligently decide when to use tools.
"""

import os
import json
import logging
from typing import List, Dict, Any, Optional

from .tool_calling_interface import <PERSON>lCapableLLM, ToolCapableLLMResponse, ToolCall, ToolCallResult
from .llm_providers import LLMProvider
from .llm_errors import LLMResponse as BaseLLMResponse
from .level_0004_simple import log_existing_timing

logger = logging.getLogger(__name__)

# Level 0007: Setup dedicated log files using centralized factory
from .logging_factory import create_llm_loggers

# Initialize Level 0007 loggers for Gemini calls
_gemini_wrap_logger, _gemini_send_logger, _gemini_rcv_logger = create_llm_loggers()

class GeminiToolLLM(ToolCapableLLM, LLMProvider):
    """Google Gemini implementation with native function calling."""
    
    def __init__(self, model_name: str = "gemini-2.0-flash-exp", temperature: float = 0.0, simple_cache=None):
        """Initialize the Gemini LLM with tool calling support.
        
        Args:
            model_name: The Gemini model to use
            temperature: Generation temperature (0.0 to 1.0)
            simple_cache: Optional cache for caching responses
        """
        super().__init__()
        self.model_name = model_name
        self.temperature = temperature
        self.client = None
        self.simple_cache = simple_cache
        
        # Initialize Gemini client
        self._init_client()
    
    def _init_client(self):
        """Initialize the Gemini client."""
        try:
            import google.generativeai as genai
            
            # Get API key from environment
            api_key = os.getenv('GEMINI_API_KEY') or os.getenv('GOOGLE_API_KEY')
            if not api_key:
                raise ValueError("GEMINI_API_KEY or GOOGLE_API_KEY environment variable not set")
            
            # Configure the client
            genai.configure(api_key=api_key)
            
            # Create the model
            self.client = genai.GenerativeModel(
                model_name=self.model_name,
                generation_config=genai.types.GenerationConfig(
                    temperature=self.temperature,
                    max_output_tokens=2048,
                )
            )
            
            # Store the genai module for later use
            self.genai = genai
            
            logger.info(f"Initialized Gemini client with model: {self.model_name}")
            
        except ImportError:
            logger.error("google-generativeai package not installed. Please install it with 'pip install google-generativeai'")
            raise
        except Exception as e:
            logger.error(f"Failed to initialize Gemini client: {e}")
            raise
    
    def supports_tools(self) -> bool:
        """Return True - Gemini supports native function calling."""
        return True
    
    def get_model_name(self) -> str:
        """Get the model name."""
        return self.model_name
    
    def generate_response(self, prompt: str, context: List[Dict[str, Any]], request_id: str = None, **kwargs) -> BaseLLMResponse:
        """Legacy interface - generate response without tools."""
        from datetime import datetime
        import json
        
        start_time = datetime.now()
        # Use provided request_id for correlation, or generate call_id as fallback
        call_id = request_id if request_id else f"gemini_{start_time.strftime('%H%M%S')}_{id(self) % 1000}"
        
        try:
            # Level 0007 logging: Log request
            _gemini_wrap_logger.info(f"REQUEST - {call_id} - {self.model_name} - legacy generate_response")
            
            # Convert context to Gemini format
            messages = self._convert_messages_to_gemini(context + [{"role": "user", "content": prompt}])
            
            # Level 0007 logging: Log send data
            send_data = {
                'call_id': call_id,
                'model': self.model_name,
                'prompt': prompt,
                'context_length': len(context),
                'kwargs': kwargs,
                'timestamp': datetime.now().isoformat()
            }
            _gemini_send_logger.info(f"SEND - {call_id} - {json.dumps(send_data)}")
            
            # Generate without tools
            response = self.client.generate_content(
                contents=messages,
                generation_config=self.genai.types.GenerationConfig(
                    temperature=kwargs.get('temperature', self.temperature),
                    max_output_tokens=kwargs.get('max_tokens', 2048)
                )
            )
            
            # Level 0007 logging: Log success
            content = self._safe_extract_text(response)
            duration_ms = (datetime.now() - start_time).total_seconds() * 1000
            char_count = len(content) if content else 0
            _gemini_wrap_logger.info(f"SUCCESS - {call_id} - {char_count} chars - {duration_ms:.0f}ms")
            
            # Level 0004: Log timing for successful generate
            log_existing_timing(call_id, "gemini_generate", start_time, {
                "model": self.model_name, "char_count": char_count, "success": True
            })
            
            # Level 0007 logging: Log receive data
            response_data = {
                'call_id': call_id,
                'content': content,
                'char_count': char_count,
                'duration_ms': duration_ms,
                'timestamp': datetime.now().isoformat()
            }
            _gemini_rcv_logger.info(f"RECEIVE - {call_id} - {json.dumps(response_data)}")
            
            return BaseLLMResponse.success_response(
                content=content,
                metadata={'model': self.model_name, 'provider': 'gemini'}
            )
            
        except Exception as e:
            # Level 0007 logging: Log error
            duration_ms = (datetime.now() - start_time).total_seconds() * 1000
            _gemini_wrap_logger.error(f"ERROR - {call_id} - {self.model_name} - {type(e).__name__}: {str(e)} - {duration_ms:.0f}ms")
            
            # Level 0004: Log timing for generate error
            log_existing_timing(call_id, "gemini_generate_error", start_time, {
                "model": self.model_name, "error": True, "error_type": type(e).__name__
            })
            
            error_data = {
                'call_id': call_id,
                'error_type': type(e).__name__,
                'error_message': str(e),
                'duration_ms': duration_ms,
                'timestamp': datetime.now().isoformat()
            }
            _gemini_rcv_logger.error(f"ERROR - {call_id} - {json.dumps(error_data)}")
            
            logger.error(f"Error generating Gemini response: {e}")
            from .llm_errors import LLMError, LLMErrorType
            error = LLMError(
                message=str(e),
                error_type=LLMErrorType.API_ERROR,
                details={'model': self.model_name}
            )
            return BaseLLMResponse.error_response(error)
    
    def generate_with_tools(self, messages: List[Dict], available_tools: List[Dict], 
                           request_id: str = None, **kwargs) -> ToolCapableLLMResponse:
        """Generate response with access to tools."""
        from datetime import datetime
        import json
        
        start_time = datetime.now()
        # Use provided request_id for correlation, or generate call_id as fallback
        call_id = request_id if request_id else f"gemini_{start_time.strftime('%H%M%S')}_{id(self) % 1000}"
        
        # Check for cache if simple_cache is available
        if hasattr(self, 'simple_cache') and self.simple_cache is not None:
            from .cache import cache_llm_call
            
            def compute_gemini_response():
                """Function to compute Gemini response on cache miss."""
                return self._generate_with_tools_direct(messages, available_tools, call_id, start_time, request_id, **kwargs)
            
            try:
                return cache_llm_call(
                    cache=self.simple_cache,
                    provider='gemini',
                    model=self.model_name,
                    messages=messages,
                    tools=available_tools,
                    func_to_call=compute_gemini_response
                )
                
            except Exception as e:
                logger.warning(f"Cache operation failed, proceeding without cache: {e}")
                # Fall through to direct call
        
        # No cache or cache miss - make direct call
        return self._generate_with_tools_direct(messages, available_tools, call_id, start_time, request_id, **kwargs)
    
    def _generate_with_tools_direct(self, messages: List[Dict], available_tools: List[Dict], 
                                   call_id: str, start_time, request_id: str = None, **kwargs) -> ToolCapableLLMResponse:
        """Direct Gemini call without caching."""
        from datetime import datetime
        import json
        
        try:
            # Level 0007 logging: Log request
            _gemini_wrap_logger.info(f"REQUEST - {call_id} - {self.model_name} - {len(messages)} messages")
            
            # Level 0007 logging: Log send data
            send_data = {
                'call_id': call_id,
                'model': self.model_name,
                'message_count': len(messages),
                'messages': messages,
                'tools_count': len(available_tools),
                'tools': available_tools,  # ← Add full tools array for debugging
                'kwargs': kwargs,
                'timestamp': datetime.now().isoformat()
            }
            _gemini_send_logger.info(f"SEND - {call_id} - {json.dumps(send_data)}")
            
            # Convert messages to Gemini format
            gemini_messages = self._convert_messages_to_gemini(messages)
            
            # Convert tools to Gemini format
            gemini_tools = self._convert_tools_to_gemini(available_tools)
            
            # Generate with tools
            response = self.client.generate_content(
                contents=gemini_messages,
                tools=gemini_tools if gemini_tools else None,
                generation_config=self.genai.types.GenerationConfig(
                    temperature=kwargs.get('temperature', self.temperature),
                    max_output_tokens=kwargs.get('max_tokens', 2048)
                )
            )
            
            # Parse tool calls
            tool_calls = self._extract_tool_calls(response)
            
            # Debug logging for tool calls
            if tool_calls:
                logger.debug(f"🔧 Extracted {len(tool_calls)} tool calls:")
                for i, call in enumerate(tool_calls):
                    logger.debug(f"  [{i+1}] {call.name}({call.args}) [id: {call.id}]")
            
            # Extract text content - handle function calls properly
            text_content = self._safe_extract_text(response)
            
            # Level 0007 logging: Log success
            duration_ms = (datetime.now() - start_time).total_seconds() * 1000
            char_count = len(text_content) if text_content else 0
            _gemini_wrap_logger.info(f"SUCCESS - {call_id} - {char_count} chars - {duration_ms:.0f}ms")
            
            # Level 0004: Log timing for successful tool-capable generate
            log_existing_timing(call_id, "gemini_with_tools", start_time, {
                "model": self.model_name, "char_count": char_count, "tool_calls_count": len(tool_calls), 
                "success": True
            })
            
            # Level 0007 logging: Log receive data
            response_data = {
                'call_id': call_id,
                'content': text_content,
                'char_count': char_count,
                'tool_calls_count': len(tool_calls),
                'duration_ms': duration_ms,
                'timestamp': datetime.now().isoformat(),
                'usage': self._extract_usage(response)
            }
            _gemini_rcv_logger.info(f"RECEIVE - {call_id} - {json.dumps(response_data)}")
            
            return ToolCapableLLMResponse(
                content=text_content,
                tool_calls=tool_calls,
                finish_reason=response.candidates[0].finish_reason.name if response.candidates else "stop",
                usage=self._extract_usage(response)
            )
            
        except Exception as e:
            # Level 0007 logging: Log error
            duration_ms = (datetime.now() - start_time).total_seconds() * 1000
            _gemini_wrap_logger.error(f"ERROR - {call_id} - {self.model_name} - {type(e).__name__}: {str(e)} - {duration_ms:.0f}ms")
            
            # Level 0004: Log timing for tool-capable generate error
            log_existing_timing(call_id, "gemini_with_tools_error", start_time, {
                "model": self.model_name, "error": True, "error_type": type(e).__name__
            })
            
            error_data = {
                'call_id': call_id,
                'error_type': type(e).__name__,
                'error_message': str(e),
                'duration_ms': duration_ms,
                'timestamp': datetime.now().isoformat()
            }
            _gemini_rcv_logger.error(f"ERROR - {call_id} - {json.dumps(error_data)}")
            
            logger.error(f"Error generating Gemini response with tools: {e}")
            return ToolCapableLLMResponse(
                content=f"Error: {str(e)}",
                tool_calls=[],
                finish_reason="error",
                usage={}
            )
    
    def continue_with_tool_results(self, messages: List[Dict], tool_results: List[ToolCallResult],
                                  request_id: str = None, **kwargs) -> ToolCapableLLMResponse:
        """Continue conversation after tool execution."""
        from datetime import datetime
        import json
        
        start_time = datetime.now()
        # Use provided request_id for correlation, or generate call_id as fallback
        call_id = request_id if request_id else f"gemini_continue_{start_time.strftime('%H%M%S')}_{id(self) % 1000}"
        
        try:
            # Level 0007 logging: Log request
            _gemini_wrap_logger.info(f"CONTINUE - {call_id} - {self.model_name} - continuing with {len(tool_results)} tool results")
            
            # Level 0007 logging: Log send data
            send_data = {
                'call_id': call_id,
                'model': self.model_name,
                'message_count': len(messages),
                'tool_results_count': len(tool_results),
                'tool_results': [{'name': tr.name, 'success': tr.success, 'content': tr.content[:200] if tr.content else '', 'error': tr.error} for tr in tool_results],  # ← Add tool results with content truncated for logging
                'kwargs': kwargs,
                'timestamp': datetime.now().isoformat()
            }
            _gemini_send_logger.info(f"CONTINUE - {call_id} - {json.dumps(send_data)}")
            
            # Convert messages (which should include tool calls and results) to Gemini format
            gemini_messages = self._convert_messages_with_tools_to_gemini(messages, tool_results)
            
            # Generate continuation
            response = self.client.generate_content(
                contents=gemini_messages,
                generation_config=self.genai.types.GenerationConfig(
                    temperature=kwargs.get('temperature', self.temperature),
                    max_output_tokens=kwargs.get('max_tokens', 2048)
                )
            )
            
            # Level 0007 logging: Log success
            content = self._safe_extract_text(response)
            duration_ms = (datetime.now() - start_time).total_seconds() * 1000
            char_count = len(content) if content else 0
            _gemini_wrap_logger.info(f"CONTINUE_SUCCESS - {call_id} - {char_count} chars - {duration_ms:.0f}ms")
            
            # Level 0004: Log timing for successful continue
            log_existing_timing(call_id, "gemini_continue", start_time, {
                "model": self.model_name, "char_count": char_count, "success": True, "continuation": True
            })
            
            # Level 0007 logging: Log receive data
            response_data = {
                'call_id': call_id,
                'content': content,
                'char_count': char_count,
                'duration_ms': duration_ms,
                'timestamp': datetime.now().isoformat(),
                'usage': self._extract_usage(response)
            }
            _gemini_rcv_logger.info(f"CONTINUE - {call_id} - {json.dumps(response_data)}")
            
            return ToolCapableLLMResponse(
                content=content,
                tool_calls=[],  # Final response, no more tool calls
                finish_reason=response.candidates[0].finish_reason.name if response.candidates else "stop",
                usage=self._extract_usage(response)
            )
            
        except Exception as e:
            # Level 0007 logging: Log error
            duration_ms = (datetime.now() - start_time).total_seconds() * 1000
            _gemini_wrap_logger.error(f"CONTINUE_ERROR - {call_id} - {self.model_name} - {type(e).__name__}: {str(e)} - {duration_ms:.0f}ms")
            
            # Level 0004: Log timing for continue error
            log_existing_timing(call_id, "gemini_continue_error", start_time, {
                "model": self.model_name, "error": True, "error_type": type(e).__name__, "continuation": True
            })
            
            error_data = {
                'call_id': call_id,
                'error_type': type(e).__name__,
                'error_message': str(e),
                'duration_ms': duration_ms,
                'timestamp': datetime.now().isoformat()
            }
            _gemini_rcv_logger.error(f"CONTINUE_ERROR - {call_id} - {json.dumps(error_data)}")
            
            logger.error(f"Error continuing Gemini conversation: {e}")
            return ToolCapableLLMResponse(
                content=f"Error continuing conversation: {str(e)}",
                tool_calls=[],
                finish_reason="error",
                usage={}
            )
    
    def convert_tools_schema(self, mcp_tools: List[Dict]) -> List[Dict]:
        """Convert MCP tools to Gemini function declarations."""
        return self._convert_tools_to_gemini(mcp_tools)
    
    def _convert_messages_to_gemini(self, messages: List[Dict]) -> List[Dict]:
        """Convert standard messages to Gemini format."""
        gemini_messages = []
        
        for msg in messages:
            role = msg.get("role", "user")
            content = msg.get("content", "")
            
            # Convert roles
            if role == "assistant":
                gemini_role = "model"
            elif role == "system":
                # Gemini doesn't have system role - prepend to first user message
                if not gemini_messages:
                    gemini_messages.append({
                        "role": "user",
                        "parts": [{"text": f"System: {content}"}]
                    })
                continue
            else:
                gemini_role = "user"
            
            gemini_messages.append({
                "role": gemini_role,
                "parts": [{"text": content}]
            })
        
        return gemini_messages
    
    def _convert_messages_with_tools_to_gemini(self, messages: List[Dict], tool_results: List[ToolCallResult]) -> List[Dict]:
        """Convert messages with tool calls and results to Gemini format."""
        gemini_messages = []
        
        # Process messages, properly handling tool calls and results
        for msg in messages:
            role = msg.get("role", "user")
            content = msg.get("content", "")
            
            if role == "assistant":
                gemini_role = "model"
                # Keep it simple - just pass text content
                gemini_messages.append({
                    "role": gemini_role,
                    "parts": [{"text": content or "I'll help you with that."}]
                })
            elif role == "system":
                continue  # Skip system messages for now
            else:
                gemini_messages.append({
                    "role": "user",
                    "parts": [{"text": content}]
                })
        
        # Add tool results as user message with clear text
        if tool_results:
            result_texts = []
            logger.debug(f"📥 Processing {len(tool_results)} tool results:")
            for tool_result in tool_results:
                if tool_result.success:
                    result_texts.append(f"🔧 Tool {tool_result.name} result: {tool_result.content}")
                    logger.debug(f"  ✅ {tool_result.name} -> {tool_result.content}")
                else:
                    result_texts.append(f"❌ Tool {tool_result.name} error: {tool_result.error}")
                    logger.debug(f"  ❌ {tool_result.name} -> {tool_result.error}")
            
            if result_texts:
                formatted_message = "\n".join(result_texts)
                logger.debug(f"📤 Sending tool results to Gemini: {formatted_message}")
                gemini_messages.append({
                    "role": "user",
                    "parts": [{"text": formatted_message}]
                })
        
        return gemini_messages
    
    def _convert_tools_to_gemini(self, tools: List[Dict]) -> List[Dict]:
        """Convert universal tool format to Gemini function declarations."""
        if not tools:
            return []
        
        gemini_tools = []
        
        for tool in tools:
            function_declaration = {
                "name": tool.get("name", "unknown_tool"),
                "description": tool.get("description", ""),
                "parameters": self._convert_parameters_to_gemini(tool.get("parameters", {}))
            }
            
            gemini_tools.append({
                "function_declarations": [function_declaration]
            })
        return gemini_tools
    
    def _convert_parameters_to_gemini(self, parameters: Dict) -> Dict:
        """Convert JSON schema parameters to Gemini format."""
        # Gemini uses a similar format to JSON schema
        # but may need some adjustments
        
        gemini_params = {
            "type": "object",
            "properties": {},
            "required": parameters.get("required", [])
        }
        
        properties = parameters.get("properties", {})
        for param_name, param_schema in properties.items():
            gemini_params["properties"][param_name] = {
                "type": param_schema.get("type", "string"),
                "description": param_schema.get("description", "")
            }
        
        return gemini_params
    
    def _extract_tool_calls(self, response) -> List[ToolCall]:
        """Extract tool calls from Gemini response."""
        tool_calls = []
        
        if not response.candidates:
            logger.debug("🔍 No candidates in Gemini response")
            return tool_calls
        
        candidate = response.candidates[0]
        logger.debug(f"🔍 Processing candidate with {len(candidate.content.parts)} parts")
        
        for i, part in enumerate(candidate.content.parts):
            logger.debug(f"🔍 Part {i}: has_function_call={hasattr(part, 'function_call')}")
            
            if hasattr(part, 'function_call'):
                function_call = part.function_call
                logger.debug(f"🔍 Function call found: {function_call}")
                logger.debug(f"🔍 Function name: '{function_call.name}'")
                logger.debug(f"🔍 Function args: {function_call.args}")
                
                # Convert function call arguments to dict
                args = {}
                if hasattr(function_call, 'args') and function_call.args:
                    args = dict(function_call.args)
                    logger.debug(f"🔍 Converted args: {args}")
                else:
                    logger.debug("🔍 No args found or args is empty")
                
                tool_calls.append(ToolCall(
                    id=f"gemini_{len(tool_calls)}",  # Gemini doesn't provide IDs
                    name=function_call.name,
                    args=args
                ))
        
        return tool_calls
    
    def _extract_usage(self, response) -> Dict[str, int]:
        """Extract token usage from Gemini response."""
        if hasattr(response, 'usage_metadata') and response.usage_metadata:
            usage = response.usage_metadata
            return {
                "prompt_tokens": getattr(usage, 'prompt_token_count', 0),
                "completion_tokens": getattr(usage, 'candidates_token_count', 0),
                "total_tokens": getattr(usage, 'total_token_count', 0)
            }
        return {}
    
    def _safe_extract_text(self, response) -> Optional[str]:
        """Safely extract text content from Gemini response, handling function calls."""
        try:
            if response.text:
                return response.text
        except ValueError as e:
            # This happens when response contains function calls
            # Extract text from non-function-call parts manually
            if response.candidates and response.candidates[0].content.parts:
                text_parts = []
                for part in response.candidates[0].content.parts:
                    if hasattr(part, 'text') and part.text:
                        text_parts.append(part.text)
                if text_parts:
                    return '\n'.join(text_parts)
            logger.debug(f"Response contains non-text parts (likely function calls): {e}")
        return None