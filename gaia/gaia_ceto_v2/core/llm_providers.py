"""
LLM Provider implementations - Clean abstractions for different AI models.

This module provides a unified interface for different LLM providers
without any dependencies on conversation management or storage.
"""

import os
import logging
import re
from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional, Callable, TYPE_CHECKING

from gaia_ceto_v2.tools.tools import ToolRegistry
from gaia_ceto_v2.core.llm_wrapper import LiteLLMWrapper
from gaia_ceto_v2.core.llm_errors import LLMResponse

logger = logging.getLogger(__name__)


class LLMProvider(ABC):
    """Abstract base class for LLM providers.
    
    Defines the interface that all LLM implementations must follow.
    This is simpler than the original Protocol-based approach.
    """
    
    def __init__(self, tool_registry: Optional[ToolRegistry] = None):
        """Initialize LLM provider.
        
        Args:
            tool_registry: Optional Ceto tool registry for tool calling
        """
        self.tool_registry = tool_registry
    
    @abstractmethod
    def generate_response(self, prompt: str, context: List[Dict[str, Any]], request_id: str = None, **kwargs) -> LLMResponse:
        """Generate a response to the given prompt with conversation context.
        
        Args:
            prompt: The user's input text
            context: List of previous messages for context
            **kwargs: Provider-specific parameters
            
        Returns:
            LLMResponse object with structured success/failure information
        """
        pass
    
    @abstractmethod
    def get_model_name(self) -> str:
        """Get the name/identifier of the model being used."""
        pass
    
    def get_provider_name(self) -> str:
        """Get the name of the provider (e.g., 'openai', 'anthropic')."""
        return self.__class__.__name__.lower().replace('llm', '')
    
    def set_tool_registry(self, tool_registry: ToolRegistry) -> None:
        """Set the tool registry for this provider.
        
        Args:
            tool_registry: Ceto tool registry to use for tool calls
        """
        self.tool_registry = tool_registry


class MockLLM(LLMProvider):
    """Mock LLM implementation for testing and development.
    
    Simplified from the original - supports tool calling for testing.
    """
    
    def __init__(self, 
                 response_func: Optional[Callable[[str, List[Dict[str, Any]], Dict[str, Any]], str]] = None,
                 tool_registry: Optional[ToolRegistry] = None):
        """Initialize the mock LLM.
        
        Args:
            response_func: Optional custom function to generate responses.
                          If not provided, uses a simple echo function.
            tool_registry: Optional tool registry for tool calling
        """
        super().__init__(tool_registry)
        self._response_func = response_func or self._default_response
    
    def generate_response(self, prompt: str, context: List[Dict[str, Any]], request_id: str = None, **kwargs) -> LLMResponse:
        """Generate a mock response, with tool calling support."""
        
        try:
            # Check for direct tool calls (like "echostring hello world")
            if self.tool_registry:
                tool_result = self._try_direct_tool_call(prompt)
                if tool_result:
                    return LLMResponse.success_response(
                        content=tool_result,
                        metadata={'tool_call': True, 'mock': True}
                    )
            
            # Fall back to regular response
            content = self._response_func(prompt, context, kwargs)
            return LLMResponse.success_response(
                content=content,
                metadata={'mock': True}
            )
            
        except Exception as e:
            from .llm_errors import LLMContentError, LLMErrorType
            error = LLMContentError(
                message=f"Mock LLM error: {str(e)}",
                error_type=LLMErrorType.UNKNOWN,
                details={'prompt': prompt[:100]}  # Truncate for logging
            )
            return LLMResponse.error_response(error)
    
    def get_model_name(self) -> str:
        return "mock-v1"
    
    def _try_direct_tool_call(self, prompt: str) -> Optional[str]:
        """Try to execute prompt as a direct tool call.
        
        Args:
            prompt: User prompt that might be a tool call
            
        Returns:
            Tool result if it was a tool call, None otherwise
        """
        if not self.tool_registry:
            return None
        
        # Simple pattern matching for direct tool calls
        # Format: "toolname argument" or "toolname(argument)"
        prompt = prompt.strip()
        
        # Try pattern: "echostring hello world"
        parts = prompt.split(None, 1)
        if len(parts) >= 1:
            tool_name = parts[0]
            if self.tool_registry.has_tool(tool_name):
                try:
                    if len(parts) > 1:
                        # Extract argument
                        arg = parts[1]
                        # Remove quotes if present
                        if (arg.startswith('"') and arg.endswith('"')) or (arg.startswith("'") and arg.endswith("'")):
                            arg = arg[1:-1]
                        
                        # For echostring tool specifically
                        if tool_name == "echostring":
                            result = self.tool_registry.execute_tool(tool_name, statement=arg)
                            return f"Tool '{tool_name}' executed: {result}"
                        else:
                            # Generic single-parameter tool call
                            result = self.tool_registry.execute_tool(tool_name, input=arg)
                            return f"Tool '{tool_name}' executed: {result}"
                    else:
                        # No-parameter tool call
                        result = self.tool_registry.execute_tool(tool_name)
                        return f"Tool '{tool_name}' executed: {result}"
                        
                except Exception as e:
                    return f"Tool '{tool_name}' failed: {str(e)}"
        
        return None
    
    @staticmethod
    def _default_response(prompt: str, context: List[Dict[str, Any]], kwargs: Dict[str, Any]) -> str:
        """Default response that echoes the input with some context info."""
        context_length = len(context)
        return f"Mock LLM response to: '{prompt}' (context: {context_length} messages)"


class LiteLLM(LLMProvider):
    """
    LLM provider that uses the LiteLLM wrapper.
    """

    def __init__(self,
                 model_name: str,
                 tool_registry: Optional[ToolRegistry] = None,
                 **kwargs):
        """
        Initializes the LiteLLM provider.

        Args:
            model_name: The name of the model to use (e.g., 'gpt-3.5-turbo').
            tool_registry: Optional Ceto tool registry for tool calling.
            **kwargs: Additional arguments for the LiteLLMWrapper.
        """
        super().__init__(tool_registry)
        self.model_name = model_name
        self.wrapper = LiteLLMWrapper(**kwargs)

    def generate_response(self,
                          prompt: str,
                          context: List[Dict[str, Any]],
                          request_id: str = None,
                          **kwargs) -> LLMResponse:
        """
        Generates a response using the LiteLLM wrapper.

        Args:
            prompt: The user's input text.
            context: List of previous messages for context.
            **kwargs: Additional arguments to pass to the wrapper.

        Returns:
            LLMResponse object with structured success/failure information.
        """
        messages = context + [{"role": "user", "content": prompt}]
        return self.wrapper.completion(model=self.model_name,
                                       messages=messages,
                                       request_id=request_id,
                                       **kwargs)

    def get_model_name(self) -> str:
        return self.model_name


class OpenAILLM(LiteLLM):
    """OpenAI LLM provider implementation."""

    def __init__(self,
                 model_name: str = "gpt-4o",
                 tool_registry: Optional[ToolRegistry] = None,
                 **kwargs):
        """
        Initializes the OpenAI LLM provider.

        Args:
            model_name: The OpenAI model to use.
            tool_registry: Optional Ceto tool registry for tool calling.
            **kwargs: Additional arguments for the LiteLLMWrapper.
        """
        super().__init__(model_name=f"openai/{model_name}", tool_registry=tool_registry, **kwargs)


class AnthropicLLM(LiteLLM):
    """Anthropic LLM provider implementation."""

    def __init__(self,
                 model_name: str = "claude-3-5-sonnet-20240620",
                 tool_registry: Optional[ToolRegistry] = None,
                 **kwargs):
        """
        Initializes the Anthropic LLM provider.

        Args:
            model_name: The Anthropic model to use.
            tool_registry: Optional Ceto tool registry for tool calling.
            **kwargs: Additional arguments for the LiteLLMWrapper.
        """
        super().__init__(model_name=f"anthropic/{model_name}", tool_registry=tool_registry, **kwargs)




# Factory function for easy provider creation
def create_llm_provider(provider_type: str, accounting_system=None, **kwargs) -> LLMProvider:
    """Factory function to create LLM providers.
    
    Args:
        provider_type: Type of provider ('mock', 'openai', 'anthropic', 'gemini', 'litellm')
        accounting_system: Optional AccountingSystem for Level 0008a cost tracking
        **kwargs: Parameters to pass to the provider constructor
        
    Returns:
        LLMProvider instance
        
    Raises:
        ValueError: If provider_type is not supported
    """
    provider_type = provider_type.lower()
    
    # Pass accounting_system to providers that use LiteLLMWrapper
    if accounting_system and provider_type in ['openai', 'anthropic', 'gemini', 'litellm']:
        kwargs['accounting_system'] = accounting_system
    
    if provider_type == 'mock':
        return MockLLM(**kwargs)
    elif provider_type == 'openai':
        return OpenAILLM(**kwargs)
    elif provider_type == 'anthropic':
        return AnthropicLLM(**kwargs)
    elif provider_type == 'gemini':
        # Use generic LiteLLM with Gemini model name
        model_name = kwargs.pop('model_name', 'gemini-2.5-flash-lite')
        return LiteLLM(model_name=f"gemini/{model_name}", **kwargs)
    elif provider_type == 'litellm':
        return LiteLLM(**kwargs)
    else:
        raise ValueError(f"Unsupported provider type: {provider_type}. "
                        f"Supported types: mock, openai, anthropic, gemini, litellm")


# Utility functions
def get_available_providers() -> List[str]:
    """Get list of available provider types."""
    return ['mock', 'openai', 'anthropic', 'litellm']


def provider_requires_api_key(provider_type: str) -> bool:
    """Check if a provider type requires an API key."""
    return provider_type.lower() in ['openai', 'anthropic', 'litellm']
