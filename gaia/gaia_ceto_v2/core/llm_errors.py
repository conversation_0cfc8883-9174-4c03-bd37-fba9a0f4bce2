"""
LLM Error Classification System

Provides structured error handling for LLM operations with clear error types
and recovery strategies. Addresses "Why 4" - missing error classification.
"""

from abc import ABC
from typing import Optional, Dict, Any
from enum import Enum


class ErrorSeverity(Enum):
    """Error severity levels for different handling strategies."""
    FATAL = "fatal"           # System misconfiguration, stop immediately  
    RECOVERABLE = "recoverable"  # Transient issues, retry possible
    WARNING = "warning"       # Degraded operation, continue with fallback


class LLMErrorType(Enum):
    """Specific LLM error classifications."""
    MISSING_API_KEY = "missing_api_key"
    INVALID_API_KEY = "invalid_api_key" 
    RATE_LIMITED = "rate_limited"
    MODEL_NOT_FOUND = "model_not_found"
    NETWORK_ERROR = "network_error"
    QUOTA_EXCEEDED = "quota_exceeded"
    CONTENT_FILTERED = "content_filtered"
    UNKNOWN = "unknown"


class LLMError(Exception, ABC):
    """Base class for all LLM-related errors."""
    
    def __init__(self, 
                 message: str,
                 error_type: LLMErrorType,
                 severity: ErrorSeverity,
                 retry_after: Optional[int] = None,
                 details: Optional[Dict[str, Any]] = None):
        super().__init__(message)
        self.message = message
        self.error_type = error_type
        self.severity = severity
        self.retry_after = retry_after
        self.details = details or {}
    
    def is_retryable(self) -> bool:
        """Check if this error allows for retry."""
        return self.severity == ErrorSeverity.RECOVERABLE
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert error to dictionary for logging/serialization."""
        return {
            'message': self.message,
            'error_type': self.error_type.value,
            'severity': self.severity.value,
            'retry_after': self.retry_after,
            'details': self.details
        }


class LLMConfigurationError(LLMError):
    """Fatal configuration errors - missing/invalid API keys, etc."""
    
    def __init__(self, message: str, error_type: LLMErrorType, details: Optional[Dict] = None):
        super().__init__(
            message=message,
            error_type=error_type, 
            severity=ErrorSeverity.FATAL,
            details=details
        )


class LLMTransientError(LLMError):
    """Recoverable transient errors - rate limits, network issues, etc."""
    
    def __init__(self, message: str, error_type: LLMErrorType, 
                 retry_after: Optional[int] = None, details: Optional[Dict] = None):
        super().__init__(
            message=message,
            error_type=error_type,
            severity=ErrorSeverity.RECOVERABLE,
            retry_after=retry_after,
            details=details
        )


class LLMContentError(LLMError):
    """Content-related errors - filtering, processing issues, etc."""
    
    def __init__(self, message: str, error_type: LLMErrorType, details: Optional[Dict] = None):
        super().__init__(
            message=message,
            error_type=error_type,
            severity=ErrorSeverity.WARNING,
            details=details
        )


class LLMResponse:
    """Structured response object that explicitly tracks success/failure state."""
    
    def __init__(self, 
                 content: Optional[str] = None,
                 success: bool = False,
                 error: Optional[LLMError] = None,
                 metadata: Optional[Dict[str, Any]] = None):
        self.content = content
        self.success = success
        self.error = error
        self.metadata = metadata or {}
    
    @classmethod
    def success_response(cls, content: str, metadata: Optional[Dict] = None) -> 'LLMResponse':
        """Create a successful response."""
        return cls(content=content, success=True, metadata=metadata)
    
    @classmethod  
    def error_response(cls, error: LLMError, metadata: Optional[Dict] = None) -> 'LLMResponse':
        """Create an error response."""
        return cls(success=False, error=error, metadata=metadata)
    
    def __str__(self) -> str:
        if self.success:
            return self.content or ""
        else:
            return f"LLM_ERROR({self.error.error_type.value}): {self.error.message}"
    
    def __bool__(self) -> bool:
        """Allow boolean checks: if response: ..."""
        return self.success and bool(self.content)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        result = {
            'success': self.success,
            'content': self.content,
            'metadata': self.metadata
        }
        if self.error:
            result['error'] = self.error.to_dict()
        return result


def classify_litellm_error(exception: Exception) -> LLMError:
    """Classify a raw exception from litellm into structured LLMError."""
    error_msg = str(exception).lower()
    
    # API Key issues
    if any(phrase in error_msg for phrase in ['api key', 'authentication', 'unauthorized']):
        if 'not provided' in error_msg or 'missing' in error_msg:
            return LLMConfigurationError(
                message="API key is missing",
                error_type=LLMErrorType.MISSING_API_KEY,
                details={'original_error': str(exception)}
            )
        else:
            return LLMConfigurationError(
                message="API key is invalid",
                error_type=LLMErrorType.INVALID_API_KEY,
                details={'original_error': str(exception)}
            )
    
    # Rate limiting
    if any(phrase in error_msg for phrase in ['rate limit', 'too many requests', '429']):
        retry_after = None
        # Try to extract retry-after from error message
        import re
        match = re.search(r'retry.after.(\d+)', error_msg)
        if match:
            retry_after = int(match.group(1))
        
        return LLMTransientError(
            message="Rate limit exceeded",
            error_type=LLMErrorType.RATE_LIMITED,
            retry_after=retry_after,
            details={'original_error': str(exception)}
        )
    
    # Model issues
    if any(phrase in error_msg for phrase in ['model not found', 'invalid model', 'model does not exist']):
        return LLMConfigurationError(
            message="Model not found or invalid",
            error_type=LLMErrorType.MODEL_NOT_FOUND,
            details={'original_error': str(exception)}
        )
    
    # Network issues
    if any(phrase in error_msg for phrase in ['connection', 'timeout', 'network', 'unreachable']):
        return LLMTransientError(
            message="Network connectivity issue",
            error_type=LLMErrorType.NETWORK_ERROR,
            retry_after=30,  # Default retry after 30 seconds
            details={'original_error': str(exception)}
        )
    
    # Quota issues
    if any(phrase in error_msg for phrase in ['quota', 'billing', 'payment']):
        return LLMConfigurationError(
            message="API quota exceeded or billing issue",
            error_type=LLMErrorType.QUOTA_EXCEEDED,
            details={'original_error': str(exception)}
        )
    
    # Content filtering
    if any(phrase in error_msg for phrase in ['content policy', 'filtered', 'safety']):
        return LLMContentError(
            message="Content filtered by safety policies",
            error_type=LLMErrorType.CONTENT_FILTERED,
            details={'original_error': str(exception)}
        )
    
    # Default: unknown error
    return LLMTransientError(
        message=f"Unknown LLM error: {str(exception)}",
        error_type=LLMErrorType.UNKNOWN,
        retry_after=60,
        details={'original_error': str(exception)}
    )