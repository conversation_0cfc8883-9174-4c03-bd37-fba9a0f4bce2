
"""
ChatManager - Simplified orchestrator using focused services.

Thin facade that coordinates specialized services.
No longer a god object - delegates all work to focused components.
"""

import logging
import uuid
from datetime import datetime
from typing import Dict, List, Any, Optional, Type, Callable

from .conversation_service import ConversationService
from .message_service import MessageService
from .system_prompt_service import System<PERSON>romptService
from .chat_statistics import ChatStatistics
from .conversation_serializer import ConversationSerializer
from .llm_providers import LLMProvider
from .storage_interface import ConversationStorage
from .conversation_cache import ConversationCache
from .tool_calling_interface import ToolCapableLLM, ToolCallResult, ToolCapableLLMResponse
try:
    from ..tools.tools import ToolRegistry, default_registry
except ImportError:
    import sys
    sys.path.insert(0, '../tools')
    from tools import ToolRegistry, default_registry
from .level_0004_simple import log_existing_timing
from .tool_response import handle_tool_response, get_document

logger = logging.getLogger(__name__)


def _register_mcp_tools(registry: ToolRegistry, mcp_handler) -> None:
    """Register MCP tools in the tool registry using the adapter."""
    from .tool_adapter import register_mcp_tools_in_registry
    register_mcp_tools_in_registry(registry, mcp_handler)


def optional_import(module_path: str, class_name: str, fallback=None):
    """Simple optional import with fallback."""
    try:
        from importlib import import_module
        if module_path.startswith('.'):
            module = import_module(module_path, package=__package__)
        else:
            module = import_module(module_path)
        return getattr(module, class_name)
    except (ImportError, AttributeError) as e:
        logger.debug(f"Optional import failed for {module_path}.{class_name}: {e}")
        return fallback


class ChatManager:
    """Simplified ChatManager - thin orchestration facade.
    
    Coordinates focused services instead of doing everything.
    Each service handles one concern well.
    """
    
    def __init__(self,
                 repository: ConversationStorage,
                 llm_provider: LLMProvider,
                 cache: ConversationCache,
                 statistics: ChatStatistics,
                 tool_registry: ToolRegistry = None,
                 max_context_messages: int = 20,
                 system_prompts_file: str = "prompt_sys.json"):
        """Initialize ChatManager with focused services."""
        
        self.conversation_service = ConversationService(repository, cache)
        self.message_service = MessageService(llm_provider, max_context_messages)
        self.prompt_service = SystemPromptService(system_prompts_file)
        self.statistics = statistics
        self.tool_registry = tool_registry or default_registry
        
        logger.info(f"ChatManager initialized with service composition")
    
    def create_conversation(self, 
                          user_id: str,
                          title: str = None,
                          system_prompt: str = None,
                          **metadata) -> str:
        """Create a new conversation."""
        try:
            conversation_id = self.conversation_service.create_conversation(
                user_id, title, **metadata
            )
            
            if system_prompt:
                conversation = self.conversation_service.get_conversation(conversation_id)
                system_text = self.prompt_service.get_system_prompt_text(system_prompt)
                if system_text:
                    conversation.add_message('system', system_text)
                    self.conversation_service.save_conversation(conversation)
            
            self.statistics.record_conversation_created(conversation_id, user_id)
            return conversation_id
            
        except Exception as e:
            logger.error(f"Error creating conversation for user {user_id}: {e}")
            self.statistics.record_error("conversation_creation", str(e))
            raise
    
    def send_message(self, 
                    conversation_id: str,
                    message: str,
                    **llm_kwargs) -> str:
        """Send a message to a conversation and get LLM response."""
        start_time = datetime.now()
        
        # Generate unique request_id for this conversation turn
        request_id = str(uuid.uuid4())
        logger.debug(f"Generated request_id {request_id} for conversation {conversation_id}")
        
        try:
            conversation = self.conversation_service.get_conversation(conversation_id)
            if not conversation:
                raise ValueError(f"Conversation {conversation_id} not found")
            
            self.statistics.record_message(
                conversation_id, 'user', getattr(conversation, 'user_id', None)
            )
            
            # Check if LLM supports native tool calling
            if isinstance(self.message_service.llm_provider, ToolCapableLLM) and self.message_service.llm_provider.supports_tools():
                response = self._handle_tool_capable_llm(conversation, message, request_id, **llm_kwargs)
            else:
                # Execute tools if detected (legacy pattern matching)
                enhanced_message = self._process_tools(message, request_id)
                response = self.message_service.send_message(conversation, enhanced_message, request_id, **llm_kwargs)
            
            response_time_ms = (datetime.now() - start_time).total_seconds() * 1000
            self.statistics.record_message(
                conversation_id, 'assistant', 
                getattr(conversation, 'user_id', None), response_time_ms
            )
            
            # Level 0004: Log timing for chat message
            log_existing_timing(request_id, "chat_send_message", start_time, {
                "conversation_id": conversation_id, "message_length": len(message),
                "response_length": len(response), "success": True
            })
            
            if not self.conversation_service.save_conversation(conversation):
                logger.warning(f"Failed to save conversation {conversation_id} after message")
                self.statistics.record_error("conversation_save", "Failed to save after message")
            
            return response
            
        except Exception as e:
            logger.error(f"Error processing message in conversation {conversation_id}: {e}")
            self.statistics.record_error("message_processing", str(e))
            raise
    
    def get_conversation(self, conversation_id: str) -> Optional[Dict[str, Any]]:
        """Get a conversation by ID."""
        try:
            conversation = self.conversation_service.get_conversation(conversation_id)
            if conversation:
                return ConversationSerializer.to_dict(conversation)
            return None
        except Exception as e:
            logger.error(f"Error getting conversation {conversation_id}: {e}")
            self.statistics.record_error("conversation_retrieval", str(e))
            return None
    
    def list_conversations(self, user_id: str = None) -> List[Dict[str, Any]]:
        """List conversations, optionally filtered by user."""
        try:
            return self.conversation_service.list_conversations(user_id)
        except Exception as e:
            logger.error(f"Error listing conversations: {e}")
            self.statistics.record_error("conversation_listing", str(e))
            return []
    
    def delete_conversation(self, conversation_id: str) -> bool:
        """Delete a conversation."""
        try:
            return self.conversation_service.delete_conversation(conversation_id)
        except Exception as e:
            logger.error(f"Error deleting conversation {conversation_id}: {e}")
            self.statistics.record_error("conversation_deletion", str(e))
            return False
    
    def update_conversation_title(self, conversation_id: str, new_title: str) -> bool:
        """Update the title of a conversation."""
        try:
            return self.conversation_service.update_conversation_title(conversation_id, new_title)
        except Exception as e:
            logger.error(f"Error updating conversation title {conversation_id}: {e}")
            self.statistics.record_error("conversation_update", str(e))
            return False
    
    def get_conversation_stats(self, conversation_id: str) -> Optional[Dict[str, Any]]:
        """Get statistics about a conversation."""
        try:
            conversation = self.conversation_service.get_conversation(conversation_id)
            if not conversation:
                return None
            return self.statistics.get_conversation_stats(conversation)
        except Exception as e:
            logger.error(f"Error getting conversation stats {conversation_id}: {e}")
            self.statistics.record_error("stats_retrieval", str(e))
            return None
    
    def get_system_stats(self) -> Dict[str, Any]:
        """Get overall system statistics."""
        try:
            stats = self.statistics.get_system_stats()
            stats['cache'] = self.conversation_service.cache.get_stats()
            stats['llm_provider'] = type(self.message_service.llm_provider).__name__
            stats['llm_model'] = self.message_service.get_model_name()
            return stats
        except Exception as e:
            logger.error(f"Error getting system stats: {e}")
            self.statistics.record_error("system_stats", str(e))
            return {'error': str(e)}
    
    def set_llm_provider(self, llm_provider: LLMProvider) -> None:
        """Change the LLM provider."""
        self.message_service.set_llm_provider(llm_provider)
    
    def clear_conversation_history(self, conversation_id: str, keep_system_messages: bool = True) -> bool:
        """Clear all messages from a conversation."""
        try:
            return self.conversation_service.clear_conversation_history(conversation_id, keep_system_messages)
        except Exception as e:
            logger.error(f"Error clearing conversation history {conversation_id}: {e}")
            self.statistics.record_error("conversation_clear", str(e))
            return False
    
    def get_available_system_prompts(self) -> Dict[str, str]:
        """Get available system prompt keys and names."""
        return self.prompt_service.get_available_system_prompts()
    
    def get_available_tools(self) -> List[str]:
        """Get available tools from tool registry."""
        return self.tool_registry.list_tools()
    
    def _process_tools(self, message: str, request_id: str) -> str:
        """Legacy tool processing - deprecated, kept for backward compatibility."""
        logger.warning("Legacy tool processing used - consider upgrading to tool-capable LLM")
        return message  # No legacy pattern matching, encourage native tool calling
    
    def _handle_tool_capable_llm(self, conversation, message: str, request_id: str, **llm_kwargs) -> str:
        """Handle LLM with native tool calling support."""
        try:
            # Add user message to conversation
            conversation.add_message('user', message)
            
            # Get available tools from registry
            available_tools = self.tool_registry.get_schemas_for_llm()
            
            # Get conversation messages for context
            messages = [{'role': msg.role, 'content': msg.content} for msg in conversation.messages]
            
            # Generate with tools
            llm_response = self.message_service.llm_provider.generate_with_tools(
                messages=messages,
                available_tools=available_tools,
                request_id=request_id,
                **llm_kwargs
            )

            # If response from cache is a dict, convert it to ToolCapableLLMResponse
            if isinstance(llm_response, dict):
                from .tool_calling_interface import ToolCapableLLMResponse, ToolCall
                
                # Reconstruct ToolCall objects if they exist
                tool_calls = llm_response.get('tool_calls')
                if tool_calls and isinstance(tool_calls, list):
                    reconstructed_calls = []
                    for call_data in tool_calls:
                        if isinstance(call_data, dict):
                            reconstructed_calls.append(ToolCall(**call_data))
                        else:
                            reconstructed_calls.append(call_data) # Keep as is if not a dict
                    llm_response['tool_calls'] = reconstructed_calls

                llm_response = ToolCapableLLMResponse(**llm_response)
            
            # Handle tool calls if present
            if llm_response.has_tool_calls:
                tool_results = []
                
                for tool_call in llm_response.tool_calls:
                    # Simple fix for empty research calls
                    if tool_call.name == 'exa_research' and not tool_call.args.get('query'):
                        logger.info(f"exa_research called with no query, using user message: {message}")
                        tool_call.args['query'] = message

                    try:
                        # Execute tool directly via registry, passing request_id for MCP logging
                        raw_result = self.tool_registry.execute_tool(tool_call.name, request_id=request_id, **tool_call.args)
                        
                        # Level 0035: Handle structured tool responses
                        processed = handle_tool_response(tool_call.name, raw_result)
                        
                        # Add to context if tool wants it
                        if processed['context_content']:
                            # Simple context addition - could be enhanced with conversation.add_context()
                            logger.debug(f"Tool {tool_call.name} added to context: {len(processed['context_content'])} chars")
                        
                        # Use processed content for tool result
                        content = processed['content'] or processed['context_content'] or str(raw_result)
                        
                        tool_results.append(ToolCallResult(
                            tool_call_id=tool_call.id,
                            name=tool_call.name,
                            content=content
                        ))
                    except Exception as e:
                        tool_results.append(ToolCallResult(
                            tool_call_id=tool_call.id,
                            name=tool_call.name,
                            content="",
                            error=str(e)
                        ))
                
                # Continue conversation with tool results
                final_response = self.message_service.llm_provider.continue_with_tool_results(
                    messages=messages,
                    tool_results=tool_results,
                    request_id=request_id,
                    **llm_kwargs
                )
                
                response_content = final_response.content or "Tool execution completed."
            else:
                response_content = llm_response.content or "No response generated."
            
            # Add assistant response to conversation
            conversation.add_message('assistant', response_content)
            
            return response_content
            
        except Exception as e:
            logger.error(f"Error in tool-capable LLM handling: {e}")
            # Fallback to legacy processing
            enhanced_message = self._process_tools(message, request_id)
            return self.message_service.send_message(conversation, enhanced_message, **llm_kwargs)
    
    def get_conversation_system_prompt(self, conversation_id: str) -> Optional[str]:
        """Get the current system prompt for a conversation."""
        try:
            conversation = self.conversation_service.get_conversation(conversation_id)
            if not conversation:
                return None
            
            # Find the system message
            for message in conversation.messages:
                if message.role == 'system':
                    return message.content
            
            return None
        except Exception as e:
            logger.error(f"Error getting system prompt for conversation {conversation_id}: {e}")
            return None
    
    def _convert_to_tool_call_result(self, tool_call, result) -> ToolCallResult:
        """Convert various tool result formats to ToolCallResult."""
        from .tool_handler import ToolResult as LegacyToolResult
        
        # Handle legacy ToolResult format (from tool_handler.py)
        if isinstance(result, LegacyToolResult):
            return ToolCallResult(
                tool_call_id=tool_call.id,
                name=tool_call.name,
                content=str(result.result) if result.success else "",
                error=result.error if not result.success else None
            )
        # Handle new ToolCallResult format (already correct)
        elif isinstance(result, ToolCallResult):
            return result
        # Handle dict-like objects
        elif hasattr(result, 'get'):
            return ToolCallResult(
                tool_call_id=tool_call.id,
                name=tool_call.name,
                content=str(result.get('result', result.get('content', str(result)))),
                error=result.get('error')
            )
        # Handle plain values
        else:
            return ToolCallResult(
                tool_call_id=tool_call.id,
                name=tool_call.name,
                content=str(result),
                error=None
            )


# Factory function for creating ChatManager with sensible defaults
def create_gemini_tool_llm(model_name: str = "gemini-2.0-flash-exp", simple_cache=None, **kwargs):
    """Create a Gemini LLM with native tool calling support."""
    GeminiToolLLM = optional_import('.gemini_tool_provider', 'GeminiToolLLM')
    return GeminiToolLLM(model_name=model_name, simple_cache=simple_cache, **kwargs) if GeminiToolLLM else None

def create_chat_manager(storage_dir: str = '/tmp/gaia_conversations',
                          llm_provider: LLMProvider = None,
                          cache_size: int = 100,
                          cache_ttl: int = 3600,
                          stats_retention_days: int = 30,
                          with_mcp: bool = False,
                          mcp_server_url: str = None,
                          cache_dir: str = None,
                          use_gemini_tools: bool = False,
                          accounting_system=None,
                          **kwargs) -> "ChatManager":
    """Create a ChatManager with default configuration.
    
    Args:
        storage_dir: Directory for file storage
        llm_provider: LLM provider to use (defaults to MockLLM)
        cache_size: Maximum conversations to cache
        cache_ttl: Cache TTL in seconds
        stats_retention_days: Days to retain statistics
        with_mcp: Enable MCP tool integration
        mcp_server_url: MCP server URL (defaults to localhost:9000)
        cache_dir: Directory for TTL cache (Level 0008b)
        use_gemini_tools: Enable Gemini with native tool calling
        accounting_system: Optional AccountingSystem for Level 0008a cost tracking
        **kwargs: Additional parameters for ChatManager
        
    Returns:
        Configured ChatManager instance
    """
    # Create storage components
    repository, cache, statistics = _create_storage_components(
        storage_dir, cache_size, cache_ttl, stats_retention_days
    )
    
    # Create TTL cache if specified
    simple_cache = _create_ttl_cache(cache_dir)
    
    # Create or configure LLM provider
    if llm_provider is None:
        llm_provider = _create_default_llm_provider(
            use_gemini_tools, with_mcp, mcp_server_url, simple_cache
        )
    else:
        _configure_llm_provider(llm_provider, simple_cache)
    
    # Setup tool registry
    tool_registry = _setup_tool_registry(
        with_mcp, mcp_server_url, simple_cache, accounting_system
    )
    
    return ChatManager(
        repository=repository,
        llm_provider=llm_provider,
        cache=cache,
        statistics=statistics,
        tool_registry=tool_registry,
        **kwargs
    )


def _create_storage_components(storage_dir: str, cache_size: int, 
                               cache_ttl: int, stats_retention_days: int):
    """Create storage-related components."""
    from .file_storage import FileConversationRepository
    from .conversation_cache import ConversationCache
    from .chat_statistics import ChatStatistics
    
    repository = FileConversationRepository(storage_dir)
    cache = ConversationCache(max_size=cache_size, ttl_seconds=cache_ttl)
    statistics = ChatStatistics(retention_days=stats_retention_days)
    
    return repository, cache, statistics


def _create_ttl_cache(cache_dir: Optional[str]):
    """Create TTL cache if directory specified."""
    if not cache_dir:
        return None
        
    try:
        from .cache import Cache
        simple_cache = Cache(cache_dir, default_ttl=3600)
        logger.info(f"TTL cache enabled: {cache_dir}")
        return simple_cache
    except Exception as e:
        logger.warning(f"Failed to create TTL cache: {e}")
        return None


def _create_default_llm_provider(use_gemini_tools: bool, with_mcp: bool,
                                mcp_server_url: Optional[str], simple_cache):
    """Create default LLM provider based on configuration."""
    if use_gemini_tools:
        # Try Gemini with native tool calling
        llm_provider = create_gemini_tool_llm(simple_cache=simple_cache)
        if llm_provider:
            return llm_provider
            
        logger.warning("Failed to create Gemini tool LLM, falling back to MockLLM")
    
    # Create MockLLM with optional MCP tools
    from .llm_providers import MockLLM
    tool_registry = None
    
    if with_mcp:
        tool_registry = _create_mcp_registry_adapter(
            mcp_server_url or "http://localhost:9000/mcp/", 
            simple_cache
        )
    
    return MockLLM(tool_registry=tool_registry)


def _configure_llm_provider(llm_provider, simple_cache):
    """Configure existing LLM provider with cache if supported."""
    if hasattr(llm_provider, 'simple_cache') and simple_cache:
        llm_provider.simple_cache = simple_cache


def _create_mcp_registry_adapter(server_url: str, simple_cache):
    """Create MCP tool registry adapter."""
    MCPToolRegistryAdapter = optional_import(
        '.mcp_tool_registry_adapter', 
        'MCPToolRegistryAdapter'
    )
    
    if MCPToolRegistryAdapter:
        logger.info("Created MCP tool registry adapter")
        return MCPToolRegistryAdapter(server_url=server_url, simple_cache=simple_cache)
    
    return None


def _setup_tool_registry(with_mcp: bool, mcp_server_url: Optional[str],
                        simple_cache, accounting_system):
    """Setup tool registry and optionally populate with MCP tools."""
    tool_registry = default_registry
    
    if not with_mcp:
        return tool_registry
    
    # Load MCP tools
    url = mcp_server_url or "http://localhost:9000/mcp/"
    MCPToolHandler = optional_import('.mcp_integration', 'MCPToolHandler')
    mcp_handler = MCPToolHandler(
        server_url=url, 
        simple_cache=simple_cache, 
        accounting_system=accounting_system
    ) if MCPToolHandler else None
    
    if mcp_handler:
        try:
            _register_mcp_tools(tool_registry, mcp_handler)
            logger.info(f"MCP tools registered from: {url}")
        except Exception as e:
            logger.warning(f"Failed to register MCP tools: {e}")
    else:
        logger.info("MCP tool integration not available")
    
    return tool_registry
