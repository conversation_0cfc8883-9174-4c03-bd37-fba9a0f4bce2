"""
Enhanced LLM provider interface supporting native tool calling.

This module defines the interfaces and data structures for LLMs that support
native tool/function calling capabilities (Claude, GPT-4, Gemini, etc.).
"""

from abc import ABC, abstractmethod
from dataclasses import dataclass
from typing import List, Dict, Any, Optional
import json
import logging

logger = logging.getLogger(__name__)

@dataclass
class ToolCall:
    """Represents a tool call request from the LLM."""
    id: str
    name: str
    args: Dict[str, Any]

@dataclass 
class ToolCallResult:
    """Represents the result of a tool execution in tool calling context."""
    tool_call_id: str
    name: str
    content: str
    error: Optional[str] = None
    
    @property
    def success(self) -> bool:
        """Return True if the tool execution was successful."""
        return self.error is None

@dataclass
class ToolCapableLLMResponse:
    """Enhanced LLM response with tool calling support."""
    content: Optional[str]          # Text response (may be None if only tool calling)
    tool_calls: List[ToolCall]      # Tools the LLM wants to call
    finish_reason: str              # 'stop', 'tool_calls', 'length', etc.
    usage: Dict[str, int]           # Token usage
    
    @property
    def has_tool_calls(self) -> bool:
        """Return True if this response contains tool calls."""
        return len(self.tool_calls) > 0

class ToolCapableLLM(ABC):
    """Enhanced LLM interface supporting native tool calling."""
    
    @abstractmethod
    def supports_tools(self) -> bool:
        """Return True if this LLM supports native tool calling."""
        pass
    
    @abstractmethod
    def generate_with_tools(self, messages: List[Dict], available_tools: List[Dict], 
                           **kwargs) -> ToolCapableLLMResponse:
        """Generate response with access to tools.
        
        Args:
            messages: Conversation history in standard format
            available_tools: Available tools in universal schema format
            **kwargs: Additional generation parameters
            
        Returns:
            ToolCapableLLMResponse with content and/or tool calls
        """
        pass
    
    @abstractmethod  
    def continue_with_tool_results(self, messages: List[Dict], tool_results: List[ToolCallResult],
                                  **kwargs) -> ToolCapableLLMResponse:
        """Continue conversation after tool execution.
        
        Args:
            messages: Conversation history including tool calls
            tool_results: Results from executed tools
            **kwargs: Additional generation parameters
            
        Returns:
            ToolCapableLLMResponse with final content
        """
        pass
    
    @abstractmethod
    def convert_tools_schema(self, mcp_tools: List[Dict]) -> List[Dict]:
        """Convert MCP tool schema to provider-specific format.
        
        Args:
            mcp_tools: Tools in MCP/universal format
            
        Returns:
            Tools in provider-specific format
        """
        pass

def create_universal_tool_schema(name: str, description: str, parameters: Dict[str, Any]) -> Dict[str, Any]:
    """Create a universal tool schema format.
    
    Args:
        name: Tool name
        description: Tool description
        parameters: JSON schema for parameters
        
    Returns:
        Universal tool schema
    """
    return {
        "name": name,
        "description": description,
        "parameters": parameters
    }

def convert_mcp_to_universal(mcp_tool: Dict[str, Any]) -> Dict[str, Any]:
    """Convert MCP tool schema to universal format.
    
    Args:
        mcp_tool: Tool in MCP format
        
    Returns:
        Tool in universal format
    """
    return create_universal_tool_schema(
        name=mcp_tool.get("name", "unknown"),
        description=mcp_tool.get("description", ""),
        parameters=mcp_tool.get("inputSchema", {})
    )