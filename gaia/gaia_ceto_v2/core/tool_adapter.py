"""
Simple adapter to unify tool interfaces.

Instead of multiple overlapping interfaces, this provides simple adapters
to make everything work with ToolRegistry.
"""

from typing import Dict, Any, Optional
import logging

from gaia_ceto_v2.tools.tools import Tool, FunctionTool, ToolRegistry
from gaia_ceto_v2.core.tool_calling_interface import <PERSON><PERSON><PERSON><PERSON>, ToolCallResult

logger = logging.getLogger(__name__)


class MCPToolAdapter(Tool):
    """Adapter to make MCP tools work with ToolRegistry."""
    
    def __init__(self, mcp_handler, tool_name: str, tool_spec: Dict[str, Any]):
        self.mcp_handler = mcp_handler
        name = tool_name
        description = tool_spec.get("description", f"MCP tool: {tool_name}")
        super().__init__(name, description)
        self._parameters_schema = tool_spec.get("inputSchema", {
            "type": "object",
            "properties": {}
        })

    def get_schema(self) -> Dict[str, Any]:
        """Get the JSON schema for this tool's parameters."""
        return self._parameters_schema
    
    def execute(self, **kwargs) -> str:
        """Execute MCP tool through handler."""
        result = self.mcp_handler.execute_single_tool(
            self.name, 
            kwargs,
            request_id=kwargs.pop('request_id', None)
        )
        
        if result.get('success'):
            return str(result.get('result', ''))
        else:
            raise Exception(result.get('error', 'Tool execution failed'))


def register_mcp_tools_in_registry(registry: ToolRegistry, mcp_handler) -> None:
    """Register all MCP tools in the unified registry."""
    try:
        # Get tools with schemas from MCP handler
        tools = mcp_handler.get_available_tools_with_schema()
        
        for tool_spec in tools:
            tool_name = tool_spec.get("name")
            
            # Create adapter
            adapter = MCPToolAdapter(mcp_handler, tool_name, tool_spec)
            
            # Register in unified registry
            registry.register_tool(adapter)
        
        logger.info(f"Registered {len(tools)} MCP tools in unified registry")
        
    except Exception as e:
        logger.error(f"Failed to register MCP tools: {e}")


def convert_tool_call_to_registry_format(tool_call: ToolCall, registry: ToolRegistry) -> Dict[str, Any]:
    """Convert ToolCall (from LLM interface) to registry execution format."""
    return {
        'tool_name': tool_call.name,
        'args': tool_call.args,
        'call_id': tool_call.id
    }


def execute_tool_call(tool_call: ToolCall, registry: ToolRegistry) -> ToolCallResult:
    """Execute a tool call using the unified registry."""
    try:
        # Get tool from registry
        tool = registry.get_tool(tool_call.name)
        if not tool:
            return ToolCallResult(
                tool_call_id=tool_call.id,
                name=tool_call.name,
                content="",
                error=f"Tool '{tool_call.name}' not found"
            )
        
        # Execute
        result = tool.execute(**tool_call.args)
        
        return ToolCallResult(
            tool_call_id=tool_call.id,
            name=tool_call.name,
            content=str(result),
            error=None
        )
        
    except Exception as e:
        logger.error(f"Tool execution failed: {e}")
        return ToolCallResult(
            tool_call_id=tool_call.id,
            name=tool_call.name,
            content="",
            error=str(e)
        )