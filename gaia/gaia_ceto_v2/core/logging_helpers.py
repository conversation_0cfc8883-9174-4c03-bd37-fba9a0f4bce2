"""
Simple logging helpers to reduce repetitive code.

No decorators, no magic - just simple functions that extract repetitive logging patterns.
"""

import json
import logging
from datetime import datetime
from typing import Any, Optional, Dict

# Get loggers
logger = logging.getLogger(__name__)

# Import the specialized loggers if available
try:
    from .logging_factory import create_llm_loggers, create_mcp_loggers, create_toolresponse_loggers
    _wrap_logger, _send_logger, _rcv_logger = create_llm_loggers()
    _mcp_wrap_logger, _mcp_send_logger, _mcp_rcv_logger = create_mcp_loggers()
    _tr_wrap_logger, _tr_send_logger, _tr_rcv_logger = create_toolresponse_loggers()
except ImportError:
    # Fallback to standard logger
    _wrap_logger = _send_logger = _rcv_logger = logger
    _mcp_wrap_logger = _mcp_send_logger = _mcp_rcv_logger = logger
    _tr_wrap_logger = _tr_send_logger = _tr_rcv_logger = logger


def log_llm_request(call_id: str, model: str, messages: list, **kwargs) -> None:
    """Log an LLM request - 3 lines instead of 30."""
    vendor, model_name = parse_model_identifier(model)
    
    # Terminal output
    print(f"🤔 LLM Call: {vendor}/{model_name} - {len(messages)} msgs")
    
    # File logging
    _wrap_logger.info(f"REQUEST - {call_id} - {model} - {len(messages)} messages")
    
    # Detailed request log
    request_data = {
        'call_id': call_id,
        'model': model,
        'message_count': len(messages),
        'messages': messages,
        'kwargs': kwargs,
        'timestamp': datetime.now().isoformat()
    }
    _send_logger.info(f"SEND - {call_id} - {json.dumps(request_data)}")


def log_llm_response(call_id: str, response_text: str, duration_ms: float, 
                    model: str = None, usage: Any = None) -> None:
    """Log an LLM response - 3 lines instead of 20."""
    char_count = len(response_text)
    
    # Terminal output
    print(f"✅ LLM Response: {char_count} chars ({duration_ms:.0f}ms)")
    
    # File logging
    _wrap_logger.info(f"SUCCESS - {call_id} - {char_count} chars - {duration_ms:.0f}ms")
    
    # Detailed response log
    response_data = {
        'call_id': call_id,
        'content': response_text[:200] + "..." if len(response_text) > 200 else response_text,
        'char_count': char_count,
        'duration_ms': duration_ms,
        'timestamp': datetime.now().isoformat()
    }
    
    # Include usage if available
    if usage:
        response_data['usage'] = {
            'prompt_tokens': getattr(usage, 'prompt_tokens', 0),
            'completion_tokens': getattr(usage, 'completion_tokens', 0),
            'total_tokens': getattr(usage, 'total_tokens', 0)
        }
    
    _rcv_logger.info(f"RECEIVE - {call_id} - {json.dumps(response_data)}")


def log_llm_error(call_id: str, error: Exception, duration_ms: float, 
                  model: str = None, messages: list = None) -> None:
    """Log an LLM error - 3 lines instead of 15."""
    vendor, model_name = parse_model_identifier(model) if model else ("unknown", "unknown")
    msg_count = len(messages) if messages else 0
    
    # Terminal output
    print(f"❌ LLM Error: {vendor}/{model_name} - {type(error).__name__} ({duration_ms:.0f}ms)")
    
    # File logging
    _wrap_logger.error(f"ERROR - {call_id} - {model} - {type(error).__name__}: {str(error)} - {duration_ms:.0f}ms")
    _rcv_logger.error(f"ERROR - {call_id} - Exception: {type(error).__name__}: {str(error)}")


def log_llm_cache_hit(call_id: str, model: str, messages: list) -> None:
    """Log a cache hit - 2 lines."""
    vendor, model_name = parse_model_identifier(model)
    print(f"🗃️  LLM Cache Hit: {vendor}/{model_name} - {len(messages)} msgs -> cached")
    _wrap_logger.info(f"CACHE_HIT - {call_id} - {model} - {len(messages)} messages")


def log_mcp_tool_call(tool_name: str, args: Dict[str, Any], call_id: str = None) -> None:
    """Log MCP tool call - simple version."""
    call_id = call_id or f"mcp_{datetime.now().strftime('%H%M%S')}"
    
    # Terminal
    print(f"🔧 Tool Call: {tool_name}")
    
    # Logging
    _mcp_wrap_logger.info(f"TOOL_CALL - {call_id} - {tool_name}")
    _mcp_send_logger.info(f"ARGS - {call_id} - {json.dumps(args)}")


def log_mcp_tool_result(tool_name: str, result: Any, duration_ms: float, 
                       call_id: str = None, success: bool = True) -> None:
    """Log MCP tool result."""
    call_id = call_id or f"mcp_{datetime.now().strftime('%H%M%S')}"
    
    if success:
        print(f"✅ Tool Result: {tool_name} ({duration_ms:.0f}ms)")
        _mcp_wrap_logger.info(f"TOOL_SUCCESS - {call_id} - {tool_name} - {duration_ms:.0f}ms")
    else:
        print(f"❌ Tool Error: {tool_name} ({duration_ms:.0f}ms)")
        _mcp_wrap_logger.error(f"TOOL_ERROR - {call_id} - {tool_name} - {duration_ms:.0f}ms")
    
    # Log result (truncated if large)
    result_str = str(result)
    if len(result_str) > 500:
        result_str = result_str[:500] + "..."
    _mcp_rcv_logger.info(f"RESULT - {call_id} - {result_str}")


def parse_model_identifier(model: str) -> tuple[str, str]:
    """Parse model string into vendor and model name."""
    if not model:
        return "unknown", "unknown"
    
    if '/' in model:
        vendor, model_name = model.split('/', 1)
    else:
        vendor = 'unknown'
        model_name = model
    return vendor, model_name


def log_toolresponse_flags(tool_name: str, flags: Dict[str, Any], call_id: str) -> None:
    """Log ToolResponse flags to terminal - concise version."""
    synthesis = "✨synthesis" if flags.get('needs_synthesis') else "🚫synthesis"
    display = "👁️display" if flags.get('show_to_user') else "🚫display"  
    docstore = "📄docstore" if flags.get('store_as_document') else "🚫docstore"
    
    print(f"🏷️  ToolResponse: {tool_name} - {synthesis} {display} {docstore}")


def log_toolresponse_docstore(tool_name: str, doc_id: str, call_id: str) -> None:
    """Log document storage result to terminal."""
    print(f"📄 Document Stored: {tool_name} → {doc_id}")


def log_toolresponse_pipeline(tool_name: str, synthesis_flag: bool, display_flag: bool, 
                             doc_id: Optional[str], call_id: str) -> None:
    """Log ToolResponse pipeline completion to terminal."""
    synthesis_result = "VERBATIM" if not synthesis_flag else "SYNTHESIS"
    display_result = "SHOWN" if display_flag else "HIDDEN"
    doc_result = f"DOC:{doc_id[:8]}" if doc_id else "NO_DOC"
    
    print(f"⚙️  Pipeline Complete: {tool_name} - {synthesis_result} {display_result} {doc_result}")


def calculate_duration_ms(start_time: datetime) -> float:
    """Calculate duration in milliseconds from start time."""
    return (datetime.now() - start_time).total_seconds() * 1000