"""
ChatManager - Simplified orchestrator using focused services.

Thin facade that coordinates specialized services.
No longer a god object - delegates all work to focused components.
"""

import logging
import uuid
from datetime import datetime
from typing import Dict, List, Any, Optional

from gaia_ceto_v2.core.conversation_service import ConversationService
from gaia_ceto_v2.core.message_service import MessageService
from gaia_ceto_v2.core.system_prompt_service import SystemPromptService
from gaia_ceto_v2.core.chat_statistics import ChatStatistics
from gaia_ceto_v2.core.conversation_serializer import ConversationSerializer
from gaia_ceto_v2.core.llm_providers import LLMProvider
from gaia_ceto_v2.core.storage_interface import ConversationStorage
from gaia_ceto_v2.core.conversation_cache import ConversationCache
from gaia_ceto_v2.core.tool_calling_interface import ToolCapableLLM
from gaia_ceto_v2.tools.simple_tools import SimpleToolRegistry, default_registry
from gaia_ceto_v2.core.level_0004_simple import log_existing_timing

logger = logging.getLogger(__name__)


class ChatManager:
    """Simplified ChatManager - thin orchestration facade.
    
    Coordinates focused services instead of doing everything.
    Each service handles one concern well.
    """
    
    def __init__(self,
                 repository: ConversationStorage,
                 llm_provider: LLMProvider,
                 cache: ConversationCache,
                 statistics: ChatStatistics,
                 tool_registry: SimpleToolRegistry = None,
                 max_context_messages: int = 20,
                 system_prompts_file: str = "prompt_sys.json"):
        """Initialize ChatManager with focused services."""
        
        self.conversation_service = ConversationService(repository, cache)
        self.message_service = MessageService(llm_provider, max_context_messages)
        self.prompt_service = SystemPromptService(system_prompts_file)
        self.statistics = statistics
        self.tool_registry = tool_registry or default_registry
        
        logger.info(f"ChatManager initialized with service composition")
    
    def create_conversation(self, 
                          user_id: str,
                          title: str = None,
                          system_prompt: str = None,
                          **metadata) -> str:
        """Create a new conversation."""
        try:
            conversation_id = self.conversation_service.create_conversation(
                user_id, title, **metadata
            )
            
            if system_prompt:
                conversation = self.conversation_service.get_conversation(conversation_id)
                system_text = self.prompt_service.get_system_prompt_text(system_prompt)
                if system_text:
                    conversation.add_message('system', system_text)
                    self.conversation_service.save_conversation(conversation)
            
            self.statistics.record_conversation_created(conversation_id, user_id)
            return conversation_id
            
        except Exception as e:
            logger.error(f"Error creating conversation for user {user_id}: {e}")
            self.statistics.record_error("conversation_creation", str(e))
            raise
    
    def send_message(self, 
                    conversation_id: str,
                    message: str,
                    **llm_kwargs) -> str:
        """Send a message to a conversation and get LLM response."""
        start_time = datetime.now()
        
        # Generate unique request_id for this conversation turn
        request_id = str(uuid.uuid4())
        logger.debug(f"Generated request_id {request_id} for conversation {conversation_id}")
        
        try:
            conversation = self.conversation_service.get_conversation(conversation_id)
            if not conversation:
                raise ValueError(f"Conversation {conversation_id} not found")
            
            self.statistics.record_message(
                conversation_id, 'user', getattr(conversation, 'user_id', None)
            )
            
            # Delegate all message processing to MessageService
            response = self.message_service.process_message(
                conversation, message, request_id, 
                tool_registry=self.tool_registry, **llm_kwargs
            )
            
            response_time_ms = (datetime.now() - start_time).total_seconds() * 1000
            self.statistics.record_message(
                conversation_id, 'assistant', 
                getattr(conversation, 'user_id', None), response_time_ms
            )
            
            # Log timing for chat message
            log_existing_timing(request_id, "chat_send_message", start_time, {
                "conversation_id": conversation_id, "message_length": len(message),
                "response_length": len(response), "success": True
            })
            
            if not self.conversation_service.save_conversation(conversation):
                logger.warning(f"Failed to save conversation {conversation_id} after message")
                self.statistics.record_error("conversation_save", "Failed to save after message")
            
            return response
            
        except Exception as e:
            logger.error(f"Error processing message in conversation {conversation_id}: {e}")
            self.statistics.record_error("message_processing", str(e))
            raise
    
    def get_conversation(self, conversation_id: str) -> Optional[Dict[str, Any]]:
        """Get a conversation by ID."""
        try:
            conversation = self.conversation_service.get_conversation(conversation_id)
            if conversation:
                return ConversationSerializer.to_dict(conversation)
            return None
        except Exception as e:
            logger.error(f"Error getting conversation {conversation_id}: {e}")
            self.statistics.record_error("conversation_retrieval", str(e))
            return None
    
    def list_conversations(self, user_id: str = None) -> List[Dict[str, Any]]:
        """List conversations, optionally filtered by user."""
        try:
            return self.conversation_service.list_conversations(user_id)
        except Exception as e:
            logger.error(f"Error listing conversations: {e}")
            self.statistics.record_error("conversation_listing", str(e))
            return []
    
    def delete_conversation(self, conversation_id: str) -> bool:
        """Delete a conversation."""
        try:
            return self.conversation_service.delete_conversation(conversation_id)
        except Exception as e:
            logger.error(f"Error deleting conversation {conversation_id}: {e}")
            self.statistics.record_error("conversation_deletion", str(e))
            return False
    
    def update_conversation_title(self, conversation_id: str, new_title: str) -> bool:
        """Update the title of a conversation."""
        try:
            return self.conversation_service.update_conversation_title(conversation_id, new_title)
        except Exception as e:
            logger.error(f"Error updating conversation title {conversation_id}: {e}")
            self.statistics.record_error("conversation_update", str(e))
            return False
    
    def get_conversation_stats(self, conversation_id: str) -> Optional[Dict[str, Any]]:
        """Get statistics about a conversation."""
        try:
            conversation = self.conversation_service.get_conversation(conversation_id)
            if not conversation:
                return None
            return self.statistics.get_conversation_stats(conversation)
        except Exception as e:
            logger.error(f"Error getting conversation stats {conversation_id}: {e}")
            self.statistics.record_error("stats_retrieval", str(e))
            return None
    
    def get_system_stats(self) -> Dict[str, Any]:
        """Get overall system statistics."""
        try:
            stats = self.statistics.get_system_stats()
            stats['cache'] = self.conversation_service.cache.get_stats()
            stats['llm_provider'] = type(self.message_service.llm_provider).__name__
            stats['llm_model'] = self.message_service.get_model_name()
            return stats
        except Exception as e:
            logger.error(f"Error getting system stats: {e}")
            self.statistics.record_error("system_stats", str(e))
            return {'error': str(e)}
    
    def set_llm_provider(self, llm_provider: LLMProvider) -> None:
        """Change the LLM provider."""
        self.message_service.set_llm_provider(llm_provider)
    
    def clear_conversation_history(self, conversation_id: str, keep_system_messages: bool = True) -> bool:
        """Clear all messages from a conversation."""
        try:
            return self.conversation_service.clear_conversation_history(conversation_id, keep_system_messages)
        except Exception as e:
            logger.error(f"Error clearing conversation history {conversation_id}: {e}")
            self.statistics.record_error("conversation_clear", str(e))
            return False
    
    def get_available_system_prompts(self) -> Dict[str, str]:
        """Get available system prompt keys and names."""
        return self.prompt_service.get_available_system_prompts()
    
    def get_available_tools(self) -> List[str]:
        """Get available tools from tool registry."""
        return self.tool_registry.list_tools()
    
    def get_conversation_system_prompt(self, conversation_id: str) -> Optional[str]:
        """Get the current system prompt for a conversation."""
        try:
            conversation = self.conversation_service.get_conversation(conversation_id)
            if not conversation:
                return None
            
            # Find the system message
            for message in conversation.messages:
                if message.role == 'system':
                    return message.content
            
            return None
        except Exception as e:
            logger.error(f"Error getting system prompt for conversation {conversation_id}: {e}")
            return None