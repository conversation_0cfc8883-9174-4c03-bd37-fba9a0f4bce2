"""
MCP Wrapper - Comprehensive logging for MCP calls.

This module provides logging infrastructure for MCP (Model Context Protocol) calls,
similar to the Level 0007 LLM wrapper logging. Tracks both outgoing calls and
incoming responses with dedicated log files.

Level 0007 Extension: MCP call logging with status summaries and dedicated files.
"""

import logging
import json
from datetime import datetime
from typing import Any, Dict, List, Optional, Union
from pathlib import Path
from .level_0004_simple import log_existing_timing

logger = logging.getLogger(__name__)

# Level 0020: Setup dedicated log files using centralized factory
from .logging_factory import create_mcp_loggers

# Initialize MCP loggers
_mcp_logger, _mcp_send_logger, _mcp_rcv_logger = create_mcp_loggers()


class MCPCallLogger:
    """Logger for MCP calls that provides comprehensive tracking."""
    
    @staticmethod
    def log_call_start(call_id: str, 
                      tool_name: str, 
                      server_url: str = None, 
                      server_name: str = None,
                      parameters: Dict[str, Any] = None) -> None:
        """Log the start of an MCP call.
        
        Args:
            call_id: Unique identifier for this call
            tool_name: Name of the tool being called
            server_url: URL of the MCP server (if applicable)
            server_name: Name of the MCP server (if applicable)
            parameters: Parameters being passed to the tool
        """
        start_time = datetime.now()
        
        # Determine server identifier
        server_id = server_name or server_url or "local"
        
        # Terminal logging
        param_count = len(parameters) if parameters else 0
        print(f"📞 MCP Call: {server_id}/{tool_name} - {param_count} params")
        
        # Main wrapper log
        _mcp_logger.info(f"CALL_START - {call_id} - {server_id}/{tool_name} - {param_count} parameters")
        
        # Detailed send log
        call_data = {
            'call_id': call_id,
            'tool_name': tool_name,
            'server_url': server_url,
            'server_name': server_name,
            'parameters': parameters or {},
            'timestamp': start_time.isoformat()
        }
        _mcp_send_logger.info(f"SEND - {call_id} - {json.dumps(call_data)}")
    
    @staticmethod
    def log_call_success(call_id: str,
                        tool_name: str,
                        server_id: str,
                        response: Any,
                        duration_ms: float) -> None:
        """Log successful MCP call completion.
        
        Args:
            call_id: Unique identifier for this call
            tool_name: Name of the tool that was called
            server_id: Server identifier
            response: Response from the MCP call
            duration_ms: Duration of the call in milliseconds
        """
        # Extract response details
        response_text = ""
        response_type = "unknown"
        
        if hasattr(response, 'content') and response.content:
            if isinstance(response.content, list) and len(response.content) > 0:
                if hasattr(response.content[0], 'text'):
                    response_text = response.content[0].text
                    response_type = "text"
                else:
                    response_text = str(response.content[0])
                    response_type = "content"
        elif isinstance(response, str):
            response_text = response
            response_type = "string"
        else:
            response_text = str(response)
            response_type = "object"
        
        char_count = len(response_text)
        
        # Terminal logging
        print(f"✅ MCP Success: {server_id}/{tool_name} -> {char_count} chars ({duration_ms:.0f}ms)")
        
        # Main wrapper log
        _mcp_logger.info(f"CALL_SUCCESS - {call_id} - {server_id}/{tool_name} - {char_count} chars - {duration_ms:.0f}ms")
        
        # Detailed receive log
        response_data = {
            'call_id': call_id,
            'tool_name': tool_name,
            'server_id': server_id,
            'response_type': response_type,
            'response_content': response_text,
            'char_count': char_count,
            'duration_ms': duration_ms,
            'timestamp': datetime.now().isoformat()
        }
        _mcp_rcv_logger.info(f"RECEIVE - {call_id} - {json.dumps(response_data)}")
    
    @staticmethod
    def log_call_error(call_id: str,
                      tool_name: str,
                      server_id: str,
                      error: Exception,
                      duration_ms: float) -> None:
        """Log MCP call error.
        
        Args:
            call_id: Unique identifier for this call
            tool_name: Name of the tool that was called
            server_id: Server identifier
            error: Exception that occurred
            duration_ms: Duration before error occurred
        """
        error_type = type(error).__name__
        error_message = str(error)
        
        # Terminal logging
        print(f"❌ MCP Error: {server_id}/{tool_name} -> {error_type} ({duration_ms:.0f}ms)")
        
        # Main wrapper log
        _mcp_logger.error(f"CALL_ERROR - {call_id} - {server_id}/{tool_name} - {error_type}: {error_message} - {duration_ms:.0f}ms")
        
        # Detailed receive log (for errors)
        error_data = {
            'call_id': call_id,
            'tool_name': tool_name,
            'server_id': server_id,
            'error_type': error_type,
            'error_message': error_message,
            'duration_ms': duration_ms,
            'timestamp': datetime.now().isoformat()
        }
        _mcp_rcv_logger.error(f"ERROR - {call_id} - {json.dumps(error_data)}")
    
    @staticmethod
    def generate_call_id(tool_name: str, server_id: str = "local") -> str:
        """Generate a unique call ID for MCP calls.
        
        Args:
            tool_name: Name of the tool being called
            server_id: Server identifier
            
        Returns:
            Unique call ID string
        """
        timestamp = datetime.now().strftime('%H%M%S')
        # Create a simple hash-like identifier from the current time
        hash_part = abs(hash(f"{tool_name}_{server_id}_{datetime.now().microsecond}")) % 1000
        return f"{server_id}_{tool_name}_{timestamp}_{hash_part}"


class MCPWrapper:
    """Wrapper for MCP operations with comprehensive logging."""
    
    def __init__(self, server_name: str = None, server_url: str = None):
        """Initialize MCP wrapper.
        
        Args:
            server_name: Name of the MCP server
            server_url: URL of the MCP server
        """
        self.server_name = server_name
        self.server_url = server_url
        self.server_id = server_name or server_url or "local"
    
    def wrap_tool_call(self, tool_name: str, parameters: Dict[str, Any] = None):
        """Context manager for wrapping MCP tool calls with logging.
        
        Args:
            tool_name: Name of the tool being called
            parameters: Parameters to pass to the tool
            
        Returns:
            Context manager that handles logging
        """
        return MCPCallContext(
            tool_name=tool_name,
            server_id=self.server_id,
            server_name=self.server_name,
            server_url=self.server_url,
            parameters=parameters
        )


class MCPCallContext:
    """Context manager for MCP calls with automatic logging."""
    
    def __init__(self, tool_name: str, server_id: str, 
                 server_name: str = None, server_url: str = None,
                 parameters: Dict[str, Any] = None):
        """Initialize call context.
        
        Args:
            tool_name: Name of the tool being called
            server_id: Server identifier
            server_name: Name of the MCP server
            server_url: URL of the MCP server  
            parameters: Parameters for the tool call
        """
        self.tool_name = tool_name
        self.server_id = server_id
        self.server_name = server_name
        self.server_url = server_url
        self.parameters = parameters
        self.call_id = MCPCallLogger.generate_call_id(tool_name, server_id)
        self.start_time = None
    
    def __enter__(self):
        """Enter the context - log call start."""
        self.start_time = datetime.now()
        MCPCallLogger.log_call_start(
            call_id=self.call_id,
            tool_name=self.tool_name,
            server_url=self.server_url,
            server_name=self.server_name,
            parameters=self.parameters
        )
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Exit the context - log call completion or error."""
        if self.start_time:
            duration_ms = (datetime.now() - self.start_time).total_seconds() * 1000
            
            if exc_type is not None:
                # An exception occurred
                MCPCallLogger.log_call_error(
                    call_id=self.call_id,
                    tool_name=self.tool_name,
                    server_id=self.server_id,
                    error=exc_val,
                    duration_ms=duration_ms
                )
                
                # Level 0004: Log timing for MCP error
                log_existing_timing(self.call_id, "mcp_call_error", self.start_time, {
                    "tool_name": self.tool_name, "server_id": self.server_id,
                    "error": True, "error_type": type(exc_val).__name__ if exc_val else "Unknown"
                })
            # Note: Success logging is handled manually via log_success()
            # because we need the response object
    
    def log_success(self, response: Any):
        """Log successful completion with response.
        
        Args:
            response: Response from the MCP call
        """
        if self.start_time:
            duration_ms = (datetime.now() - self.start_time).total_seconds() * 1000
            MCPCallLogger.log_call_success(
                call_id=self.call_id,
                tool_name=self.tool_name,
                server_id=self.server_id,
                response=response,
                duration_ms=duration_ms
            )
            
            # Level 0004: Log timing for successful MCP call
            response_size = len(str(response)) if response else 0
            log_existing_timing(self.call_id, "mcp_call_success", self.start_time, {
                "tool_name": self.tool_name, "server_id": self.server_id, 
                "response_size": response_size, "success": True
            })


# Convenience functions for direct logging
def log_mcp_tool_call(tool_name: str, parameters: Dict[str, Any] = None, 
                     server_name: str = None, server_url: str = None):
    """Convenience function to create an MCP logging context.
    
    Args:
        tool_name: Name of the tool being called
        parameters: Parameters for the tool call
        server_name: Name of the MCP server
        server_url: URL of the MCP server
        
    Returns:
        MCPCallContext for use in with statement
    """
    server_id = server_name or server_url or "local"
    return MCPCallContext(
        tool_name=tool_name,
        server_id=server_id,
        server_name=server_name,
        server_url=server_url,
        parameters=parameters
    )


# Initialize log files on import
try:
    # Ensure log files exist
    for log_file in ['/tmp/mcpwrap.log', '/tmp/mcpwrap_send.log', '/tmp/mcpwrap_rcv.log']:
        Path(log_file).touch(exist_ok=True)
    
    # Log initialization
    _mcp_logger.info("MCP logging infrastructure initialized")
    logger.info("MCP wrapper logging ready - /tmp/mcpwrap*.log files created")
    
except Exception as e:
    logger.error(f"Failed to initialize MCP logging: {e}")