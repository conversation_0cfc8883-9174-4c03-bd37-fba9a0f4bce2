"""
Level 0003: Multiple Log Architecture

Standardized multi-file logging pattern for service wrappers.
Provides centralized factory for 3-file logging: main, send, receive.
"""

import json
import logging
import uuid
from datetime import datetime
from pathlib import Path
from typing import Tuple, Dict, Any, Optional


class FlushingFileHandler(logging.FileHandler):
    """FileHandler that flushes after every emit for immediate log visibility."""
    
    def emit(self, record):
        """Emit a record and immediately flush to ensure real-time logging."""
        super().emit(record)
        self.flush()


class Level0003Logger:
    """
    Level 0003 compliant logger with 3-file pattern.
    
    Creates standardized logging for service wrappers:
    - Main log: flow control and status
    - Send log: complete outgoing requests (JSON)
    - Receive log: complete incoming responses (JSON)
    """
    
    def __init__(self, service_name: str, log_dir: str = '/tmp/gaia_logs/ceto'):
        """
        Initialize Level 0003 logger.
        
        Args:
            service_name: Name of service (e.g., 'llmwrap', 'mcpwrap')
            log_dir: Directory for log files
        """
        self.service_name = service_name
        self.log_dir = Path(log_dir)
        
        # Ensure log directory exists
        self.log_dir.mkdir(parents=True, exist_ok=True)
        
        # Create the three required loggers
        self.main_logger, self.send_logger, self.rcv_logger = self._create_loggers()
        
        # Force handler creation by emitting initial log entry and flushing
        self.main_logger.info(f"INIT - {service_name} logger initialized")
        self.send_logger.info(f"INIT - {service_name} send logger initialized")
        self.rcv_logger.info(f"INIT - {service_name} receive logger initialized")
        
        # Ensure handlers flush to create files
        for handler in self.main_logger.handlers:
            handler.flush()
        for handler in self.send_logger.handlers:
            handler.flush()
        for handler in self.rcv_logger.handlers:
            handler.flush()
    
    def _create_loggers(self) -> Tuple[logging.Logger, logging.Logger, logging.Logger]:
        """Create the three dedicated loggers."""
        # Use unique logger names to avoid conflicts in tests
        import uuid
        unique_id = str(uuid.uuid4())[:8]
        
        # Main service wrapper logger - flow control
        main_logger = logging.getLogger(f'{self.service_name}.main.{unique_id}')
        main_logger.setLevel(logging.INFO)
        # Clear any existing handlers to avoid conflicts
        main_logger.handlers.clear()
        handler = FlushingFileHandler(self.log_dir / f'{self.service_name}.log')
        handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(message)s'))
        main_logger.addHandler(handler)
        
        # Send logger - outgoing requests (JSON)
        send_logger = logging.getLogger(f'{self.service_name}.send.{unique_id}')
        send_logger.setLevel(logging.INFO)
        send_logger.handlers.clear()
        handler = FlushingFileHandler(self.log_dir / f'{self.service_name}_send.log')
        handler.setFormatter(logging.Formatter('%(asctime)s - %(message)s'))
        send_logger.addHandler(handler)
        
        # Receive logger - incoming responses (JSON)
        rcv_logger = logging.getLogger(f'{self.service_name}.rcv.{unique_id}')
        rcv_logger.setLevel(logging.INFO)
        rcv_logger.handlers.clear()
        handler = FlushingFileHandler(self.log_dir / f'{self.service_name}_rcv.log')
        handler.setFormatter(logging.Formatter('%(asctime)s - %(message)s'))
        rcv_logger.addHandler(handler)
        
        return main_logger, send_logger, rcv_logger
    
    def generate_request_id(self) -> str:
        """Generate unique request_id for correlation across log files and accounting system."""
        timestamp = datetime.now().strftime('%H%M%S')
        unique_id = str(uuid.uuid4())[:8]
        return f"{self.service_name}_{timestamp}_{unique_id}"
    
    def log_request(self, request_id: str, summary: str, request_data: Dict[str, Any]):
        """
        Log outgoing request using Level 0003 pattern.
        
        Args:
            request_id: Unique identifier for correlation with accounting system
            summary: Brief description for main log
            request_data: Complete request payload for send log
        """
        # Main log: flow control
        self.main_logger.info(f"REQUEST - {request_id} - {summary}")
        
        # Send log: complete JSON payload
        payload = {
            'request_id': request_id,
            'timestamp': datetime.now().isoformat(),
            **request_data
        }
        self.send_logger.info(f"SEND - {request_id} - {json.dumps(payload)}")
    
    def log_response(self, request_id: str, summary: str, response_data: Dict[str, Any], 
                    duration_ms: Optional[float] = None):
        """
        Log incoming response using Level 0003 pattern.
        
        Args:
            request_id: Unique identifier for correlation with accounting system
            summary: Brief description for main log
            response_data: Complete response payload for receive log
            duration_ms: Optional timing information
        """
        # Main log: flow control with timing
        timing_info = f" - {duration_ms:.0f}ms" if duration_ms else ""
        self.main_logger.info(f"SUCCESS - {request_id} - {summary}{timing_info}")
        
        # Receive log: complete JSON payload
        payload = {
            'request_id': request_id,
            'timestamp': datetime.now().isoformat(),
            'duration_ms': duration_ms,
            **response_data
        }
        self.rcv_logger.info(f"RECEIVE - {request_id} - {json.dumps(payload)}")
    
    def log_error(self, request_id: str, summary: str, error: Exception, 
                 duration_ms: Optional[float] = None):
        """
        Log error using Level 0003 pattern.
        
        Args:
            request_id: Unique identifier for correlation with accounting system
            summary: Brief description for main log
            error: Exception that occurred
            duration_ms: Optional timing information
        """
        # Main log: error status
        timing_info = f" - {duration_ms:.0f}ms" if duration_ms else ""
        self.main_logger.error(f"ERROR - {request_id} - {summary} - {type(error).__name__}: {str(error)}{timing_info}")
        
        # Receive log: error details as JSON
        payload = {
            'request_id': request_id,
            'timestamp': datetime.now().isoformat(),
            'duration_ms': duration_ms,
            'error_type': type(error).__name__,
            'error_message': str(error),
            'success': False
        }
        self.rcv_logger.error(f"ERROR - {request_id} - {json.dumps(payload)}")
    
    def log_cache_hit(self, request_id: str, summary: str):
        """
        Log cache hit using Level 0003 pattern.
        
        Args:
            request_id: Unique identifier for correlation with accounting system
            summary: Brief description for main log
        """
        # Main log: cache hit status
        self.main_logger.info(f"CACHE_HIT - {request_id} - {summary}")
        
        # Receive log: cache hit as JSON
        payload = {
            'request_id': request_id,
            'timestamp': datetime.now().isoformat(),
            'cache_hit': True,
            'summary': summary
        }
        self.rcv_logger.info(f"CACHE_HIT - {request_id} - {json.dumps(payload)}")


def create_level_0003_logger(service_name: str, log_dir: str = '/tmp/gaia_logs/ceto') -> Level0003Logger:
    """
    Factory function to create Level 0003 compliant logger.
    
    Args:
        service_name: Name of service (e.g., 'llmwrap', 'mcpwrap')
        log_dir: Directory for log files
        
    Returns:
        Level0003Logger instance
    """
    return Level0003Logger(service_name, log_dir)


# Legacy compatibility functions for existing code
def create_dedicated_loggers(service_name: str, log_dir: str = '/tmp/gaia_logs/ceto') -> Tuple[logging.Logger, logging.Logger, logging.Logger]:
    """
    Legacy function for backward compatibility.
    
    Returns:
        Tuple of (main_logger, send_logger, receive_logger)
    """
    logger_instance = Level0003Logger(service_name, log_dir)
    return logger_instance.main_logger, logger_instance.send_logger, logger_instance.rcv_logger


def create_llm_loggers(log_dir: str = '/tmp/gaia_logs/ceto') -> Tuple[logging.Logger, logging.Logger, logging.Logger]:
    """Legacy LLM logger creation for backward compatibility."""
    return create_dedicated_loggers('llmwrap', log_dir)


def create_mcp_loggers(log_dir: str = '/tmp/gaia_logs/ceto') -> Tuple[logging.Logger, logging.Logger, logging.Logger]:
    """Legacy MCP logger creation for backward compatibility."""
    return create_dedicated_loggers('mcpwrap', log_dir)