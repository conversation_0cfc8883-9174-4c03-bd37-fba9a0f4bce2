"""
File Storage Implementation - Concrete storage using filesystem.

Implements the storage interfaces using JSON files and directory structure.
Composed from focused components following Interface Segregation Principle.
"""

import json
import os
import logging
from datetime import datetime
from typing import List, Dict, Any, Optional
from pathlib import Path

from .storage_interface import ConversationStorage
from .conversation import Conversation
from .conversation_serializer import ConversationSerializer

logger = logging.getLogger(__name__)


class FileSystemBackend:
    """File system storage backend implementation."""
    
    def __init__(self, base_path: str):
        """Initialize filesystem backend.
        
        Args:
            base_path: Base directory for storage
        """
        self.base_path = Path(base_path)
        self.base_path.mkdir(parents=True, exist_ok=True)
        logger.info(f"FileSystemBackend initialized at {base_path}")
    
    def write_data(self, key: str, data: bytes) -> bool:
        """Write data to file."""
        try:
            file_path = self.base_path / key
            file_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(file_path, 'wb') as f:
                f.write(data)
            
            logger.debug(f"Wrote data to {file_path}")
            return True
            
        except Exception as e:
            logger.error(f"Error writing to {key}: {e}")
            return False
    
    def read_data(self, key: str) -> Optional[bytes]:
        """Read data from file."""
        try:
            file_path = self.base_path / key
            
            if not file_path.exists():
                return None
            
            with open(file_path, 'rb') as f:
                data = f.read()
            
            logger.debug(f"Read data from {file_path}")
            return data
            
        except Exception as e:
            logger.error(f"Error reading from {key}: {e}")
            return None
    
    def delete_data(self, key: str) -> bool:
        """Delete file."""
        try:
            file_path = self.base_path / key
            
            if file_path.exists():
                file_path.unlink()
                logger.debug(f"Deleted {file_path}")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error deleting {key}: {e}")
            return False
    
    def list_keys(self, prefix: str = None) -> List[str]:
        """List files with optional prefix."""
        try:
            keys = []
            
            if prefix:
                search_path = self.base_path / prefix
                if search_path.is_dir():
                    for file_path in search_path.rglob('*.json'):
                        relative_path = file_path.relative_to(self.base_path)
                        keys.append(str(relative_path))
            else:
                for file_path in self.base_path.rglob('*.json'):
                    relative_path = file_path.relative_to(self.base_path)
                    keys.append(str(relative_path))
            
            return keys
            
        except Exception as e:
            logger.error(f"Error listing keys with prefix {prefix}: {e}")
            return []
    
    def exists(self, key: str) -> bool:
        """Check if file exists."""
        file_path = self.base_path / key
        return file_path.exists()


class JsonStorageSerializer:
    """JSON serialization strategy for conversations."""
    
    def serialize(self, conversation: Conversation) -> bytes:
        """Serialize conversation to JSON bytes."""
        try:
            conversation_dict = ConversationSerializer.to_dict(conversation)
            json_str = json.dumps(conversation_dict, indent=2, ensure_ascii=False)
            return json_str.encode('utf-8')
            
        except Exception as e:
            logger.error(f"Error serializing conversation: {e}")
            raise
    
    def deserialize(self, data: bytes) -> Optional[Conversation]:
        """Deserialize conversation from JSON bytes."""
        try:
            json_str = data.decode('utf-8')
            conversation_dict = json.loads(json_str)
            return ConversationSerializer.from_dict(conversation_dict)
            
        except Exception as e:
            logger.error(f"Error deserializing conversation: {e}")
            return None
    
    def get_format_name(self) -> str:
        """Get format name."""
        return "JSON"


class FileConversationRepository(ConversationStorage):
    """File-based conversation repository implementation.
    
    Composes filesystem backend and JSON serializer to provide
    conversation persistence using files.
    """
    
    def __init__(self, storage_dir: str, serializer: 'JsonStorageSerializer' = None):
        """Initialize file repository.
        
        Args:
            storage_dir: Directory for conversation files
            serializer: Optional custom serializer (defaults to JSON)
        """
        self.backend = FileSystemBackend(storage_dir)
        self.serializer = serializer or JsonStorageSerializer()
        
        logger.info(f"FileConversationRepository initialized with {self.serializer.get_format_name()} serialization")
    
    def save(self, conversation: Conversation) -> bool:
        """Save conversation to file."""
        if not conversation or not conversation.conversation_id:
            logger.error("Cannot save conversation without valid ID")
            return False
        
        try:
            # Serialize conversation
            data = self.serializer.serialize(conversation)
            
            # Generate file key
            key = self._get_conversation_key(conversation.conversation_id)
            
            # Write to storage
            success = self.backend.write_data(key, data)
            
            if success:
                logger.info(f"Saved conversation {conversation.conversation_id}")
            
            return success
            
        except Exception as e:
            logger.error(f"Error saving conversation {conversation.conversation_id}: {e}")
            return False
    
    def load(self, conversation_id: str) -> Optional[Conversation]:
        """Load conversation from file."""
        if not conversation_id:
            return None
        
        try:
            # Generate file key
            key = self._get_conversation_key(conversation_id)
            
            # Read from storage
            data = self.backend.read_data(key)
            if not data:
                return None
            
            # Deserialize conversation
            conversation = self.serializer.deserialize(data)
            
            if conversation:
                logger.debug(f"Loaded conversation {conversation_id}")
            
            return conversation
            
        except Exception as e:
            logger.error(f"Error loading conversation {conversation_id}: {e}")
            return None
    
    def delete(self, conversation_id: str) -> bool:
        """Delete conversation file."""
        if not conversation_id:
            return False
        
        try:
            key = self._get_conversation_key(conversation_id)
            success = self.backend.delete_data(key)
            
            if success:
                logger.info(f"Deleted conversation {conversation_id}")
            
            return success
            
        except Exception as e:
            logger.error(f"Error deleting conversation {conversation_id}: {e}")
            return False
    
    def exists(self, conversation_id: str) -> bool:
        """Check if conversation file exists."""
        if not conversation_id:
            return False
        
        key = self._get_conversation_key(conversation_id)
        return self.backend.exists(key)
    
    def list_conversations(self, user_id: str = None) -> List[Dict[str, Any]]:
        """List all conversations with optional user filter."""
        try:
            # Get all conversation keys
            prefix = f"users/{user_id}/" if user_id else None
            keys = self.backend.list_keys(prefix)
            
            conversations = []
            
            for key in keys:
                # Extract conversation ID from key
                conv_id = self._extract_conversation_id(key)
                if not conv_id:
                    continue
                
                # Load conversation for summary
                conversation = self.load(conv_id)
                if conversation:
                    try:
                        summary = ConversationSerializer.to_summary_dict(conversation)
                        conversations.append(summary)
                    except Exception as e:
                        logger.warning(f"Error creating summary for {conv_id}: {e}")
                        continue
            
            # Sort by updated_at (most recent first)
            conversations.sort(
                key=lambda x: x.get('updated_at', ''),
                reverse=True
            )
            
            logger.debug(f"Listed {len(conversations)} conversations for user {user_id or 'all'}")
            return conversations
            
        except Exception as e:
            logger.error(f"Error listing conversations: {e}")
            return []
    
    def _get_conversation_key(self, conversation_id: str) -> str:
        """Generate storage key for conversation."""
        # Use hierarchical structure: conversations/{first_2_chars}/{conversation_id}.json
        prefix = conversation_id[:2] if len(conversation_id) >= 2 else 'xx'
        return f"conversations/{prefix}/{conversation_id}.json"
    
    def _extract_conversation_id(self, key: str) -> Optional[str]:
        """Extract conversation ID from storage key."""
        try:
            # Key format: conversations/{prefix}/{conversation_id}.json
            parts = key.split('/')
            if len(parts) >= 3 and parts[0] == 'conversations':
                filename = parts[-1]
                if filename.endswith('.json'):
                    return filename[:-5]  # Remove .json extension
            return None
            
        except Exception:
            return None
    
    def get_storage_stats(self) -> Dict[str, Any]:
        """Get storage statistics."""
        try:
            all_keys = self.backend.list_keys()
            total_conversations = len(all_keys)
            
            # Calculate total storage size
            total_size = 0
            for key in all_keys:
                data = self.backend.read_data(key)
                if data:
                    total_size += len(data)
            
            return {
                'total_conversations': total_conversations,
                'total_size_bytes': total_size,
                'total_size_mb': round(total_size / (1024 * 1024), 2),
                'storage_format': self.serializer.get_format_name(),
                'storage_path': str(self.backend.base_path)
            }
            
        except Exception as e:
            logger.error(f"Error getting storage stats: {e}")
            return {'error': str(e)}


# Factory function for backward compatibility
# Factory function removed - use FileConversationRepository(storage_dir) directly