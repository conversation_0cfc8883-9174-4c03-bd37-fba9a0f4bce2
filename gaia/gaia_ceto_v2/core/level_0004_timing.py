"""
Level 0004: Stopwatch Timer and Latency Log

Standardized timing functions for debug and logging.
Single timing log where timings of various things are shown.
Accepts request_id for correlation with level 0003 logging and accounting.
"""

import json
import time
import uuid
from contextlib import contextmanager
from datetime import datetime
from pathlib import Path
from typing import Optional, Dict, Any, Iterator
from dataclasses import dataclass

from .level_0003_logging import FlushingFileHandler
import logging


@dataclass
class TimingEntry:
    """Single timing measurement entry."""
    request_id: str
    operation: str
    duration_ms: float
    timestamp: str
    metadata: Dict[str, Any]


class Level0004Timer:
    """
    Level 0004 compliant timing system.
    
    Provides standardized stopwatch functionality with centralized latency logging.
    All timings are logged to a single dedicated timing log file.
    """
    
    def __init__(self, log_dir: str = '/tmp/gaia_logs/ceto'):
        """
        Initialize Level 0004 timer.
        
        Args:
            log_dir: Directory for timing log file
        """
        self.log_dir = Path(log_dir)
        self.log_dir.mkdir(parents=True, exist_ok=True)
        
        # Create dedicated timing logger
        self.timing_logger = self._create_timing_logger()
        
        # Initialize timing log
        self.timing_logger.info("INIT - Level 0004 timing system initialized")
        
        # Ensure handler flushes to create file
        for handler in self.timing_logger.handlers:
            handler.flush()
    
    def _create_timing_logger(self) -> logging.Logger:
        """Create dedicated timing logger."""
        unique_id = str(uuid.uuid4())[:8]
        
        timing_logger = logging.getLogger(f'level0004.timing.{unique_id}')
        timing_logger.setLevel(logging.INFO)
        timing_logger.handlers.clear()
        
        handler = FlushingFileHandler(self.log_dir / 'timing.log')
        handler.setFormatter(logging.Formatter('%(asctime)s - %(message)s'))
        timing_logger.addHandler(handler)
        
        return timing_logger
    
    def generate_request_id(self, prefix: str = "timing") -> str:
        """Generate unique request_id for timing correlation."""
        timestamp = datetime.now().strftime('%H%M%S')
        unique_id = str(uuid.uuid4())[:8]
        return f"{prefix}_{timestamp}_{unique_id}"
    
    @contextmanager
    def stopwatch(self, operation: str, request_id: Optional[str] = None, 
                  **metadata) -> Iterator[None]:
        """
        Context manager for timing operations.
        
        Args:
            operation: Description of operation being timed
            request_id: Optional request_id for correlation with other logs
            **metadata: Additional metadata to log with timing
            
        Usage:
            with timer.stopwatch("llm_call", request_id="req_123", model="gpt-4"):
                result = call_llm()
        """
        if request_id is None:
            request_id = self.generate_request_id()
        
        start_time = time.time()
        
        try:
            yield
        finally:
            duration_ms = (time.time() - start_time) * 1000
            self.log_timing(request_id, operation, duration_ms, metadata)
    
    def time_operation(self, operation: str, func, request_id: Optional[str] = None, 
                      *args, **kwargs) -> tuple:
        """
        Time a function call and return (result, duration_ms).
        
        Args:
            operation: Description of operation being timed
            func: Function to execute and time
            request_id: Optional request_id for correlation
            *args, **kwargs: Arguments for func
            
        Returns:
            Tuple of (result, duration_ms)
        """
        if request_id is None:
            request_id = self.generate_request_id()
        
        start_time = time.time()
        
        try:
            result = func(*args, **kwargs)
            duration_ms = (time.time() - start_time) * 1000
            
            # Extract metadata from result if possible
            metadata = self._extract_result_metadata(result)
            self.log_timing(request_id, operation, duration_ms, metadata)
            
            return result, duration_ms
            
        except Exception as e:
            duration_ms = (time.time() - start_time) * 1000
            
            # Log timing even for failed operations
            metadata = {
                'error': True,
                'error_type': type(e).__name__,
                'error_message': str(e)
            }
            self.log_timing(request_id, f"{operation}_ERROR", duration_ms, metadata)
            
            raise
    
    def log_timing(self, request_id: str, operation: str, duration_ms: float, 
                  metadata: Optional[Dict[str, Any]] = None):
        """
        Log timing entry to centralized timing log.
        
        Args:
            request_id: Request ID for correlation with other logs
            operation: Description of timed operation
            duration_ms: Duration in milliseconds
            metadata: Additional metadata to include
        """
        if metadata is None:
            metadata = {}
        
        # Create timing entry
        entry = TimingEntry(
            request_id=request_id,
            operation=operation,
            duration_ms=duration_ms,
            timestamp=datetime.now().isoformat(),
            metadata=metadata
        )
        
        # Log as JSON for structured data
        log_data = {
            'request_id': entry.request_id,
            'operation': entry.operation,
            'duration_ms': entry.duration_ms,
            'timestamp': entry.timestamp,
            **entry.metadata
        }
        
        # Main timing log entry
        self.timing_logger.info(f"TIMING - {request_id} - {operation} - {duration_ms:.1f}ms - {json.dumps(log_data)}")
    
    def _extract_result_metadata(self, result: Any) -> Dict[str, Any]:
        """Extract useful metadata from operation result."""
        metadata = {}
        
        # Character count for text results
        if isinstance(result, str):
            metadata['char_count'] = len(result)
        elif hasattr(result, 'content') and isinstance(result.content, str):
            metadata['char_count'] = len(result.content)
        
        # Item count for collections
        if hasattr(result, '__len__'):
            try:
                metadata['item_count'] = len(result)
            except TypeError:
                pass
        
        # Success indicator
        if hasattr(result, 'success'):
            metadata['success'] = result.success
        elif isinstance(result, dict) and 'success' in result:
            metadata['success'] = result['success']
        else:
            metadata['success'] = True
        
        return metadata


# Global timer instance for simple usage
_global_timer: Optional[Level0004Timer] = None


def get_timer(log_dir: str = '/tmp/gaia_logs/ceto') -> Level0004Timer:
    """Get or create global timer instance."""
    global _global_timer
    if _global_timer is None:
        _global_timer = Level0004Timer(log_dir)
    return _global_timer


# Simple interface functions
def stopwatch(operation: str, request_id: Optional[str] = None, **metadata):
    """Context manager for timing operations using global timer."""
    return get_timer().stopwatch(operation, request_id, **metadata)


def time_operation(operation: str, func, request_id: Optional[str] = None, *args, **kwargs):
    """Time a function call using global timer."""
    return get_timer().time_operation(operation, func, request_id, *args, **kwargs)


def log_timing(request_id: str, operation: str, duration_ms: float, 
              metadata: Optional[Dict[str, Any]] = None):
    """Log timing entry using global timer."""
    get_timer().log_timing(request_id, operation, duration_ms, metadata)


def generate_timing_request_id(prefix: str = "timing") -> str:
    """Generate timing request ID using global timer."""
    return get_timer().generate_request_id(prefix)


# Integration helpers for existing service wrappers
class TimingMixin:
    """Mixin class to add timing capabilities to existing service wrappers."""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.timer = get_timer()
    
    def execute_with_timing(self, operation: str, func, request_id: Optional[str] = None, 
                           *args, **kwargs):
        """Execute function with automatic timing integration."""
        with self.timer.stopwatch(operation, request_id):
            return func(*args, **kwargs)