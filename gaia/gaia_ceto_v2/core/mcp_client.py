"""
MCP Client - Enhanced MCP client with comprehensive logging.

This module provides an MCP client wrapper that includes Level 0007 logging
for all MCP calls, both successful and failed.
"""

import asyncio
import logging
from typing import Dict, Any, Optional, List
from datetime import datetime

from mcp.client.session import ClientSession
from mcp.client.streamable_http import streamablehttp_client

from .mcp_wrapper import MCPWrapper, log_mcp_tool_call

logger = logging.getLogger(__name__)


class LoggedMCPClient:
    """MCP Client with comprehensive Level 0007 logging."""
    
    def __init__(self, server_url: str, server_name: str = None):
        """Initialize logged MCP client.
        
        Args:
            server_url: URL of the MCP server
            server_name: Optional friendly name for the server
        """
        self.server_url = server_url
        self.server_name = server_name or server_url
        self.wrapper = MCPWrapper(server_name=self.server_name, server_url=server_url)
        self._session = None
    
    async def __aenter__(self):
        """Async context manager entry."""
        self._read, self._write, _ = await streamablehttp_client(self.server_url).__aenter__()
        self._session = await ClientSession(self._read, self._write).__aenter__()
        await self._session.initialize()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        if self._session:
            await self._session.__aexit__(exc_type, exc_val, exc_tb)
    
    async def list_tools(self) -> List[Dict[str, Any]]:
        """List available tools with logging.
        
        Returns:
            List of available tools
        """
        with log_mcp_tool_call("list_tools", {}, server_name=self.server_name, server_url=self.server_url) as call:
            try:
                tool_list = await self._session.list_tools()
                tools = [{"name": t.name, "description": t.description} for t in tool_list.tools]
                call.log_success(f"Found {len(tools)} tools: {[t['name'] for t in tools]}")
                return tools
            except Exception as e:
                # Error is automatically logged by context manager
                raise
    
    async def call_tool(self, tool_name: str, parameters: Dict[str, Any] = None) -> Any:
        """Call a tool with logging.
        
        Args:
            tool_name: Name of the tool to call
            parameters: Parameters to pass to the tool
            
        Returns:
            Tool response
        """
        params = parameters or {}
        
        with log_mcp_tool_call(tool_name, params, server_name=self.server_name, server_url=self.server_url) as call:
            try:
                if params:
                    result = await self._session.call_tool(tool_name, **params)
                else:
                    result = await self._session.call_tool(tool_name)
                
                call.log_success(result)
                return result
            except Exception as e:
                # Error is automatically logged by context manager
                raise
    
    async def get_tool_schema(self, tool_name: str) -> Dict[str, Any]:
        """Get schema for a specific tool.
        
        Args:
            tool_name: Name of the tool
            
        Returns:
            Tool schema
        """
        with log_mcp_tool_call("get_tool_schema", {"tool_name": tool_name}, 
                              server_name=self.server_name, server_url=self.server_url) as call:
            try:
                tools = await self._session.list_tools()
                for tool in tools.tools:
                    if tool.name == tool_name:
                        schema = {"name": tool.name, "description": tool.description}
                        if hasattr(tool, 'inputSchema'):
                            schema['inputSchema'] = tool.inputSchema
                        call.log_success(f"Schema for {tool_name}")
                        return schema
                
                # Tool not found
                raise ValueError(f"Tool '{tool_name}' not found")
            except Exception as e:
                # Error is automatically logged by context manager
                raise


class MCPClientManager:
    """Manager for multiple MCP clients with logging."""
    
    def __init__(self):
        """Initialize MCP client manager."""
        self.clients: Dict[str, LoggedMCPClient] = {}
    
    def add_server(self, server_name: str, server_url: str) -> None:
        """Add an MCP server to manage.
        
        Args:
            server_name: Friendly name for the server
            server_url: URL of the MCP server
        """
        self.clients[server_name] = LoggedMCPClient(server_url, server_name)
        logger.info(f"Added MCP server '{server_name}' at {server_url}")
    
    async def call_tool_on_server(self, server_name: str, tool_name: str, 
                                 parameters: Dict[str, Any] = None) -> Any:
        """Call a tool on a specific server.
        
        Args:
            server_name: Name of the server
            tool_name: Name of the tool to call
            parameters: Parameters for the tool
            
        Returns:
            Tool response
        """
        if server_name not in self.clients:
            raise ValueError(f"Server '{server_name}' not found")
        
        client = self.clients[server_name]
        async with client:
            return await client.call_tool(tool_name, parameters)
    
    async def list_all_tools(self) -> Dict[str, List[Dict[str, Any]]]:
        """List tools from all servers.
        
        Returns:
            Dictionary mapping server names to their tools
        """
        all_tools = {}
        
        for server_name, client in self.clients.items():
            try:
                async with client:
                    tools = await client.list_tools()
                    all_tools[server_name] = tools
            except Exception as e:
                logger.error(f"Failed to list tools from server '{server_name}': {e}")
                all_tools[server_name] = []
        
        return all_tools


# Convenience functions for direct MCP operations with logging
async def logged_mcp_call(server_url: str, tool_name: str, 
                         parameters: Dict[str, Any] = None,
                         server_name: str = None) -> Any:
    """Make a single MCP call with logging.
    
    Args:
        server_url: URL of the MCP server
        tool_name: Name of the tool to call
        parameters: Parameters for the tool
        server_name: Optional friendly name for the server
        
    Returns:
        Tool response
    """
    async with LoggedMCPClient(server_url, server_name) as client:
        return await client.call_tool(tool_name, parameters)


async def logged_mcp_list_tools(server_url: str, server_name: str = None) -> List[Dict[str, Any]]:
    """List tools from an MCP server with logging.
    
    Args:
        server_url: URL of the MCP server
        server_name: Optional friendly name for the server
        
    Returns:
        List of available tools
    """
    async with LoggedMCPClient(server_url, server_name) as client:
        return await client.list_tools()