#!/usr/bin/env python3
"""
Truth CLI - Command-line interface for testing truth detection on log traces.
"""

import argparse
import json
import sys
import logging
from pathlib import Path
from typing import Dict, List, Any, Optional
from datetime import datetime
import re

from .truth_tracker import TruthViolationTracker, ViolationType
from .truth_enforcer import TruthEnforcer

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

class LogTraceParser:
    """Parse log trace files to extract LLM interactions."""
    
    def __init__(self):
        # Patterns to extract log data
        self.llm_request_pattern = re.compile(r'REQUEST - (\S+) - (\S+) - (.+)')
        self.llm_response_pattern = re.compile(r'CONTINUE - (\S+) - (.+)')
        self.tool_call_pattern = re.compile(r'REQUEST - (\S+) - (\w+) - (.+)')
        self.tool_result_pattern = re.compile(r'(?:SUCCESS|ERROR) - (\S+) - (\w+) - (.+)')
    
    def parse_log_file(self, log_file: Path) -> List[Dict[str, Any]]:
        """Parse log file and extract LLM interactions."""
        interactions = []
        current_interaction = None
        
        try:
            with open(log_file, 'r') as f:
                for line_num, line in enumerate(f, 1):
                    line = line.strip()
                    if not line:
                        continue
                    
                    # Try to parse different log types
                    interaction = self._parse_log_line(line, line_num)
                    if interaction:
                        if interaction['type'] == 'llm_request':
                            # Start new interaction
                            if current_interaction:
                                interactions.append(current_interaction)
                            current_interaction = interaction
                            current_interaction['tool_calls'] = []
                            current_interaction['tool_results'] = {}
                        
                        elif interaction['type'] == 'llm_response' and current_interaction:
                            # Complete current interaction
                            current_interaction.update(interaction)
                        
                        elif interaction['type'] in ['tool_call', 'tool_result'] and current_interaction:
                            # Add to current interaction
                            if interaction['type'] == 'tool_call':
                                current_interaction['tool_calls'].append(interaction)
                            else:
                                current_interaction['tool_results'][interaction['tool_name']] = interaction
                
                # Add final interaction
                if current_interaction:
                    interactions.append(current_interaction)
        
        except Exception as e:
            logger.error(f"Failed to parse log file {log_file}: {e}")
            return []
        
        logger.info(f"Parsed {len(interactions)} interactions from {log_file}")
        return interactions
    
    def _parse_log_line(self, line: str, line_num: int) -> Optional[Dict[str, Any]]:
        """Parse a single log line."""
        try:
            # Extract timestamp and log level
            parts = line.split(' - ', 3)
            if len(parts) < 4:
                return None
            
            timestamp, log_level, logger_name, message = parts
            
            # Parse LLM requests
            if 'REQUEST' in message and ('llmwrap' in logger_name.lower() or 'gemini' in message or 'openai' in message):
                match = self.llm_request_pattern.search(message)
                if match:
                    request_id, model, details = match.groups()
                    return {
                        'type': 'llm_request',
                        'request_id': request_id,
                        'model': model,
                        'timestamp': timestamp,
                        'line_num': line_num,
                        'details': details
                    }
            
            # Parse LLM responses
            elif 'CONTINUE' in message and 'content' in message:
                # Extract JSON content from log
                json_start = message.find('{')
                if json_start != -1:
                    try:
                        json_data = json.loads(message[json_start:])
                        if 'content' in json_data and json_data['content']:
                            return {
                                'type': 'llm_response',
                                'response_content': json_data['content'],
                                'timestamp': timestamp,
                                'line_num': line_num,
                                'request_id': json_data.get('call_id', 'unknown')
                            }
                    except json.JSONDecodeError:
                        pass
            
            # Parse tool calls
            elif ('mcp' in logger_name.lower() or 'mcpwrap' in logger_name.lower()) and 'REQUEST' in message:
                match = self.tool_call_pattern.search(message)
                if match:
                    request_id, tool_name, args = match.groups()
                    return {
                        'type': 'tool_call',
                        'request_id': request_id,
                        'tool_name': tool_name,
                        'args': args,
                        'timestamp': timestamp,
                        'line_num': line_num
                    }
            
            # Parse tool results
            elif ('mcp' in logger_name.lower() or 'mcpwrap' in logger_name.lower()) and ('SUCCESS' in message or 'ERROR' in message):
                match = self.tool_result_pattern.search(message)
                if match:
                    request_id, tool_name, result = match.groups()
                    success = 'SUCCESS' in message
                    return {
                        'type': 'tool_result',
                        'request_id': request_id,
                        'tool_name': tool_name,
                        'success': success,
                        'result': result,
                        'timestamp': timestamp,
                        'line_num': line_num
                    }
        
        except Exception as e:
            logger.debug(f"Failed to parse line {line_num}: {e}")
        
        return None

class TruthCliAnalyzer:
    """CLI analyzer for truth detection on log traces."""
    
    def __init__(self, truth_dir: Path = None):
        self.parser = LogTraceParser()
        self.tracker = TruthViolationTracker(truth_dir)
        self.enforcer = TruthEnforcer(truth_dir)
    
    def analyze_log_file(self, log_file: Path, output_format: str = 'text') -> Dict[str, Any]:
        """Analyze a log file for truth violations."""
        logger.info(f"Analyzing log file: {log_file}")
        
        # Parse log file
        interactions = self.parser.parse_log_file(log_file)
        if not interactions:
            return {
                'status': 'error',
                'message': 'No interactions found in log file',
                'violations': []
            }
        
        # Analyze each interaction
        all_violations = []
        analysis_results = []
        
        for i, interaction in enumerate(interactions):
            if 'response_content' not in interaction:
                continue
            
            # Build context for violation detection
            context = self._build_context_from_interaction(interaction)
            
            # Detect violations
            violations = self.tracker.detect_violations(
                interaction['response_content'], 
                context
            )
            
            # Store results
            interaction_result = {
                'interaction_id': i + 1,
                'request_id': interaction.get('request_id', 'unknown'),
                'timestamp': interaction.get('timestamp', 'unknown'),
                'line_num': interaction.get('line_num', 0),
                'model': interaction.get('model', 'unknown'),
                'response_content': interaction['response_content'],
                'available_tools': context.get('available_tools', []),
                'called_tools': context.get('called_tools', []),
                'violations': []
            }
            
            for violation in violations:
                violation_info = {
                    'violation_id': violation.violation_id,
                    'type': violation.violation_type.value,
                    'severity': violation.severity,
                    'confidence': violation.confidence,
                    'claimed_actions': violation.claimed_actions,
                    'actual_actions': violation.actual_actions,
                    'analysis_notes': violation.analysis_notes
                }
                interaction_result['violations'].append(violation_info)
                all_violations.append(violation)
            
            analysis_results.append(interaction_result)
        
        # Generate summary
        summary = self._generate_summary(all_violations, len(interactions))
        
        result = {
            'status': 'success',
            'summary': summary,
            'interactions': analysis_results,
            'violations': [
                {
                    'violation_id': v.violation_id,
                    'type': v.violation_type.value,
                    'severity': v.severity,
                    'confidence': v.confidence,
                    'request_id': v.request_id,
                    'claimed_actions': v.claimed_actions,
                    'actual_actions': v.actual_actions,
                    'analysis_notes': v.analysis_notes
                }
                for v in all_violations
            ]
        }
        
        return result
    
    def _build_context_from_interaction(self, interaction: Dict[str, Any]) -> Dict[str, Any]:
        """Build violation detection context from parsed interaction."""
        # Extract called tools from tool_calls
        called_tools = []
        tool_results = {}
        available_tools = set()
        
        for tool_call in interaction.get('tool_calls', []):
            tool_name = tool_call.get('tool_name')
            if tool_name:
                called_tools.append(tool_name)
                available_tools.add(tool_name)
        
        # Extract tool results
        for tool_name, tool_result in interaction.get('tool_results', {}).items():
            tool_results[tool_name] = {
                'success': tool_result.get('success', True),
                'result': tool_result.get('result', ''),
                'error': None if tool_result.get('success', True) else tool_result.get('result', 'Unknown error')
            }
            available_tools.add(tool_name)
        
        # Add common tools that might be available
        available_tools.update(['echostring', 'get_time', 'weather', 'exa_search'])
        
        return {
            'available_tools': list(available_tools),
            'called_tools': called_tools,
            'tool_results': tool_results,
            'request_id': interaction.get('request_id', 'cli_analysis'),
            'conversation_id': 'cli_analysis',
            'truth_prompts': {},
            'prompt_versions': {}
        }
    
    def _generate_summary(self, violations: List, total_interactions: int) -> Dict[str, Any]:
        """Generate analysis summary."""
        violation_counts = {}
        severity_counts = {}
        
        for violation in violations:
            vtype = violation.violation_type.value
            violation_counts[vtype] = violation_counts.get(vtype, 0) + 1
            severity_counts[violation.severity] = severity_counts.get(violation.severity, 0) + 1
        
        return {
            'total_interactions': total_interactions,
            'interactions_with_violations': len([v for v in violations]),
            'total_violations': len(violations),
            'violation_rate': len(violations) / total_interactions if total_interactions > 0 else 0,
            'violations_by_type': violation_counts,
            'violations_by_severity': severity_counts,
            'most_common_violation': max(violation_counts.items(), key=lambda x: x[1])[0] if violation_counts else None
        }

def print_text_report(analysis_result: Dict[str, Any]):
    """Print analysis results in human-readable format."""
    print("=" * 80)
    print("TRUTH VIOLATION ANALYSIS REPORT")
    print("=" * 80)
    
    if analysis_result['status'] != 'success':
        print(f"❌ ERROR: {analysis_result['message']}")
        return
    
    summary = analysis_result['summary']
    violations = analysis_result['violations']
    
    # Summary
    print(f"\n📊 SUMMARY:")
    print(f"   Total Interactions: {summary['total_interactions']}")
    print(f"   Total Violations: {summary['total_violations']}")
    print(f"   Violation Rate: {summary['violation_rate']:.1%}")
    
    if summary['most_common_violation']:
        print(f"   Most Common: {summary['most_common_violation']}")
    
    # Violations by type
    if summary['violations_by_type']:
        print(f"\n🔍 VIOLATIONS BY TYPE:")
        for vtype, count in summary['violations_by_type'].items():
            print(f"   {vtype}: {count}")
    
    # Violations by severity  
    if summary['violations_by_severity']:
        print(f"\n⚠️  VIOLATIONS BY SEVERITY:")
        for severity, count in summary['violations_by_severity'].items():
            emoji = {'low': '🟡', 'medium': '🟠', 'high': '🔴', 'critical': '💥'}.get(severity, '⚪')
            print(f"   {emoji} {severity}: {count}")
    
    # Detailed violations
    if violations:
        print(f"\n🚨 DETAILED VIOLATIONS:")
        print("-" * 80)
        
        for i, violation in enumerate(violations, 1):
            severity_emoji = {'low': '🟡', 'medium': '🟠', 'high': '🔴', 'critical': '💥'}.get(violation['severity'], '⚪')
            
            print(f"\n{i}. {severity_emoji} {violation['type'].upper()} (Confidence: {violation['confidence']:.1%})")
            print(f"   Request ID: {violation['request_id']}")
            print(f"   Claimed: {', '.join(violation['claimed_actions'])}")
            print(f"   Actually: {', '.join(violation['actual_actions'])}")
            print(f"   Analysis: {violation['analysis_notes']}")
    
    else:
        print(f"\n✅ No truth violations detected!")
    
    print("=" * 80)

def print_json_report(analysis_result: Dict[str, Any]):
    """Print analysis results in JSON format."""
    print(json.dumps(analysis_result, indent=2))

def main():
    """Main CLI entry point."""
    parser = argparse.ArgumentParser(
        description="Analyze log trace files for LLM truth violations",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Analyze LLM logs for truth violations
  python -m core.llm_truth.truth_cli /tmp/gaia_logs/ceto/llmwrap.log
  
  # Analyze with JSON output
  python -m core.llm_truth.truth_cli /tmp/gaia_logs/ceto/llmwrap.log --format json
  
  # Analyze specific interaction by grep
  grep "762b9e41-761d-4e87-ad1d-9f4312f1effe" /tmp/gaia_logs/ceto/*.log | python -m core.llm_truth.truth_cli --stdin
        """
    )
    
    parser.add_argument('log_file', nargs='?', type=Path, 
                       help='Path to log file to analyze')
    parser.add_argument('--stdin', action='store_true',
                       help='Read log data from stdin instead of file')
    parser.add_argument('--format', choices=['text', 'json'], default='text',
                       help='Output format (default: text)')
    parser.add_argument('--truth-dir', type=Path,
                       help='Directory for truth system data (default: auto)')
    parser.add_argument('--verbose', '-v', action='store_true',
                       help='Enable verbose logging')
    
    args = parser.parse_args()
    
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # Input validation
    if not args.stdin and not args.log_file:
        parser.error("Must specify log_file or --stdin")
    
    if not args.stdin and not args.log_file.exists():
        parser.error(f"Log file not found: {args.log_file}")
    
    try:
        # Initialize analyzer
        analyzer = TruthCliAnalyzer(args.truth_dir)
        
        if args.stdin:
            # Create temporary file from stdin
            import tempfile
            with tempfile.NamedTemporaryFile(mode='w+', suffix='.log', delete=False) as f:
                f.write(sys.stdin.read())
                temp_log_file = Path(f.name)
            
            try:
                result = analyzer.analyze_log_file(temp_log_file, args.format)
            finally:
                temp_log_file.unlink()  # Clean up
        else:
            result = analyzer.analyze_log_file(args.log_file, args.format)
        
        # Output results
        if args.format == 'json':
            print_json_report(result)
        else:
            print_text_report(result)
        
        # Exit code based on violations found
        if result['status'] == 'success':
            violations = result.get('violations', [])
            high_severity = [v for v in violations if v['severity'] in ['high', 'critical']]
            sys.exit(1 if high_severity else 0)
        else:
            sys.exit(2)  # Error in analysis
    
    except KeyboardInterrupt:
        print("\n❌ Analysis interrupted by user")
        sys.exit(130)
    except Exception as e:
        logger.error(f"Analysis failed: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        sys.exit(1)

if __name__ == '__main__':
    main()