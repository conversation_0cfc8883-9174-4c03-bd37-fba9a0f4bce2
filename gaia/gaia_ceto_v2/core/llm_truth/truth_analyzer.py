"""
Truth Analyzer - Analyzes truth violation patterns and recommends fixes.
"""

import json
import logging
from pathlib import Path
from typing import Dict, List, Any, Tuple, Optional
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
from collections import Counter, defaultdict

from .truth_tracker import TruthViolation, ViolationType, ViolationPattern

logger = logging.getLogger(__name__)

@dataclass
class AnalysisRecommendation:
    """A recommendation for improving truth enforcement."""
    recommendation_id: str
    violation_pattern: str
    recommended_action: str
    confidence: float
    prompt_changes: Dict[str, str]  # prompt_type -> suggested content
    estimated_effectiveness: float
    priority: str  # low, medium, high, critical
    created_at: str

@dataclass
class TruthEffectivenessReport:
    """Report on truth enforcement effectiveness."""
    report_id: str
    analysis_period: str
    total_violations: int
    violations_by_type: Dict[str, int]
    violations_by_severity: Dict[str, int]
    prompt_effectiveness: Dict[str, float]
    trending_patterns: List[str]
    recommendations: List[AnalysisRecommendation]
    overall_score: float  # 0-100, higher is better
    created_at: str

class TruthAnalyzer:
    """Analyzes truth violations and generates recommendations."""
    
    def __init__(self, truth_dir: Path = None):
        """Initialize truth analyzer."""
        self.truth_dir = truth_dir or Path(__file__).parent / "truth_data"
        self.truth_dir.mkdir(parents=True, exist_ok=True)
        
        self.reports_file = self.truth_dir / "analysis_reports.json"
        self.recommendations_file = self.truth_dir / "recommendations.json"
        
        # Load existing data
        self.reports: Dict[str, TruthEffectivenessReport] = self._load_reports()
        self.recommendations: Dict[str, AnalysisRecommendation] = self._load_recommendations()
        
        logger.info(f"TruthAnalyzer initialized: {len(self.reports)} reports, {len(self.recommendations)} recommendations")
    
    def _load_reports(self) -> Dict[str, TruthEffectivenessReport]:
        """Load analysis reports from storage."""
        if not self.reports_file.exists():
            return {}
        
        try:
            with open(self.reports_file, 'r') as f:
                data = json.load(f)
            
            reports = {}
            for rid, report_data in data.items():
                # Convert recommendations back to objects
                recommendations = []
                for rec_data in report_data.get('recommendations', []):
                    recommendations.append(AnalysisRecommendation(**rec_data))
                report_data['recommendations'] = recommendations
                
                reports[rid] = TruthEffectivenessReport(**report_data)
            
            return reports
        except Exception as e:
            logger.error(f"Failed to load analysis reports: {e}")
            return {}
    
    def _load_recommendations(self) -> Dict[str, AnalysisRecommendation]:
        """Load recommendations from storage."""
        if not self.recommendations_file.exists():
            return {}
        
        try:
            with open(self.recommendations_file, 'r') as f:
                data = json.load(f)
            
            recommendations = {}
            for rid, rec_data in data.items():
                recommendations[rid] = AnalysisRecommendation(**rec_data)
            
            return recommendations
        except Exception as e:
            logger.error(f"Failed to load recommendations: {e}")
            return {}
    
    def _save_reports(self):
        """Save analysis reports to storage."""
        try:
            data = {}
            for rid, report in self.reports.items():
                report_dict = asdict(report)
                # Convert recommendations to dicts
                report_dict['recommendations'] = [asdict(rec) for rec in report.recommendations]
                data[rid] = report_dict
            
            with open(self.reports_file, 'w') as f:
                json.dump(data, f, indent=2)
        except Exception as e:
            logger.error(f"Failed to save analysis reports: {e}")
    
    def _save_recommendations(self):
        """Save recommendations to storage."""
        try:
            data = {rid: asdict(rec) for rid, rec in self.recommendations.items()}
            with open(self.recommendations_file, 'w') as f:
                json.dump(data, f, indent=2)
        except Exception as e:
            logger.error(f"Failed to save recommendations: {e}")
    
    def analyze_violations(self, violations: List[TruthViolation], 
                         prompt_versions: Dict[str, str]) -> TruthEffectivenessReport:
        """Analyze a set of violations and generate effectiveness report."""
        report_id = f"analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        # Filter out false positives
        valid_violations = [v for v in violations if not v.false_positive]
        
        # Basic statistics
        total_violations = len(valid_violations)
        violations_by_type = self._count_violations_by_type(valid_violations)
        violations_by_severity = self._count_violations_by_severity(valid_violations)
        
        # Analyze prompt effectiveness
        prompt_effectiveness = self._analyze_prompt_effectiveness(valid_violations, prompt_versions)
        
        # Find trending patterns
        trending_patterns = self._find_trending_patterns(valid_violations)
        
        # Generate recommendations
        recommendations = self._generate_recommendations(valid_violations, trending_patterns, prompt_effectiveness)
        
        # Calculate overall score
        overall_score = self._calculate_overall_score(total_violations, violations_by_severity, prompt_effectiveness)
        
        # Create report
        report = TruthEffectivenessReport(
            report_id=report_id,
            analysis_period=f"Analysis of {total_violations} violations",
            total_violations=total_violations,
            violations_by_type=violations_by_type,
            violations_by_severity=violations_by_severity,
            prompt_effectiveness=prompt_effectiveness,
            trending_patterns=trending_patterns,
            recommendations=recommendations,
            overall_score=overall_score,
            created_at=datetime.now().isoformat()
        )
        
        # Store report
        self.reports[report_id] = report
        self._save_reports()
        
        # Store recommendations
        for rec in recommendations:
            self.recommendations[rec.recommendation_id] = rec
        self._save_recommendations()
        
        logger.info(f"Generated truth analysis report {report_id}: {total_violations} violations, score {overall_score:.1f}")
        
        return report
    
    def _count_violations_by_type(self, violations: List[TruthViolation]) -> Dict[str, int]:
        """Count violations by type."""
        return dict(Counter(v.violation_type.value for v in violations))
    
    def _count_violations_by_severity(self, violations: List[TruthViolation]) -> Dict[str, int]:
        """Count violations by severity."""
        return dict(Counter(v.severity for v in violations))
    
    def _analyze_prompt_effectiveness(self, violations: List[TruthViolation], 
                                    prompt_versions: Dict[str, str]) -> Dict[str, float]:
        """Analyze effectiveness of different prompt versions."""
        effectiveness = {}
        
        # Group violations by prompt versions used
        violations_by_prompts = defaultdict(list)
        for violation in violations:
            for prompt_type, version_id in violation.prompt_versions.items():
                violations_by_prompts[f"{prompt_type}:{version_id}"].append(violation)
        
        # Calculate effectiveness scores (lower violations = higher effectiveness)
        for prompt_key, prompt_violations in violations_by_prompts.items():
            # Simple effectiveness calculation: inverse of violation rate
            # This would be more sophisticated in a real system with baseline measurements
            violation_count = len(prompt_violations)
            severity_weight = sum(
                {'low': 1, 'medium': 2, 'high': 3, 'critical': 4}.get(v.severity, 2) 
                for v in prompt_violations
            )
            
            # Effectiveness score: higher is better, scaled 0-100
            if violation_count == 0:
                effectiveness[prompt_key] = 100.0
            else:
                # Penalize both count and severity
                penalty = violation_count + (severity_weight / violation_count)
                effectiveness[prompt_key] = max(0.0, 100.0 - (penalty * 10))
        
        return effectiveness
    
    def _find_trending_patterns(self, violations: List[TruthViolation]) -> List[str]:
        """Identify trending violation patterns."""
        patterns = []
        
        # Analyze common phrases in violations
        all_responses = [v.llm_response for v in violations]
        
        # Look for repeated patterns
        pattern_counts = defaultdict(int)
        for response in all_responses:
            # Simple pattern detection - look for common phrases
            words = response.lower().split()
            for i in range(len(words) - 2):
                phrase = " ".join(words[i:i+3])
                if any(keyword in phrase for keyword in ['called', 'used', 'found', 'retrieved', 'successfully']):
                    pattern_counts[phrase] += 1
        
        # Return patterns that appear multiple times
        trending = [pattern for pattern, count in pattern_counts.items() if count >= 2]
        return trending[:10]  # Top 10 patterns
    
    def _generate_recommendations(self, violations: List[TruthViolation], 
                                trending_patterns: List[str],
                                prompt_effectiveness: Dict[str, float]) -> List[AnalysisRecommendation]:
        """Generate recommendations based on violation analysis."""
        recommendations = []
        
        # Recommendation 1: Address most common violation types
        violation_types = self._count_violations_by_type(violations)
        if violation_types:
            most_common_type = max(violation_types.items(), key=lambda x: x[1])
            
            rec_id = f"rec_common_type_{datetime.now().timestamp()}"
            recommendation = self._create_type_specific_recommendation(
                rec_id, most_common_type[0], most_common_type[1]
            )
            recommendations.append(recommendation)
        
        # Recommendation 2: Improve low-effectiveness prompts
        low_effectiveness_prompts = [
            prompt for prompt, score in prompt_effectiveness.items() 
            if score < 70.0
        ]
        
        if low_effectiveness_prompts:
            rec_id = f"rec_improve_prompts_{datetime.now().timestamp()}"
            recommendation = self._create_prompt_improvement_recommendation(
                rec_id, low_effectiveness_prompts
            )
            recommendations.append(recommendation)
        
        # Recommendation 3: Address trending patterns
        if trending_patterns:
            rec_id = f"rec_trending_patterns_{datetime.now().timestamp()}"
            recommendation = self._create_pattern_specific_recommendation(
                rec_id, trending_patterns
            )
            recommendations.append(recommendation)
        
        return recommendations
    
    def _create_type_specific_recommendation(self, rec_id: str, violation_type: str, 
                                          count: int) -> AnalysisRecommendation:
        """Create recommendation for specific violation type."""
        type_specific_prompts = {
            ViolationType.FAKE_TOOL_CALL.value: {
                "system": "CRITICAL: Before claiming you used any tool, double-check your actual tool calls. Only describe tools you actually invoked, not tools you considered using.",
                "tool_reminder": "VERIFICATION REQUIRED: Did you actually call the tools you're about to mention? List only tools that were genuinely executed."
            },
            ViolationType.FAKE_TOOL_SUCCESS.value: {
                "system": "IMPORTANT: If a tool call failed or returned an error, you must acknowledge the failure. Never claim success when there was an error.",
                "fact_check": "Before responding: Did all tool calls succeed? If any failed, mention the failure explicitly."
            },
            ViolationType.FAKE_DATA_ACCESS.value: {
                "system": "You can only reference data that was explicitly provided or retrieved through actual tool calls. Do not claim to access databases, files, or systems without using the appropriate tools.",
                "tool_reminder": "Data verification: Are you referencing data from actual tool results, or making assumptions about data availability?"
            }
        }
        
        prompt_changes = type_specific_prompts.get(violation_type, {
            "system": f"Address {violation_type} violations by being more careful about accuracy."
        })
        
        return AnalysisRecommendation(
            recommendation_id=rec_id,
            violation_pattern=f"High frequency of {violation_type} violations ({count} instances)",
            recommended_action=f"Strengthen truth enforcement for {violation_type} violations",
            confidence=0.8,
            prompt_changes=prompt_changes,
            estimated_effectiveness=0.7,
            priority="high" if count > 5 else "medium",
            created_at=datetime.now().isoformat()
        )
    
    def _create_prompt_improvement_recommendation(self, rec_id: str, 
                                                low_effectiveness_prompts: List[str]) -> AnalysisRecommendation:
        """Create recommendation for improving low-effectiveness prompts."""
        return AnalysisRecommendation(
            recommendation_id=rec_id,
            violation_pattern=f"Low effectiveness prompts: {', '.join(low_effectiveness_prompts)}",
            recommended_action="Revise and strengthen underperforming truth enforcement prompts",
            confidence=0.9,
            prompt_changes={
                "system": "Enhanced system prompt with stronger truth enforcement and explicit verification requirements",
                "tool_reminder": "More specific tool verification instructions with examples of proper vs improper claims"
            },
            estimated_effectiveness=0.8,
            priority="high",
            created_at=datetime.now().isoformat()
        )
    
    def _create_pattern_specific_recommendation(self, rec_id: str, 
                                              trending_patterns: List[str]) -> AnalysisRecommendation:
        """Create recommendation for addressing trending patterns."""
        return AnalysisRecommendation(
            recommendation_id=rec_id,
            violation_pattern=f"Trending violation patterns: {', '.join(trending_patterns[:3])}",
            recommended_action="Add specific pattern detection and prevention measures",
            confidence=0.7,
            prompt_changes={
                "system": f"Avoid these specific problematic phrases: {', '.join(trending_patterns[:3])}. Instead, use precise language about what actually occurred.",
                "fact_check": "Pattern check: Does your response contain any of the common problematic phrases that lead to truth violations?"
            },
            estimated_effectiveness=0.6,
            priority="medium",
            created_at=datetime.now().isoformat()
        )
    
    def _calculate_overall_score(self, total_violations: int, 
                               violations_by_severity: Dict[str, int],
                               prompt_effectiveness: Dict[str, float]) -> float:
        """Calculate overall truth enforcement effectiveness score."""
        if total_violations == 0:
            return 100.0
        
        # Penalty based on violation count and severity
        severity_weights = {'low': 1, 'medium': 2, 'high': 4, 'critical': 8}
        weighted_violations = sum(
            violations_by_severity.get(severity, 0) * weight 
            for severity, weight in severity_weights.items()
        )
        
        # Base score penalty
        violation_penalty = min(80, weighted_violations * 5)  # Max 80 point penalty
        
        # Prompt effectiveness bonus
        if prompt_effectiveness:
            avg_effectiveness = sum(prompt_effectiveness.values()) / len(prompt_effectiveness)
            effectiveness_bonus = (avg_effectiveness - 50) / 10  # Convert to ±5 point range
        else:
            effectiveness_bonus = 0
        
        # Final score: 100 - penalty + bonus, clamped to 0-100
        score = max(0.0, min(100.0, 100.0 - violation_penalty + effectiveness_bonus))
        
        return round(score, 1)
    
    def get_latest_report(self) -> Optional[TruthEffectivenessReport]:
        """Get the most recent analysis report."""
        if not self.reports:
            return None
        
        latest_report_id = max(self.reports.keys())
        return self.reports[latest_report_id]
    
    def get_recommendations_by_priority(self, priority: str = None) -> List[AnalysisRecommendation]:
        """Get recommendations filtered by priority."""
        recommendations = list(self.recommendations.values())
        
        if priority:
            recommendations = [r for r in recommendations if r.priority == priority]
        
        # Sort by confidence descending
        recommendations.sort(key=lambda r: r.confidence, reverse=True)
        
        return recommendations
    
    def export_analysis_summary(self) -> Dict[str, Any]:
        """Export comprehensive analysis summary."""
        latest_report = self.get_latest_report()
        high_priority_recs = self.get_recommendations_by_priority("high")
        
        return {
            "summary": {
                "total_reports": len(self.reports),
                "total_recommendations": len(self.recommendations),
                "latest_analysis": latest_report.report_id if latest_report else None,
                "overall_score": latest_report.overall_score if latest_report else None
            },
            "latest_report": asdict(latest_report) if latest_report else None,
            "high_priority_recommendations": [asdict(rec) for rec in high_priority_recs],
            "generated_at": datetime.now().isoformat()
        }