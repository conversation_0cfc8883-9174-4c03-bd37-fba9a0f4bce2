"""
Level 0008c: Truth LLM call extensions

Tracks and prevents "truth-breaking" statements by LLMs where they claim
to have performed actions (like tool calls) that never actually occurred.

Key Components:
- TruthEnforcer: Manages truth-enforcing prompt fragments with versioning
- TruthViolationTracker: Detects and logs truth violations
- TruthAnalyzer: Analyzes patterns and recommends fixes
"""

from .truth_enforcer import TruthEnforcer
from .truth_tracker import TruthViolationTracker
from .truth_analyzer import TruthAnalyzer
from .interference_detector import InterferenceDetector

__all__ = ['TruthEnforcer', 'TruthViolationTracker', 'TruthAnalyzer', 'InterferenceDetector']