"""
Truth Violation Tracker - Detects and logs cases where LLM makes fictitious claims.
"""

import json
import logging
import re
from pathlib import Path
from typing import Dict, List, Optional, Any, Set
from dataclasses import dataclass, asdict
from datetime import datetime
from enum import Enum

logger = logging.getLogger(__name__)

class ViolationType(Enum):
    """Types of truth violations."""
    FAKE_TOOL_CALL = "fake_tool_call"           # Claims tool was called but wasn't
    FAKE_TOOL_SUCCESS = "fake_tool_success"     # Claims tool succeeded but failed
    FAKE_DATA_ACCESS = "fake_data_access"       # Claims to access data not available
    FAKE_CAPABILITY = "fake_capability"         # Claims capabilities not possessed
    HALLUCINATED_RESULT = "hallucinated_result" # Makes up results
    OTHER = "other"

@dataclass
class TruthViolation:
    """A detected truth violation incident."""
    violation_id: str
    violation_type: ViolationType
    detected_at: str
    request_id: str
    conversation_id: str
    
    # LLM Response Analysis
    llm_response: str
    claimed_actions: List[str]      # What LLM claimed to do
    actual_actions: List[str]       # What actually happened
    
    # Context Information
    available_tools: List[str]      # Tools that were available
    called_tools: List[str]         # Tools that were actually called
    tool_results: Dict[str, Any]    # Actual tool results
    
    # Truth Enforcement Context
    active_truth_prompts: Dict[str, str]  # Which truth prompts were active
    prompt_versions: Dict[str, str]       # Version IDs of prompts used
    
    # Analysis
    severity: str = "medium"        # low, medium, high, critical
    confidence: float = 0.8         # Confidence in violation detection
    analysis_notes: str = ""        # Human or automated analysis
    false_positive: bool = False    # Mark if determined to be false positive

@dataclass 
class ViolationPattern:
    """Pattern analysis for truth violations."""
    pattern_id: str
    violation_types: List[ViolationType]
    common_phrases: List[str]
    trigger_conditions: List[str]
    frequency: int
    effectiveness_impact: Dict[str, float]  # Impact on prompt effectiveness

class TruthViolationTracker:
    """Tracks and analyzes truth violations."""
    
    def __init__(self, truth_dir: Path = None):
        """Initialize violation tracker."""
        self.truth_dir = truth_dir or Path(__file__).parent / "truth_data"
        self.truth_dir.mkdir(parents=True, exist_ok=True)
        
        self.violations_file = self.truth_dir / "violations.json"
        self.patterns_file = self.truth_dir / "patterns.json"
        
        # Load existing data
        self.violations: Dict[str, TruthViolation] = self._load_violations()
        self.patterns: Dict[str, ViolationPattern] = self._load_patterns()
        
        # Detection patterns
        self._init_detection_patterns()
        
        logger.info(f"TruthViolationTracker initialized: {len(self.violations)} violations tracked")
    
    def _load_violations(self) -> Dict[str, TruthViolation]:
        """Load violations from storage."""
        if not self.violations_file.exists():
            return {}
        
        try:
            with open(self.violations_file, 'r') as f:
                data = json.load(f)
            
            violations = {}
            for vid, violation_data in data.items():
                # Convert violation_type back to enum
                violation_data['violation_type'] = ViolationType(violation_data['violation_type'])
                violations[vid] = TruthViolation(**violation_data)
            
            return violations
        except Exception as e:
            logger.error(f"Failed to load violations: {e}")
            return {}
    
    def _load_patterns(self) -> Dict[str, ViolationPattern]:
        """Load violation patterns from storage."""
        if not self.patterns_file.exists():
            return {}
        
        try:
            with open(self.patterns_file, 'r') as f:
                data = json.load(f)
            
            patterns = {}
            for pid, pattern_data in data.items():
                # Convert violation_types back to enums
                pattern_data['violation_types'] = [ViolationType(vt) for vt in pattern_data['violation_types']]
                patterns[pid] = ViolationPattern(**pattern_data)
            
            return patterns
        except Exception as e:
            logger.error(f"Failed to load patterns: {e}")
            return {}
    
    def _save_violations(self):
        """Save violations to storage."""
        try:
            data = {}
            for vid, violation in self.violations.items():
                violation_dict = asdict(violation)
                # Convert enum to string for JSON serialization
                violation_dict['violation_type'] = violation.violation_type.value
                data[vid] = violation_dict
            
            with open(self.violations_file, 'w') as f:
                json.dump(data, f, indent=2)
        except Exception as e:
            logger.error(f"Failed to save violations: {e}")
    
    def _save_patterns(self):
        """Save patterns to storage."""
        try:
            data = {}
            for pid, pattern in self.patterns.items():
                pattern_dict = asdict(pattern)
                # Convert enums to strings
                pattern_dict['violation_types'] = [vt.value for vt in pattern.violation_types]
                data[pid] = pattern_dict
            
            with open(self.patterns_file, 'w') as f:
                json.dump(data, f, indent=2)
        except Exception as e:
            logger.error(f"Failed to save patterns: {e}")
    
    def _init_detection_patterns(self):
        """Initialize detection patterns for common violations."""
        self.detection_patterns = {
            ViolationType.FAKE_TOOL_CALL: [
                r"I (?:called|used|executed|ran) the (\w+) tool",
                r"(?:Using|With) the (\w+) tool, I (?:found|got|retrieved)",
                r"The (\w+) tool (?:returned|showed|found)",
                r"After (?:calling|using) (\w+), I can see"
            ],
            ViolationType.FAKE_TOOL_SUCCESS: [
                r"The (\w+) tool successfully (?:returned|found|completed)",
                r"I successfully (?:called|used|executed) (\w+) and (?:got|found|retrieved)",
                r"(?:Using|With) (\w+), I was able to (?:find|get|retrieve)"
            ],
            ViolationType.FAKE_DATA_ACCESS: [
                r"I can see (?:in|from) the (?:database|file|system)",
                r"Looking at the (?:current|latest|recent) data",
                r"Based on the (?:file|database|system) contents"
            ],
            ViolationType.FAKE_CAPABILITY: [
                r"I (?:can|will) (?:access|connect to|download from)",
                r"Let me (?:check|access|look up) the (?:internet|database|file)",
                r"I'll (?:search|browse|access) (?:online|the web|external)"
            ]
        }
    
    def detect_violations(self, llm_response: str, context: Dict[str, Any]) -> List[TruthViolation]:
        """Detect potential truth violations in LLM response."""
        violations = []
        
        # Extract context information
        available_tools = context.get('available_tools', [])
        called_tools = context.get('called_tools', [])
        tool_results = context.get('tool_results', {})
        request_id = context.get('request_id', 'unknown')
        conversation_id = context.get('conversation_id', 'unknown')
        truth_prompts = context.get('truth_prompts', {})
        prompt_versions = context.get('prompt_versions', {})
        
        # Check for fake tool calls
        violations.extend(self._detect_fake_tool_calls(
            llm_response, available_tools, called_tools, 
            request_id, conversation_id, truth_prompts, prompt_versions
        ))
        
        # Check for fake tool success claims
        violations.extend(self._detect_fake_tool_success(
            llm_response, called_tools, tool_results,
            request_id, conversation_id, truth_prompts, prompt_versions
        ))
        
        # Check for fake data access claims
        violations.extend(self._detect_fake_data_access(
            llm_response, context,
            request_id, conversation_id, truth_prompts, prompt_versions
        ))
        
        # Check for fake capability claims
        violations.extend(self._detect_fake_capabilities(
            llm_response, context,
            request_id, conversation_id, truth_prompts, prompt_versions
        ))
        
        # Save detected violations
        for violation in violations:
            self.violations[violation.violation_id] = violation
        
        if violations:
            self._save_violations()
            logger.warning(f"Detected {len(violations)} truth violations in response")
        
        return violations
    
    def _detect_fake_tool_calls(self, response: str, available_tools: List[str], 
                               called_tools: List[str], request_id: str, conversation_id: str,
                               truth_prompts: Dict[str, str], prompt_versions: Dict[str, str]) -> List[TruthViolation]:
        """Detect claims of tool calls that didn't happen."""
        violations = []
        
        for pattern in self.detection_patterns[ViolationType.FAKE_TOOL_CALL]:
            matches = re.finditer(pattern, response, re.IGNORECASE)
            for match in matches:
                claimed_tool = match.group(1) if len(match.groups()) > 0 else "unknown"
                
                # Check if tool was actually called
                if claimed_tool in available_tools and claimed_tool not in called_tools:
                    violation_id = f"fake_call_{request_id}_{claimed_tool}_{datetime.now().timestamp()}"
                    
                    violation = TruthViolation(
                        violation_id=violation_id,
                        violation_type=ViolationType.FAKE_TOOL_CALL,
                        detected_at=datetime.now().isoformat(),
                        request_id=request_id,
                        conversation_id=conversation_id,
                        llm_response=response,
                        claimed_actions=[f"Called {claimed_tool} tool"],
                        actual_actions=called_tools,
                        available_tools=available_tools,
                        called_tools=called_tools,
                        tool_results={},
                        active_truth_prompts=truth_prompts,
                        prompt_versions=prompt_versions,
                        severity="high",
                        confidence=0.9,
                        analysis_notes=f"LLM claimed to call {claimed_tool} but tool was never invoked"
                    )
                    violations.append(violation)
        
        return violations
    
    def _detect_fake_tool_success(self, response: str, called_tools: List[str],
                                 tool_results: Dict[str, Any], request_id: str, conversation_id: str,
                                 truth_prompts: Dict[str, str], prompt_versions: Dict[str, str]) -> List[TruthViolation]:
        """Detect claims of successful tool execution when tools failed."""
        violations = []
        
        for pattern in self.detection_patterns[ViolationType.FAKE_TOOL_SUCCESS]:
            matches = re.finditer(pattern, response, re.IGNORECASE)
            for match in matches:
                claimed_tool = match.group(1) if len(match.groups()) > 0 else "unknown"
                
                # Check if tool was called but failed
                if claimed_tool in called_tools:
                    tool_result = tool_results.get(claimed_tool, {})
                    if not tool_result.get('success', True):  # Tool failed
                        violation_id = f"fake_success_{request_id}_{claimed_tool}_{datetime.now().timestamp()}"
                        
                        violation = TruthViolation(
                            violation_id=violation_id,
                            violation_type=ViolationType.FAKE_TOOL_SUCCESS,
                            detected_at=datetime.now().isoformat(),
                            request_id=request_id,
                            conversation_id=conversation_id,
                            llm_response=response,
                            claimed_actions=[f"Successfully executed {claimed_tool}"],
                            actual_actions=[f"Failed to execute {claimed_tool}"],
                            available_tools=[],
                            called_tools=called_tools,
                            tool_results=tool_results,
                            active_truth_prompts=truth_prompts,
                            prompt_versions=prompt_versions,
                            severity="high",
                            confidence=0.8,
                            analysis_notes=f"LLM claimed {claimed_tool} succeeded but it failed: {tool_result.get('error', 'Unknown error')}"
                        )
                        violations.append(violation)
        
        return violations
    
    def _detect_fake_data_access(self, response: str, context: Dict[str, Any],
                                request_id: str, conversation_id: str,
                                truth_prompts: Dict[str, str], prompt_versions: Dict[str, str]) -> List[TruthViolation]:
        """Detect claims of data access without actual access."""
        violations = []
        
        for pattern in self.detection_patterns[ViolationType.FAKE_DATA_ACCESS]:
            if re.search(pattern, response, re.IGNORECASE):
                # Check if any data access tools were actually called
                data_tools = ['file_read', 'database_query', 'api_call', 'web_search']
                called_data_tools = [t for t in context.get('called_tools', []) if t in data_tools]
                
                if not called_data_tools:
                    violation_id = f"fake_data_{request_id}_{datetime.now().timestamp()}"
                    
                    violation = TruthViolation(
                        violation_id=violation_id,
                        violation_type=ViolationType.FAKE_DATA_ACCESS,
                        detected_at=datetime.now().isoformat(),
                        request_id=request_id,
                        conversation_id=conversation_id,
                        llm_response=response,
                        claimed_actions=["Accessed external data"],
                        actual_actions=context.get('called_tools', []),
                        available_tools=context.get('available_tools', []),
                        called_tools=context.get('called_tools', []),
                        tool_results=context.get('tool_results', {}),
                        active_truth_prompts=truth_prompts,
                        prompt_versions=prompt_versions,
                        severity="medium",
                        confidence=0.7,
                        analysis_notes="LLM claimed to access data without calling data access tools"
                    )
                    violations.append(violation)
        
        return violations
    
    def _detect_fake_capabilities(self, response: str, context: Dict[str, Any],
                                 request_id: str, conversation_id: str,
                                 truth_prompts: Dict[str, str], prompt_versions: Dict[str, str]) -> List[TruthViolation]:
        """Detect claims of capabilities that aren't available."""
        violations = []
        
        for pattern in self.detection_patterns[ViolationType.FAKE_CAPABILITY]:
            if re.search(pattern, response, re.IGNORECASE):
                violation_id = f"fake_capability_{request_id}_{datetime.now().timestamp()}"
                
                violation = TruthViolation(
                    violation_id=violation_id,
                    violation_type=ViolationType.FAKE_CAPABILITY,
                    detected_at=datetime.now().isoformat(),
                    request_id=request_id,
                    conversation_id=conversation_id,
                    llm_response=response,
                    claimed_actions=["Claimed unavailable capabilities"],
                    actual_actions=context.get('called_tools', []),
                    available_tools=context.get('available_tools', []),
                    called_tools=context.get('called_tools', []),
                    tool_results=context.get('tool_results', {}),
                    active_truth_prompts=truth_prompts,
                    prompt_versions=prompt_versions,
                    severity="medium",
                    confidence=0.6,
                    analysis_notes="LLM claimed capabilities not available in current context"
                )
                violations.append(violation)
        
        return violations
    
    def mark_false_positive(self, violation_id: str, reason: str = ""):
        """Mark a violation as a false positive."""
        if violation_id in self.violations:
            self.violations[violation_id].false_positive = True
            self.violations[violation_id].analysis_notes += f" [FALSE POSITIVE: {reason}]"
            self._save_violations()
            logger.info(f"Marked violation {violation_id} as false positive")
    
    def get_violation_stats(self) -> Dict[str, Any]:
        """Get violation statistics."""
        violations = [v for v in self.violations.values() if not v.false_positive]
        
        stats = {
            'total_violations': len(violations),
            'by_type': {},
            'by_severity': {},
            'recent_violations': 0,
            'confidence_distribution': {}
        }
        
        # Calculate statistics
        for violation in violations:
            vtype = violation.violation_type.value
            stats['by_type'][vtype] = stats['by_type'].get(vtype, 0) + 1
            stats['by_severity'][violation.severity] = stats['by_severity'].get(violation.severity, 0) + 1
        
        return stats
    
    def get_violations_by_prompt_version(self, version_id: str) -> List[TruthViolation]:
        """Get all violations that occurred with a specific prompt version."""
        return [
            v for v in self.violations.values() 
            if version_id in v.prompt_versions.values() and not v.false_positive
        ]