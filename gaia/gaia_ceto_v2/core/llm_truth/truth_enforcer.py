"""
Truth Enforcer - Manages versioned truth-enforcing prompt fragments.
"""

import json
import logging
from pathlib import Path
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from datetime import datetime

logger = logging.getLogger(__name__)

@dataclass
class TruthPromptVersion:
    """A versioned truth-enforcing prompt fragment."""
    version_id: str
    content: str
    description: str
    created_at: str
    module_file: str
    prompt_type: str  # 'system', 'user_prefix', 'tool_reminder', etc.
    effectiveness_score: Optional[float] = None  # Track how well it works
    active: bool = True

class TruthEnforcer:
    """Manages versioned truth-enforcing prompt fragments."""
    
    def __init__(self, truth_dir: Path = None):
        """Initialize truth enforcer with storage directory."""
        self.truth_dir = truth_dir or Path(__file__).parent / "truth_data"
        self.truth_dir.mkdir(parents=True, exist_ok=True)
        
        self.versions_file = self.truth_dir / "prompt_versions.json"
        self.active_config_file = self.truth_dir / "active_config.json"
        
        # Load existing versions and config
        self.prompt_versions: Dict[str, TruthPromptVersion] = self._load_versions()
        self.active_config: Dict[str, str] = self._load_active_config()
        
        # Initialize default truth prompts if none exist
        if not self.prompt_versions:
            self._create_default_prompts()
            
        logger.info(f"TruthEnforcer initialized: {len(self.prompt_versions)} prompt versions")
    
    def _load_versions(self) -> Dict[str, TruthPromptVersion]:
        """Load prompt versions from storage."""
        if not self.versions_file.exists():
            return {}
        
        try:
            with open(self.versions_file, 'r') as f:
                data = json.load(f)
            
            versions = {}
            for version_id, version_data in data.items():
                versions[version_id] = TruthPromptVersion(**version_data)
            
            return versions
        except Exception as e:
            logger.error(f"Failed to load truth prompt versions: {e}")
            return {}
    
    def _load_active_config(self) -> Dict[str, str]:
        """Load active configuration mapping prompt types to version IDs."""
        if not self.active_config_file.exists():
            return {}
        
        try:
            with open(self.active_config_file, 'r') as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"Failed to load active config: {e}")
            return {}
    
    def _save_versions(self):
        """Save prompt versions to storage."""
        try:
            data = {vid: asdict(version) for vid, version in self.prompt_versions.items()}
            with open(self.versions_file, 'w') as f:
                json.dump(data, f, indent=2)
        except Exception as e:
            logger.error(f"Failed to save prompt versions: {e}")
    
    def _save_active_config(self):
        """Save active configuration."""
        try:
            with open(self.active_config_file, 'w') as f:
                json.dump(self.active_config, f, indent=2)
        except Exception as e:
            logger.error(f"Failed to save active config: {e}")
    
    def _create_default_prompts(self):
        """Create default truth-enforcing prompts."""
        defaults = [
            {
                "version_id": "system_truth_v1.0",
                "content": """CRITICAL: You must NEVER claim to have performed actions you did not actually perform. 
If you did not call a tool, do not say you did. If a tool call failed, acknowledge the failure.
Be precise about what actually happened vs what you intended to do.""",
                "description": "Basic system-level truth enforcement",
                "prompt_type": "system",
                "module_file": "core/llm_truth/truth_enforcer.py"
            },
            {
                "version_id": "tool_reminder_v1.0", 
                "content": """Before responding, verify: Did I actually call the tools I'm about to mention? 
Only describe actions that actually occurred. If a tool wasn't called or failed, state that clearly.""",
                "description": "Pre-response tool verification reminder",
                "prompt_type": "tool_reminder",
                "module_file": "core/llm_truth/truth_enforcer.py"
            },
            {
                "version_id": "fact_check_v1.0",
                "content": """Double-check your response: Are you describing actual events or imagined ones? 
Distinguish clearly between what you attempted vs what actually succeeded.""",
                "description": "Response fact-checking prompt",
                "prompt_type": "fact_check",
                "module_file": "core/llm_truth/truth_enforcer.py"
            }
        ]
        
        for prompt_data in defaults:
            self.add_prompt_version(**prompt_data)
        
        # Set default active configuration
        self.active_config = {
            "system": "system_truth_v1.0",
            "tool_reminder": "tool_reminder_v1.0", 
            "fact_check": "fact_check_v1.0"
        }
        self._save_active_config()
    
    def add_prompt_version(self, version_id: str, content: str, description: str, 
                          prompt_type: str, module_file: str, **kwargs) -> TruthPromptVersion:
        """Add a new prompt version."""
        version = TruthPromptVersion(
            version_id=version_id,
            content=content,
            description=description,
            created_at=datetime.now().isoformat(),
            module_file=module_file,
            prompt_type=prompt_type,
            **kwargs
        )
        
        self.prompt_versions[version_id] = version
        self._save_versions()
        
        logger.info(f"Added truth prompt version: {version_id}")
        return version
    
    def get_active_prompt(self, prompt_type: str) -> Optional[TruthPromptVersion]:
        """Get the currently active prompt for a given type."""
        version_id = self.active_config.get(prompt_type)
        if not version_id:
            return None
        
        version = self.prompt_versions.get(version_id)
        if version and version.active:
            return version
        
        return None
    
    def set_active_prompt(self, prompt_type: str, version_id: str):
        """Set the active prompt version for a type."""
        if version_id not in self.prompt_versions:
            raise ValueError(f"Unknown prompt version: {version_id}")
        
        if not self.prompt_versions[version_id].active:
            raise ValueError(f"Prompt version {version_id} is inactive")
        
        self.active_config[prompt_type] = version_id
        self._save_active_config()
        
        logger.info(f"Set active prompt {prompt_type} -> {version_id}")
    
    def get_all_prompts_for_type(self, prompt_type: str) -> List[TruthPromptVersion]:
        """Get all prompt versions for a given type."""
        return [v for v in self.prompt_versions.values() if v.prompt_type == prompt_type]
    
    def deactivate_prompt(self, version_id: str):
        """Deactivate a prompt version."""
        if version_id in self.prompt_versions:
            self.prompt_versions[version_id].active = False
            self._save_versions()
            
            # Remove from active config if it was active
            for ptype, active_id in list(self.active_config.items()):
                if active_id == version_id:
                    del self.active_config[ptype]
            self._save_active_config()
    
    def update_effectiveness_score(self, version_id: str, score: float):
        """Update effectiveness score for a prompt version."""
        if version_id in self.prompt_versions:
            self.prompt_versions[version_id].effectiveness_score = score
            self._save_versions()
            logger.info(f"Updated effectiveness score for {version_id}: {score}")
    
    def get_enforcement_context(self, context_type: str = "full") -> Dict[str, str]:
        """Get truth enforcement prompts for current context."""
        context = {}
        
        if context_type in ["full", "system"]:
            system_prompt = self.get_active_prompt("system")
            if system_prompt:
                context["system"] = system_prompt.content
        
        if context_type in ["full", "tool"]:
            tool_reminder = self.get_active_prompt("tool_reminder")
            if tool_reminder:
                context["tool_reminder"] = tool_reminder.content
        
        if context_type in ["full", "fact_check"]:
            fact_check = self.get_active_prompt("fact_check")
            if fact_check:
                context["fact_check"] = fact_check.content
        
        return context
    
    def get_version_info(self) -> Dict[str, Any]:
        """Get comprehensive version information."""
        return {
            "total_versions": len(self.prompt_versions),
            "active_config": self.active_config.copy(),
            "versions_by_type": {
                ptype: [v.version_id for v in self.get_all_prompts_for_type(ptype)]
                for ptype in set(v.prompt_type for v in self.prompt_versions.values())
            },
            "effectiveness_scores": {
                vid: v.effectiveness_score 
                for vid, v in self.prompt_versions.items() 
                if v.effectiveness_score is not None
            }
        }