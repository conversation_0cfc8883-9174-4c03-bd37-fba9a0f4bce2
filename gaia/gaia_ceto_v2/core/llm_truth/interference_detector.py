"""
Truth Enforcer Interference Detection System

Detects cases where truth enforcement prompts cause problems with legitimate
user queries, such as:
- User searches for "tool companies" but truth enforcer blocks tool mentions
- User discusses "software tools" but system thinks they mean MCP tools
- Over-aggressive truth enforcement affecting normal conversation
"""

import json
import re
from dataclasses import dataclass, asdict
from datetime import datetime
from enum import Enum
from pathlib import Path
from typing import Dict, List, Optional, Any


class InterferenceType(Enum):
    """Types of truth enforcer interference."""
    DOMAIN_CONFUSION = "domain_confusion"  # Tool term confusion (software tools vs MCP tools)
    OVER_SUPPRESSION = "over_suppression"  # Truth enforcer blocking legitimate responses
    CONTEXT_MISREAD = "context_misread"    # Misinterpreting user intent
    PROMPT_CONFLICT = "prompt_conflict"    # Truth prompt conflicts with user request
    FALSE_POSITIVE = "false_positive"      # Truth violation flagged incorrectly


@dataclass
class InterferenceIncident:
    """A detected interference incident."""
    incident_id: str
    interference_type: InterferenceType
    detected_at: str
    request_id: str
    
    # User Request Analysis
    user_query: str
    user_intent: str              # What user actually wanted
    domain_context: str           # Business domain (e.g., "manufacturing", "software")
    
    # Truth System Impact
    truth_prompts_active: List[str]  # Which truth prompts were active
    suspected_trigger: str           # What in the truth prompt likely caused issue
    
    # Response Analysis  
    llm_response: str
    response_quality: str         # "suppressed", "confused", "off-topic"
    expected_response: str        # What response should have been
    
    # Detection Signals
    detection_patterns: List[str] # Patterns that triggered detection
    confidence_score: float       # 0.0-1.0 confidence this is interference
    
    # Impact Assessment
    severity: str                 # "low", "medium", "high", "critical"
    user_experience_impact: str   # Description of UX impact


@dataclass
class InterferencePattern:
    """Pattern for detecting interference."""
    pattern_id: str
    pattern_type: InterferenceType
    detection_regex: str
    context_keywords: List[str]   # Keywords that indicate this domain
    confidence_weight: float      # How much this pattern contributes to confidence
    description: str


class InterferenceDetector:
    """Detects truth enforcer interference with legitimate queries."""
    
    def __init__(self, truth_dir: Path = None):
        """Initialize interference detector."""
        self.truth_dir = truth_dir or Path(__file__).parent / "truth_data"
        self.truth_dir.mkdir(parents=True, exist_ok=True)
        
        self.incidents_file = self.truth_dir / "interference_incidents.json"
        self.patterns_file = self.truth_dir / "interference_patterns.json"
        
        # Load existing data
        self.incidents: Dict[str, InterferenceIncident] = self._load_incidents()
        self.patterns: Dict[str, InterferencePattern] = self._load_patterns()
        
        # Initialize default patterns if none exist
        if not self.patterns:
            self._create_default_patterns()
    
    def _load_incidents(self) -> Dict[str, InterferenceIncident]:
        """Load interference incidents from storage."""
        incidents = {}
        if self.incidents_file.exists():
            try:
                with open(self.incidents_file, 'r') as f:
                    data = json.load(f)
                    for incident_data in data.get('incidents', []):
                        # Convert string back to enum
                        incident_data['interference_type'] = InterferenceType(incident_data['interference_type'])
                        incident = InterferenceIncident(**incident_data)
                        incidents[incident.incident_id] = incident
            except Exception as e:
                print(f"Error loading interference incidents: {e}")
        return incidents
    
    def _load_patterns(self) -> Dict[str, InterferencePattern]:
        """Load interference patterns from storage.""" 
        patterns = {}
        if self.patterns_file.exists():
            try:
                with open(self.patterns_file, 'r') as f:
                    data = json.load(f)
                    for pattern_data in data.get('patterns', []):
                        # Convert string back to enum
                        pattern_data['pattern_type'] = InterferenceType(pattern_data['pattern_type'])
                        pattern = InterferencePattern(**pattern_data)
                        patterns[pattern.pattern_id] = pattern
            except Exception as e:
                print(f"Error loading interference patterns: {e}")
        return patterns
    
    def _create_default_patterns(self):
        """Create default interference detection patterns."""
        default_patterns = [
            InterferencePattern(
                pattern_id="tool_company_search",
                pattern_type=InterferenceType.DOMAIN_CONFUSION,
                detection_regex=r"(?i)(?:search|find|looking for|companies?|manufacturers?).*\b(?:tool|tools)\b.*(?:companies?|manufacturers?|industry|sector)",
                context_keywords=["manufacturing", "industrial", "company", "business", "search"],
                confidence_weight=0.8,
                description="User searching for tool/die companies, not MCP tools"
            ),
            InterferencePattern(
                pattern_id="software_tools_discussion",
                pattern_type=InterferenceType.DOMAIN_CONFUSION,
                detection_regex=r"(?i)\b(?:software|development|programming|coding)\b.*\btools?\b",
                context_keywords=["software", "development", "programming", "IDE", "framework"],
                confidence_weight=0.7,
                description="User discussing software development tools"
            ),
            InterferencePattern(
                pattern_id="gardening_tools",
                pattern_type=InterferenceType.DOMAIN_CONFUSION,
                detection_regex=r"(?i)\b(?:garden|farming|agriculture|yard)\b.*\btools?\b",
                context_keywords=["garden", "farming", "agriculture", "outdoor", "lawn"],
                confidence_weight=0.9,
                description="User discussing gardening/farming tools"
            ),
            InterferencePattern(
                pattern_id="reluctant_response",
                pattern_type=InterferenceType.OVER_SUPPRESSION,
                detection_regex=r"(?i)(?:I (?:can't|cannot|won't|will not)|(?:unable to|not able to)).*(?:tools?|search|find)",
                context_keywords=["cannot", "unable", "restricted", "not allowed"],
                confidence_weight=0.6,
                description="LLM being overly cautious due to truth enforcement"
            )
        ]
        
        for pattern in default_patterns:
            self.patterns[pattern.pattern_id] = pattern
        
        self._save_patterns()
    
    def detect_interference(self, user_query: str, llm_response: str, 
                          context: Dict[str, Any]) -> List[InterferenceIncident]:
        """Detect potential truth enforcer interference."""
        incidents = []
        
        # Get active truth prompts from context
        active_truth_prompts = context.get('active_truth_prompts', [])
        
        for pattern in self.patterns.values():
            # Check if pattern matches user query
            if re.search(pattern.detection_regex, user_query):
                # Calculate confidence based on context keywords
                confidence = self._calculate_confidence(user_query, llm_response, pattern)
                
                if confidence > 0.5:  # Threshold for detection
                    incident = self._create_incident(
                        pattern, user_query, llm_response, context, confidence
                    )
                    incidents.append(incident)
        
        # Store incidents
        for incident in incidents:
            self.incidents[incident.incident_id] = incident
        
        if incidents:
            self._save_incidents()
        
        return incidents
    
    def _calculate_confidence(self, user_query: str, llm_response: str, 
                            pattern: InterferencePattern) -> float:
        """Calculate confidence that this is interference."""
        confidence = pattern.confidence_weight
        
        # Boost confidence for keyword matches
        keyword_matches = sum(1 for keyword in pattern.context_keywords 
                            if keyword.lower() in user_query.lower())
        confidence += (keyword_matches / len(pattern.context_keywords)) * 0.2
        
        # Check for signs of suppression in response
        suppression_indicators = [
            r"(?i)I (?:can't|cannot|won't) help",
            r"(?i)not able to (?:search|find|locate)",
            r"(?i)don't have access to",
            r"(?i)unable to (?:perform|execute|run)"
        ]
        
        response_suppressed = any(re.search(indicator, llm_response) 
                                for indicator in suppression_indicators)
        if response_suppressed:
            confidence += 0.3
        
        return min(confidence, 1.0)
    
    def _create_incident(self, pattern: InterferencePattern, user_query: str,
                        llm_response: str, context: Dict[str, Any], 
                        confidence: float) -> InterferenceIncident:
        """Create interference incident record."""
        incident_id = f"interference_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{id(self) % 1000}"
        
        # Analyze user intent
        user_intent = self._analyze_user_intent(user_query, pattern)
        domain_context = self._determine_domain_context(user_query, pattern)
        
        # Determine response quality
        response_quality = self._assess_response_quality(llm_response, user_query)
        
        # Assess severity
        severity = self._assess_severity(confidence, response_quality, context)
        
        return InterferenceIncident(
            incident_id=incident_id,
            interference_type=pattern.pattern_type,
            detected_at=datetime.now().isoformat(),
            request_id=context.get('request_id', 'unknown'),
            user_query=user_query,
            user_intent=user_intent,
            domain_context=domain_context,
            truth_prompts_active=context.get('active_truth_prompts', []),
            suspected_trigger=self._identify_trigger(pattern, context),
            llm_response=llm_response,
            response_quality=response_quality,
            expected_response=self._generate_expected_response(user_query, domain_context),
            detection_patterns=[pattern.pattern_id],
            confidence_score=confidence,
            severity=severity,
            user_experience_impact=self._assess_ux_impact(response_quality, severity)
        )
    
    def _analyze_user_intent(self, user_query: str, pattern: InterferencePattern) -> str:
        """Analyze what the user actually wanted."""
        if pattern.pattern_type == InterferenceType.DOMAIN_CONFUSION:
            if "company" in user_query.lower() or "business" in user_query.lower():
                return "Find companies/businesses in a specific industry"
            elif "software" in user_query.lower():
                return "Information about software development tools"
            elif "garden" in user_query.lower() or "farm" in user_query.lower():
                return "Information about physical tools for outdoor work"
        
        return "General information request in non-MCP domain"
    
    def _determine_domain_context(self, user_query: str, pattern: InterferencePattern) -> str:
        """Determine the business/topic domain."""
        query_lower = user_query.lower()
        
        domain_keywords = {
            "manufacturing": ["manufacturing", "industrial", "factory", "production"],
            "software": ["software", "programming", "development", "coding", "app"],
            "agriculture": ["farming", "agriculture", "garden", "crop", "livestock"],
            "construction": ["construction", "building", "contractor", "renovation"],
            "automotive": ["automotive", "car", "vehicle", "mechanic", "repair"]
        }
        
        for domain, keywords in domain_keywords.items():
            if any(keyword in query_lower for keyword in keywords):
                return domain
        
        return "general"
    
    def _assess_response_quality(self, llm_response: str, user_query: str) -> str:
        """Assess quality of LLM response."""
        response_lower = llm_response.lower()
        
        if any(phrase in response_lower for phrase in ["can't help", "cannot assist", "unable to"]):
            return "suppressed"
        elif "tool" in user_query.lower() and "tool" not in response_lower:
            return "avoided_topic"
        elif len(llm_response) < 50:
            return "too_brief"
        else:
            return "normal"
    
    def _assess_severity(self, confidence: float, response_quality: str, context: Dict[str, Any]) -> str:
        """Assess severity of interference."""
        if confidence > 0.9 and response_quality == "suppressed":
            return "critical"
        elif confidence > 0.7 and response_quality in ["suppressed", "avoided_topic"]:
            return "high"
        elif confidence > 0.5:
            return "medium"
        else:
            return "low"
    
    def _identify_trigger(self, pattern: InterferencePattern, context: Dict[str, Any]) -> str:
        """Identify what in truth prompt likely caused the issue."""
        active_prompts = context.get('active_truth_prompts', [])
        
        if pattern.pattern_type == InterferenceType.DOMAIN_CONFUSION:
            return "Truth prompt uses word 'tools' which conflicts with user's domain context"
        elif pattern.pattern_type == InterferenceType.OVER_SUPPRESSION:
            return "Truth enforcement making LLM overly cautious about any tool mentions"
        
        return "Unknown trigger in truth enforcement prompts"
    
    def _generate_expected_response(self, user_query: str, domain_context: str) -> str:
        """Generate what the response should have been without interference."""
        if domain_context == "manufacturing":
            return "Should provide information about tool and die companies, manufacturing equipment suppliers, etc."
        elif domain_context == "software":
            return "Should discuss software development tools, IDEs, frameworks, etc."
        elif domain_context == "agriculture":
            return "Should discuss farming equipment, gardening tools, agricultural machinery, etc."
        
        return "Should provide helpful information relevant to user's domain"
    
    def _assess_ux_impact(self, response_quality: str, severity: str) -> str:
        """Assess user experience impact."""
        if response_quality == "suppressed" and severity in ["critical", "high"]:
            return "User cannot get needed information, likely frustrated"
        elif response_quality == "avoided_topic":
            return "User gets incomplete answer, may need to rephrase query"
        elif response_quality == "too_brief":
            return "User gets minimal help, reduced utility"
        
        return "Minor impact on user experience"
    
    def _save_incidents(self):
        """Save interference incidents to storage."""
        try:
            # Convert incidents to JSON-serializable format
            incidents_data = []
            for incident in self.incidents.values():
                incident_dict = asdict(incident)
                # Convert enum to string
                incident_dict['interference_type'] = incident.interference_type.value
                incidents_data.append(incident_dict)
            
            data = {
                'incidents': incidents_data,
                'last_updated': datetime.now().isoformat()
            }
            with open(self.incidents_file, 'w') as f:
                json.dump(data, f, indent=2)
        except Exception as e:
            print(f"Error saving interference incidents: {e}")
    
    def _save_patterns(self):
        """Save interference patterns to storage."""
        try:
            # Convert patterns to JSON-serializable format
            patterns_data = []
            for pattern in self.patterns.values():
                pattern_dict = asdict(pattern)
                # Convert enum to string  
                pattern_dict['pattern_type'] = pattern.pattern_type.value
                patterns_data.append(pattern_dict)
            
            data = {
                'patterns': patterns_data,
                'last_updated': datetime.now().isoformat()
            }
            with open(self.patterns_file, 'w') as f:
                json.dump(data, f, indent=2)
        except Exception as e:
            print(f"Error saving interference patterns: {e}")
    
    def get_interference_stats(self) -> Dict[str, Any]:
        """Get statistics about interference incidents."""
        if not self.incidents:
            return {"total_incidents": 0}
        
        # Count by type
        type_counts = {}
        severity_counts = {}
        
        for incident in self.incidents.values():
            type_name = incident.interference_type.value
            type_counts[type_name] = type_counts.get(type_name, 0) + 1
            
            severity_counts[incident.severity] = severity_counts.get(incident.severity, 0) + 1
        
        return {
            "total_incidents": len(self.incidents),
            "by_type": type_counts,
            "by_severity": severity_counts,
            "latest_incident": max(self.incidents.values(), 
                                 key=lambda x: x.detected_at).detected_at
        }
    
    def generate_interference_report(self) -> str:
        """Generate human-readable interference report."""
        stats = self.get_interference_stats()
        
        if stats["total_incidents"] == 0:
            return "No truth enforcer interference detected."
        
        report = f"""
TRUTH ENFORCER INTERFERENCE REPORT
==================================

Total Incidents: {stats['total_incidents']}

By Type:
{chr(10).join(f"  {t}: {c}" for t, c in stats.get('by_type', {}).items())}

By Severity:
{chr(10).join(f"  {s}: {c}" for s, c in stats.get('by_severity', {}).items())}

Recent Incidents:
"""
        # Add recent incidents
        recent = sorted(self.incidents.values(), 
                       key=lambda x: x.detected_at, reverse=True)[:5]
        
        for incident in recent:
            report += f"""
  [{incident.severity.upper()}] {incident.interference_type.value}
  Query: {incident.user_query[:80]}...
  Impact: {incident.user_experience_impact}
  Confidence: {incident.confidence_score:.2f}
"""
        
        return report