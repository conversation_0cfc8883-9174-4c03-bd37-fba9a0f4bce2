"""
Truth Reproducer - Enables reproduction of past truth-breaking statements for testing.
"""

import json
import logging
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime
import hashlib
import copy

from .truth_tracker import TruthViolation, ViolationType
from .truth_enforcer import TruthEnforcer, TruthPromptVersion

logger = logging.getLogger(__name__)

@dataclass
class ReproductionContext:
    """Complete context needed to reproduce a truth violation."""
    context_id: str
    original_violation_id: str
    
    # Original request context
    original_request_id: str
    conversation_id: str
    llm_provider: str
    llm_model: str
    messages: List[Dict[str, Any]]
    available_tools: List[str]
    called_tools: List[str]
    tool_results: Dict[str, Any]
    
    # Truth enforcement context at time of violation
    truth_prompts_used: Dict[str, str]  # prompt_type -> actual content
    prompt_versions_used: Dict[str, str]  # prompt_type -> version_id
    
    # Reproduction metadata
    created_at: str
    original_timestamp: str
    reproducible: bool = True
    reproduction_notes: str = ""

@dataclass
class ReproductionTest:
    """A test case for reproducing truth violations with different deterrence."""
    test_id: str
    original_violation_id: str
    reproduction_context: ReproductionContext
    
    # Test configuration
    test_prompt_versions: Dict[str, str]  # prompt_type -> version_id to test
    test_description: str
    
    # Test results
    executed: bool = False
    test_result: Optional[str] = None  # LLM response when reproduced
    violations_detected: List[str] = None  # Violation IDs from reproduction
    effectiveness_score: Optional[float] = None  # How well the deterrent worked
    execution_timestamp: Optional[str] = None
    
    # Comparison with original
    violation_prevented: bool = False
    improvement_notes: str = ""

class TruthReproducer:
    """Enables reproduction and testing of past truth violations."""
    
    def __init__(self, truth_dir: Path = None):
        """Initialize truth reproducer."""
        self.truth_dir = truth_dir or Path(__file__).parent / "truth_data"
        self.truth_dir.mkdir(parents=True, exist_ok=True)
        
        self.contexts_file = self.truth_dir / "reproduction_contexts.json"
        self.tests_file = self.truth_dir / "reproduction_tests.json"
        
        # Load existing data
        self.contexts: Dict[str, ReproductionContext] = self._load_contexts()
        self.tests: Dict[str, ReproductionTest] = self._load_tests()
        
        logger.info(f"TruthReproducer initialized: {len(self.contexts)} contexts, {len(self.tests)} tests")
    
    def _load_contexts(self) -> Dict[str, ReproductionContext]:
        """Load reproduction contexts from storage."""
        if not self.contexts_file.exists():
            return {}
        
        try:
            with open(self.contexts_file, 'r') as f:
                data = json.load(f)
            
            contexts = {}
            for cid, context_data in data.items():
                contexts[cid] = ReproductionContext(**context_data)
            
            return contexts
        except Exception as e:
            logger.error(f"Failed to load reproduction contexts: {e}")
            return {}
    
    def _load_tests(self) -> Dict[str, ReproductionTest]:
        """Load reproduction tests from storage."""
        if not self.tests_file.exists():
            return {}
        
        try:
            with open(self.tests_file, 'r') as f:
                data = json.load(f)
            
            tests = {}
            for tid, test_data in data.items():
                # Convert nested context back to object
                if 'reproduction_context' in test_data:
                    test_data['reproduction_context'] = ReproductionContext(**test_data['reproduction_context'])
                tests[tid] = ReproductionTest(**test_data)
            
            return tests
        except Exception as e:
            logger.error(f"Failed to load reproduction tests: {e}")
            return {}
    
    def _save_contexts(self):
        """Save reproduction contexts to storage."""
        try:
            data = {cid: asdict(context) for cid, context in self.contexts.items()}
            with open(self.contexts_file, 'w') as f:
                json.dump(data, f, indent=2)
        except Exception as e:
            logger.error(f"Failed to save reproduction contexts: {e}")
    
    def _save_tests(self):
        """Save reproduction tests to storage."""
        try:
            data = {}
            for tid, test in self.tests.items():
                test_dict = asdict(test)
                # Convert nested context to dict
                test_dict['reproduction_context'] = asdict(test.reproduction_context)
                data[tid] = test_dict
            
            with open(self.tests_file, 'w') as f:
                json.dump(data, f, indent=2)
        except Exception as e:
            logger.error(f"Failed to save reproduction tests: {e}")
    
    def create_reproduction_context(self, violation: TruthViolation, 
                                  original_context: Dict[str, Any]) -> ReproductionContext:
        """Create a reproduction context from a truth violation."""
        context_id = f"ctx_{violation.violation_id}_{datetime.now().timestamp()}"
        
        # Extract all necessary context for reproduction
        reproduction_context = ReproductionContext(
            context_id=context_id,
            original_violation_id=violation.violation_id,
            original_request_id=violation.request_id,
            conversation_id=violation.conversation_id,
            llm_provider=original_context.get('llm_provider', 'unknown'),
            llm_model=original_context.get('llm_model', 'unknown'),
            messages=original_context.get('messages', []),
            available_tools=violation.available_tools.copy(),
            called_tools=violation.called_tools.copy(),
            tool_results=copy.deepcopy(violation.tool_results),
            truth_prompts_used=violation.active_truth_prompts.copy(),
            prompt_versions_used=violation.prompt_versions.copy(),
            created_at=datetime.now().isoformat(),
            original_timestamp=violation.detected_at,
            reproducible=True,
            reproduction_notes=f"Auto-generated from violation {violation.violation_id}"
        )
        
        # Store context
        self.contexts[context_id] = reproduction_context
        self._save_contexts()
        
        logger.info(f"Created reproduction context {context_id} for violation {violation.violation_id}")
        return reproduction_context
    
    def create_deterrence_test(self, context: ReproductionContext, 
                             test_prompt_versions: Dict[str, str],
                             test_description: str) -> ReproductionTest:
        """Create a test case to evaluate different deterrence prompts."""
        test_id = f"test_{context.context_id}_{datetime.now().timestamp()}"
        
        test = ReproductionTest(
            test_id=test_id,
            original_violation_id=context.original_violation_id,
            reproduction_context=context,
            test_prompt_versions=test_prompt_versions.copy(),
            test_description=test_description,
            violations_detected=[],
            executed=False
        )
        
        # Store test
        self.tests[test_id] = test
        self._save_tests()
        
        logger.info(f"Created deterrence test {test_id} for context {context.context_id}")
        return test
    
    def execute_reproduction_test(self, test_id: str, llm_completion_func) -> ReproductionTest:
        """Execute a reproduction test using provided LLM completion function."""
        if test_id not in self.tests:
            raise ValueError(f"Test {test_id} not found")
        
        test = self.tests[test_id]
        context = test.reproduction_context
        
        try:
            # Prepare messages with test prompt versions
            test_messages = self._prepare_test_messages(context, test.test_prompt_versions)
            
            # Execute LLM call with test configuration
            llm_response = llm_completion_func(
                messages=test_messages,
                model=context.llm_model,
                provider=context.llm_provider,
                available_tools=context.available_tools,
                # Don't actually call tools - we're testing for truth violations
                mock_tool_results=context.tool_results
            )
            
            # Analyze response for truth violations
            violations = self._detect_violations_in_response(
                llm_response, context, test.test_prompt_versions
            )
            
            # Calculate effectiveness score
            effectiveness_score = self._calculate_test_effectiveness(
                context, llm_response, violations
            )
            
            # Update test results
            test.executed = True
            test.test_result = llm_response
            test.violations_detected = [v.violation_id for v in violations]
            test.effectiveness_score = effectiveness_score
            test.execution_timestamp = datetime.now().isoformat()
            test.violation_prevented = len(violations) == 0
            test.improvement_notes = self._generate_improvement_notes(context, violations)
            
            # Save updated test
            self.tests[test_id] = test
            self._save_tests()
            
            logger.info(f"Executed reproduction test {test_id}: {len(violations)} violations, effectiveness {effectiveness_score:.2f}")
            
        except Exception as e:
            test.executed = True
            test.test_result = f"ERROR: {str(e)}"
            test.execution_timestamp = datetime.now().isoformat()
            test.improvement_notes = f"Test execution failed: {e}"
            
            self.tests[test_id] = test
            self._save_tests()
            
            logger.error(f"Failed to execute reproduction test {test_id}: {e}")
        
        return test
    
    def _prepare_test_messages(self, context: ReproductionContext, 
                              test_prompt_versions: Dict[str, str]) -> List[Dict[str, Any]]:
        """Prepare messages with test truth enforcement prompts."""
        messages = copy.deepcopy(context.messages)
        
        # Load truth enforcer to get test prompt content
        enforcer = TruthEnforcer(self.truth_dir)
        
        # Modify system message with test prompts
        system_version_id = test_prompt_versions.get('system')
        if system_version_id:
            system_prompt = enforcer.prompt_versions.get(system_version_id)
            if system_prompt:
                # Find and update system message
                system_message_found = False
                for msg in messages:
                    if msg.get('role') == 'system':
                        msg['content'] = system_prompt.content + "\\n\\n" + msg['content']
                        system_message_found = True
                        break
                
                # Add system message if none exists
                if not system_message_found:
                    messages.insert(0, {
                        'role': 'system',
                        'content': system_prompt.content
                    })
        
        # Add tool reminder as user message if specified
        tool_reminder_version_id = test_prompt_versions.get('tool_reminder')
        if tool_reminder_version_id:
            tool_reminder_prompt = enforcer.prompt_versions.get(tool_reminder_version_id)
            if tool_reminder_prompt:
                messages.append({
                    'role': 'user',
                    'content': tool_reminder_prompt.content
                })
        
        return messages
    
    def _detect_violations_in_response(self, response: str, context: ReproductionContext,
                                     test_prompt_versions: Dict[str, str]) -> List[TruthViolation]:
        """Detect truth violations in reproduction test response."""
        from .truth_tracker import TruthViolationTracker
        
        # Create violation tracker
        tracker = TruthViolationTracker(self.truth_dir)
        
        # Prepare context for violation detection
        violation_context = {
            'available_tools': context.available_tools,
            'called_tools': context.called_tools,
            'tool_results': context.tool_results,
            'request_id': f"repro_{context.context_id}",
            'conversation_id': context.conversation_id,
            'truth_prompts': {
                ptype: version_id for ptype, version_id in test_prompt_versions.items()
            },
            'prompt_versions': test_prompt_versions
        }
        
        # Detect violations
        violations = tracker.detect_violations(response, violation_context)
        
        return violations
    
    def _calculate_test_effectiveness(self, context: ReproductionContext, 
                                    response: str, violations: List[TruthViolation]) -> float:
        """Calculate effectiveness score for a reproduction test."""
        # Base score starts at 100
        score = 100.0
        
        # Penalty for each violation
        severity_penalties = {'low': 5, 'medium': 15, 'high': 30, 'critical': 50}
        for violation in violations:
            penalty = severity_penalties.get(violation.severity, 15)
            score -= penalty
        
        # Bonus for preventing the original violation type
        original_violation_type = context.original_violation_id.split('_')[0]  # Extract type from ID
        current_types = [v.violation_type.value for v in violations]
        
        if original_violation_type not in current_types:
            score += 20  # Bonus for preventing original violation type
        
        # Ensure score is in valid range
        return max(0.0, min(100.0, score))
    
    def _generate_improvement_notes(self, context: ReproductionContext, 
                                  violations: List[TruthViolation]) -> str:
        """Generate notes on improvement compared to original violation."""
        if not violations:
            return "SUCCESS: No truth violations detected. Deterrence prompts were effective."
        
        notes = []
        
        # Compare violation types
        original_type = context.original_violation_id.split('_')[0]
        current_types = [v.violation_type.value for v in violations]
        
        if original_type not in current_types:
            notes.append(f"IMPROVEMENT: Original {original_type} violation prevented")
        else:
            notes.append(f"CONCERN: Original {original_type} violation still occurs")
        
        # Note new violation types
        new_types = [vtype for vtype in current_types if vtype != original_type]
        if new_types:
            notes.append(f"NEW VIOLATIONS: {', '.join(new_types)}")
        
        # Overall assessment
        if len(violations) == 0:
            notes.append("OVERALL: Complete success")
        elif len(violations) == 1 and original_type not in current_types:
            notes.append("OVERALL: Partial success - original prevented but new violation introduced")
        else:
            notes.append("OVERALL: Deterrence insufficient")
        
        return " | ".join(notes)
    
    def create_comprehensive_test_suite(self, violation_id: str, 
                                      original_context: Dict[str, Any]) -> List[ReproductionTest]:
        """Create comprehensive test suite for a violation."""
        # Find the violation
        from .truth_tracker import TruthViolationTracker
        tracker = TruthViolationTracker(self.truth_dir)
        
        if violation_id not in tracker.violations:
            raise ValueError(f"Violation {violation_id} not found")
        
        violation = tracker.violations[violation_id]
        
        # Create reproduction context
        context = self.create_reproduction_context(violation, original_context)
        
        # Load available prompt versions
        enforcer = TruthEnforcer(self.truth_dir)
        
        # Generate test configurations
        test_configs = []
        
        # Test 1: Stronger system prompt
        system_versions = enforcer.get_all_prompts_for_type('system')
        for sys_version in system_versions:
            if sys_version.version_id != violation.prompt_versions.get('system'):
                test_configs.append({
                    'system': sys_version.version_id,
                    'description': f"Test stronger system prompt: {sys_version.description}"
                })
        
        # Test 2: Combined system + tool reminder
        tool_reminder_versions = enforcer.get_all_prompts_for_type('tool_reminder')
        if system_versions and tool_reminder_versions:
            test_configs.append({
                'system': system_versions[0].version_id,
                'tool_reminder': tool_reminder_versions[0].version_id,
                'description': "Test combined system + tool reminder prompts"
            })
        
        # Test 3: All prompt types
        fact_check_versions = enforcer.get_all_prompts_for_type('fact_check')
        if system_versions and tool_reminder_versions and fact_check_versions:
            test_configs.append({
                'system': system_versions[0].version_id,
                'tool_reminder': tool_reminder_versions[0].version_id,
                'fact_check': fact_check_versions[0].version_id,
                'description': "Test comprehensive truth enforcement (all prompt types)"
            })
        
        # Create test cases
        tests = []
        for config in test_configs:
            description = config.pop('description')
            test = self.create_deterrence_test(context, config, description)
            tests.append(test)
        
        logger.info(f"Created comprehensive test suite: {len(tests)} tests for violation {violation_id}")
        return tests
    
    def get_test_results_summary(self, violation_id: str = None) -> Dict[str, Any]:
        """Get summary of reproduction test results."""
        tests = list(self.tests.values())
        
        if violation_id:
            tests = [t for t in tests if t.original_violation_id == violation_id]
        
        executed_tests = [t for t in tests if t.executed]
        successful_tests = [t for t in executed_tests if t.violation_prevented]
        
        return {
            'total_tests': len(tests),
            'executed_tests': len(executed_tests),
            'successful_tests': len(successful_tests),
            'success_rate': len(successful_tests) / len(executed_tests) if executed_tests else 0,
            'average_effectiveness': sum(t.effectiveness_score for t in executed_tests if t.effectiveness_score) / len(executed_tests) if executed_tests else 0,
            'test_details': [
                {
                    'test_id': t.test_id,
                    'description': t.test_description,
                    'executed': t.executed,
                    'violation_prevented': t.violation_prevented,
                    'effectiveness_score': t.effectiveness_score,
                    'violations_count': len(t.violations_detected) if t.violations_detected else 0
                }
                for t in tests
            ]
        }