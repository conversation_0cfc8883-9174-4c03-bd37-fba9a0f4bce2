"""
Level 0035: Tool Response Types - Minimal Implementation

Simple structured responses from tools with context and synthesis control.
"""

import json
import uuid
from dataclasses import dataclass, field
from datetime import datetime
from pathlib import Path
from typing import Optional, Dict, Any

from gaia_ceto_v2.core.level_0003_logging import create_level_0003_logger
from gaia_ceto_v2.core.logging_factory import create_toolresponse_loggers
from gaia_ceto_v2.settings import GAIA_SETTINGS

# Import terminal output helpers  
try:
    from gaia_ceto_v2.core.logging_helpers import (
        log_toolresponse_flags, 
        log_toolresponse_docstore, 
        log_toolresponse_pipeline
    )
except ImportError:
    # Fallback functions if import fails
    def log_toolresponse_flags(tool_name, flags, call_id): pass
    def log_toolresponse_docstore(tool_name, doc_id, call_id): pass  
    def log_toolresponse_pipeline(tool_name, synthesis_flag, display_flag, doc_id, call_id): pass


@dataclass
class ToolResponse:
    """Simple tool response with control flags."""
    content: str
    include_in_context: bool = True
    needs_synthesis: bool = False
    show_to_user: bool = True
    store_as_document: bool = False
    document_id: Optional[str] = None
    content_format: str = "text"
    metadata: Dict[str, Any] = field(default_factory=dict)


class SimpleDocumentStore:
    """Minimal document storage for large tool outputs."""
    
    def __init__(self, storage_dir: str = None):
        self.storage_dir = Path(storage_dir or GAIA_SETTINGS.GAIA_DOCS_DIR)
        self.storage_dir.mkdir(exist_ok=True)
        self.logger = create_level_0003_logger('docstore')
    
    def store(self, content: str, doc_type: str, metadata: Dict[str, Any] = None) -> str:
        """Store document, return ID."""
        doc_id = f"{doc_type}_{uuid.uuid4().hex[:8]}"
        doc_path = self.storage_dir / f"{doc_id}.json"
        
        doc_data = {
            'id': doc_id,
            'content': content,
            'doc_type': doc_type,
            'metadata': metadata or {},
            'created_at': datetime.now().isoformat(),
            'size': len(content)
        }
        
        with open(doc_path, 'w') as f:
            json.dump(doc_data, f)
        
        # Level 0003 logging
        self.logger.log_request(doc_id, f"store_{doc_type}", {
            'doc_type': doc_type, 'size': len(content), 'metadata_keys': list((metadata or {}).keys())
        })
        self.logger.log_response(doc_id, f"stored_{doc_type}", {
            'doc_id': doc_id, 'path': str(doc_path), 'success': True
        })
        
        return doc_id
    
    def get(self, doc_id: str) -> Dict[str, Any]:
        """Get stored document."""
        doc_path = self.storage_dir / f"{doc_id}.json"
        
        try:
            with open(doc_path, 'r') as f:
                doc_data = json.load(f)
            
            # Level 0003 logging
            self.logger.log_request(doc_id, "get_document", {'doc_id': doc_id})
            self.logger.log_response(doc_id, "retrieved", {
                'doc_id': doc_id, 'size': doc_data.get('size', 0), 'success': True
            })
            
            return doc_data
            
        except Exception as e:
            # Level 0003 error logging
            self.logger.log_error(doc_id, f"get_document_failed", e)
            raise


# Global document store
_doc_store = SimpleDocumentStore()

# ToolResponse processing loggers
_toolresponse_logger, _toolresponse_send_logger, _toolresponse_rcv_logger = create_toolresponse_loggers()


def handle_tool_response(tool_name: str, response) -> Dict[str, Any]:
    """Process tool response - handles both ToolResponse and legacy strings."""
    
    call_id = uuid.uuid4().hex[:8]
    
    # Log pipeline start
    _toolresponse_send_logger.info(f"PIPELINE_START - {call_id} - tool={tool_name} response_type={type(response).__name__}")
    
    # Handle legacy string responses
    if isinstance(response, str):
        _toolresponse_logger.info(f"LEGACY_CONVERSION - {call_id} - Converting string to ToolResponse")
        response = ToolResponse(content=response)
    
    # Log ToolResponse flags
    flags = {
        'needs_synthesis': response.needs_synthesis,
        'show_to_user': response.show_to_user,
        'store_as_document': response.store_as_document,
        'include_in_context': response.include_in_context
    }
    _toolresponse_send_logger.info(f"TOOL_RESPONSE_FLAGS - {call_id} - {json.dumps(flags)}")
    
    # Terminal output for flags
    log_toolresponse_flags(tool_name, flags, call_id)
    
    # Store large documents
    if response.store_as_document and not response.document_id:
        _toolresponse_logger.info(f"DOCSTORE_DECISION - {call_id} - store_as_document=True → STORING_DOCUMENT")
        response.document_id = _doc_store.store(
            response.content, 
            response.content_format,
            response.metadata
        )
        _toolresponse_logger.info(f"DOCSTORE_STORED - {call_id} - document_id={response.document_id}")
        
        # Terminal output for document storage
        log_toolresponse_docstore(tool_name, response.document_id, call_id)
    elif response.store_as_document:
        _toolresponse_logger.info(f"DOCSTORE_DECISION - {call_id} - store_as_document=True but document_id exists → SKIP_STORE")
    else:
        _toolresponse_logger.info(f"DOCSTORE_DECISION - {call_id} - store_as_document=False → SKIP_STORE")
    
    # Determine what goes in context
    if response.include_in_context:
        if response.document_id:
            context_content = f"[Doc {response.document_id}]: {response.content[:200]}..."
            _toolresponse_logger.info(f"CONTEXT_DECISION - {call_id} - include_in_context=True + document_id → TRUNCATED_REFERENCE")
        else:
            context_content = response.content
            _toolresponse_logger.info(f"CONTEXT_DECISION - {call_id} - include_in_context=True → FULL_CONTENT")
    else:
        context_content = None
        _toolresponse_logger.info(f"CONTEXT_DECISION - {call_id} - include_in_context=False → NO_CONTEXT")
    
    # Determine final output
    if response.show_to_user:
        display_content = response.content
        _toolresponse_logger.info(f"DISPLAY_DECISION - {call_id} - show_to_user=True → USER_DISPLAY_ENABLED")
    else:
        display_content = None
        _toolresponse_logger.info(f"DISPLAY_DECISION - {call_id} - show_to_user=False → NO_USER_DISPLAY")
    
    # Log synthesis decision
    if response.needs_synthesis:
        _toolresponse_logger.info(f"SYNTHESIS_DECISION - {call_id} - needs_synthesis=True → LLM_PROCESSING_ENABLED")
    else:
        _toolresponse_logger.info(f"SYNTHESIS_DECISION - {call_id} - needs_synthesis=False → VERBATIM_OUTPUT")
    
    result = {
        'content': display_content,
        'context_content': context_content,
        'needs_synthesis': response.needs_synthesis,
        'show_to_user': response.show_to_user,
        'document_id': response.document_id,
        'metadata': response.metadata
    }
    
    # Log pipeline completion
    _toolresponse_rcv_logger.info(f"PIPELINE_END - {call_id} - Final: synthesis={response.needs_synthesis} display={response.show_to_user} doc_id={response.document_id}")
    
    # Terminal output for pipeline completion
    log_toolresponse_pipeline(tool_name, response.needs_synthesis, response.show_to_user, response.document_id, call_id)
    
    return result


def log_flag_violation(call_id: str, flag_name: str, expected: bool, actual_behavior: str):
    """Log when ToolResponse flags are ignored by downstream processing."""
    _toolresponse_logger.error(f"FLAG_VIOLATION - {call_id} - ALERT: {flag_name}={expected} but {actual_behavior}")


def get_document(doc_id: str) -> str:
    """Get stored document content."""
    doc = _doc_store.get(doc_id)
    return doc['content']