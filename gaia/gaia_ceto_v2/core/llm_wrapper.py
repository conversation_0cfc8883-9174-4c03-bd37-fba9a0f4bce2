"""
LiteLLM Wrapper - A unified interface for LLM providers.

This module provides a wrapper around the 'litellm' library, offering a
centralized point for handling LLM completions. It is designed to be
extensible for logging, caching, and accounting.

Level 0007: Comprehensive logging with terminal summaries and dedicated log files.
"""

import logging
import os
import json
from datetime import datetime
from typing import Any, Dict, List, Optional

import litellm
from .llm_errors import LLMResponse, classify_litellm_error
from .level_0004_simple import log_existing_timing

logger = logging.getLogger(__name__)

# Level 0007: Setup dedicated log files using centralized factory
from .logging_factory import create_llm_loggers

# Initialize Level 0007 loggers
_wrap_logger, _send_logger, _rcv_logger = create_llm_loggers()


class LiteLLMWrapper:
    """A wrapper for litellm to standardize LLM calls."""

    def __init__(self,
                 cache=None,
                 cost_tracker=None,
                 simple_cache=None,
                 accounting_system=None,
                 prompt_prefix_enabled=False,
                 truth_system_enabled=False):
        """
        Initializes the LiteLLMWrapper.

        Args:
            cache: Legacy caching mechanism (kept for compatibility)
            cost_tracker: An object to track the cost of LLM calls.
            simple_cache: Level 0008b SimpleCache instance with TTL
            accounting_system: Level 0008a AccountingSystem for cost tracking
            prompt_prefix_enabled: Enable system prompt prefixing
            truth_system_enabled: Enable truth enforcement prompts
        """
        self.cache = cache  # Legacy cache
        self.cost_tracker = cost_tracker
        self.simple_cache = simple_cache  # Level 0008b TTL cache
        self.accounting = accounting_system  # Level 0008a accounting
        self.prompt_prefix_enabled = prompt_prefix_enabled
        self.truth_system_enabled = truth_system_enabled
        
        # Initialize truth system if enabled
        self.truth_enforcer = None
        if truth_system_enabled:
            try:
                from .llm_truth import TruthEnforcer
                self.truth_enforcer = TruthEnforcer()
                logger.info("Truth system enabled in LLM wrapper")
            except ImportError:
                logger.warning("Truth system requested but llm_truth module not available")
                self.truth_system_enabled = False
        
        if simple_cache:
            logger.info("LLM wrapper initialized with TTL cache enabled")
        if accounting_system:
            logger.info("LLM wrapper initialized with accounting system enabled")

    def completion(self,
                   model: str,
                   messages: List[Dict[str, str]],
                   request_id: str = None,
                   **kwargs: Any) -> LLMResponse:
        """
        Calls the specified LLM with the given messages.

        Args:
            model: The name of the model to use (e.g., 'gpt-3.5-turbo').
            messages: A list of messages in the conversation.
            **kwargs: Additional arguments to pass to litellm.completion.

        Returns:
            LLMResponse object with structured success/failure information.
        """
        start_time = datetime.now()
        # Use provided request_id for correlation, or generate call_id as fallback
        call_id = request_id if request_id else f"{model}_{start_time.strftime('%H%M%S')}_{id(self) % 1000}"
        
        # Extract vendor and model name for Level 0007 logging
        vendor, model_name = self._parse_model_identifier(model)
        
        # Universal caching using cache_llm_call
        if self.simple_cache is not None:
            from .cache import cache_llm_call
            
            def compute_llm_response():
                """Function to compute LLM response on cache miss."""
                return self._call_llm_direct(model, messages, call_id, start_time, request_id, **kwargs)
            
            try:
                return cache_llm_call(
                    cache=self.simple_cache,
                    provider='litellm',
                    model=model,
                    messages=messages,
                    func_to_call=compute_llm_response
                )
                
            except Exception as e:
                logger.warning(f"Cache operation failed, proceeding without cache: {e}")
                # Fall through to direct call
        
        # Legacy caching logic (kept for compatibility)
        elif self.cache is not None:
            cache_key = self._generate_cache_key(model, messages, **kwargs)
            cached_response = self.cache.get(cache_key)
            if cached_response:
                # Log cache hit
                from .logging_helpers import log_llm_cache_hit
                log_llm_cache_hit(call_id, model, messages)
                logger.info("Returning cached response.")
                return LLMResponse.success_response(
                    content=cached_response,
                    metadata={'from_cache': True, 'call_id': call_id}
                )
        
        # No cache or cache miss - make direct call
        return self._call_llm_direct(model, messages, call_id, start_time, request_id, **kwargs)
    
    def _call_llm_direct(self, model: str, messages: List[Dict[str, str]], 
                        call_id: str, start_time: datetime, request_id: str = None, **kwargs) -> LLMResponse:
        """Make direct LLM call without caching."""
        try:
            # Prepare messages
            messages, kwargs = self._prepare_messages(model, messages, kwargs)
            
            # Log request (3 lines instead of 30)
            from .logging_helpers import log_llm_request
            log_llm_request(call_id, model, messages, **kwargs)
            
            # Make the call
            response = self._execute_llm_call(model, messages, **kwargs)
            
            # Process response
            response_text = response.choices[0].message.content
            if not response_text:
                return self._handle_empty_response(model, messages, call_id, start_time)
            
            # Log success (3 lines instead of 20)
            duration_ms = (datetime.now() - start_time).total_seconds() * 1000
            from .logging_helpers import log_llm_response
            log_llm_response(call_id, response_text, duration_ms, model, response.usage)
            
            # Record metrics
            self._record_usage_metrics(response, model, request_id, start_time, messages, call_id)
            
            # Cache if needed
            self._cache_response_if_needed(model, messages, response_text, **kwargs)
            
            # Return response
            return LLMResponse.success_response(
                content=response_text,
                metadata={
                    'model': model,
                    'message_count': len(messages),
                    'from_cache': False,
                    'call_id': call_id,
                    'duration_ms': duration_ms
                }
            )

        except Exception as e:
            return self._handle_llm_error(e, model, messages, call_id, start_time)
    
    def _prepare_messages(self, model: str, messages: List[Dict[str, str]], kwargs: dict) -> tuple:
        """Prepare messages and kwargs for LLM call."""
        vendor = model.split('/')[0] if '/' in model else 'unknown'
        return self._inject_system_message(vendor, messages, kwargs)
    
    def _execute_llm_call(self, model: str, messages: List[Dict[str, str]], **kwargs):
        """Execute the actual LLM call with proper API key handling."""
        # Handle Gemini API key mapping
        if model.startswith('gemini/') and os.getenv('GEMINI_API_KEY') and not os.getenv('GOOGLE_API_KEY'):
            original_google_key = os.environ.get('GOOGLE_API_KEY')
            os.environ['GOOGLE_API_KEY'] = os.environ['GEMINI_API_KEY']
            try:
                return litellm.completion(model=model, messages=messages, **kwargs)
            finally:
                # Restore original state
                if original_google_key is None:
                    os.environ.pop('GOOGLE_API_KEY', None)
                else:
                    os.environ['GOOGLE_API_KEY'] = original_google_key
        else:
            # Normal call
            return litellm.completion(model=model, messages=messages, **kwargs)
    
    def _handle_empty_response(self, model: str, messages: list, call_id: str, start_time: datetime) -> LLMResponse:
        """Handle empty response case."""
        from .llm_errors import LLMContentError, LLMErrorType
        error = LLMContentError(
            message="LLM returned empty response",
            error_type=LLMErrorType.UNKNOWN,
            details={'model': model, 'message_count': len(messages)}
        )
        
        # Log empty response
        duration_ms = (datetime.now() - start_time).total_seconds() * 1000
        from .logging_helpers import parse_model_identifier
        vendor, model_name = parse_model_identifier(model)
        print(f"❌ LLM Empty: {vendor}/{model_name} - {len(messages)} msgs -> EMPTY ({duration_ms:.0f}ms)")
        _wrap_logger.error(f"EMPTY_RESPONSE - {call_id} - {model} - {duration_ms:.0f}ms")
        
        # Level 0004 timing
        log_existing_timing(call_id, "llm_empty_response", start_time, {
            "model": model, "vendor": vendor, "message_count": len(messages), "error": True
        })
        
        return LLMResponse.error_response(error)
    
    def _record_usage_metrics(self, response, model: str, request_id: str, 
                             start_time: datetime, messages: list, call_id: str) -> None:
        """Record usage metrics for cost tracking and accounting."""
        vendor, model_name = self._parse_model_identifier(model)
        
        # Level 0004 timing
        log_existing_timing(call_id, "llm_call_success", start_time, {
            "model": model, "vendor": vendor, "message_count": len(messages), 
            "char_count": len(response.choices[0].message.content), "success": True
        })
        
        # Cost tracking
        if self.cost_tracker:
            self.cost_tracker.track_cost(response)
        
        # Accounting
        if self.accounting:
            try:
                usage = getattr(response, 'usage', None)
                if usage:
                    self.accounting.record_llm_call(
                        provider=vendor,
                        model=model_name,
                        input_tokens=getattr(usage, 'prompt_tokens', 0),
                        output_tokens=getattr(usage, 'completion_tokens', 0),
                        request_id=request_id
                    )
            except Exception as e:
                logger.warning(f"Accounting failed: {e}")
    
    def _cache_response_if_needed(self, model: str, messages: list, 
                                  response_text: str, **kwargs) -> None:
        """Cache response if legacy cache is enabled."""
        if self.cache is not None and not self.simple_cache:
            cache_key = self._generate_cache_key(model, messages, **kwargs)
            self.cache.set(cache_key, response_text)
    
    def _handle_llm_error(self, error: Exception, model: str, messages: list, 
                         call_id: str, start_time: datetime) -> LLMResponse:
        """Handle LLM call errors."""
        duration_ms = (datetime.now() - start_time).total_seconds() * 1000
        
        # Log error (3 lines instead of 15)
        from .logging_helpers import log_llm_error
        log_llm_error(call_id, error, duration_ms, model, messages)
        
        # Classify error
        structured_error = classify_litellm_error(error)
        
        # Level 0004 timing
        vendor = model.split('/')[0] if '/' in model else 'unknown'
        log_existing_timing(call_id, "llm_call_error", start_time, {
            "model": model, "vendor": vendor, "message_count": len(messages),
            "error": True, "error_type": type(error).__name__, "error_message": str(error)
        })
        
        logger.error(f"Classified as {structured_error.error_type.value} "
                    f"(severity: {structured_error.severity.value})")
        
        return LLMResponse.error_response(structured_error)

    def _generate_cache_key(self,
                            model: str,
                            messages: List[Dict[str, str]],
                            **kwargs: Any) -> str:
        """Generates a cache key for the given request."""
        from .cache import create_llm_cache_key
        
        # Remove non-cacheable keys like call_id, request_id, timestamp
        clean_kwargs = {k: v for k, v in kwargs.items() 
                       if k not in ['call_id', 'request_id', 'timestamp']}
        
        # Create clean messages without metadata
        clean_messages = []
        for msg in messages:
            clean_msg = {'role': msg['role'], 'content': msg['content']}
            clean_messages.append(clean_msg)
        
        cache_data = create_llm_cache_key('litellm', model, clean_messages, **clean_kwargs)
        return self._cache._generate_cache_key(cache_data)

    def _parse_model_identifier(self, model: str) -> tuple[str, str]:
        """Parse model string into vendor and model name.
        
        Args:
            model: Model identifier like 'gemini/gemini-2.5-flash-lite'
            
        Returns:
            Tuple of (vendor, model_name)
        """
        if '/' in model:
            vendor, model_name = model.split('/', 1)
        else:
            vendor = 'unknown'
            model_name = model
        return vendor, model_name

    def _log_request_level_0007(self,
                                call_id: str,
                                model: str, 
                                messages: List[Dict[str, str]],
                                **kwargs: Any) -> None:
        """Level 0007: Log request to dedicated send file."""
        # Log basic request info to main wrapper log
        _wrap_logger.info(f"REQUEST - {call_id} - {model} - {len(messages)} messages")
        
        # Log detailed request to send log
        request_data = {
            'call_id': call_id,
            'model': model,
            'message_count': len(messages),
            'messages': messages,
            'kwargs': kwargs,
            'timestamp': datetime.now().isoformat()
        }
        _send_logger.info(f"SEND - {call_id} - {json.dumps(request_data)}")

    def _log_response_level_0007(self,
                                 call_id: str,
                                 response: Any,
                                 response_text: str,
                                 duration_ms: float) -> None:
        """Level 0007: Log response to dedicated receive file."""
        # Log basic response info to main wrapper log  
        char_count = len(response_text)
        _wrap_logger.info(f"SUCCESS - {call_id} - {char_count} chars - {duration_ms:.0f}ms")
        
        # Log detailed response to receive log
        response_data = {
            'call_id': call_id,
            'content': response_text,
            'char_count': char_count,
            'duration_ms': duration_ms,
            'timestamp': datetime.now().isoformat()
        }
        
        # Include usage info if available
        if hasattr(response, 'usage') and response.usage:
            response_data['usage'] = {
                'prompt_tokens': getattr(response.usage, 'prompt_tokens', 0),
                'completion_tokens': getattr(response.usage, 'completion_tokens', 0),
                'total_tokens': getattr(response.usage, 'total_tokens', 0)
            }
        
        _rcv_logger.info(f"RECEIVE - {call_id} - {json.dumps(response_data)}")

    def _build_system_prefix(self) -> str:
        """Build system prompt prefix with optional truth enforcement."""
        base_prefix = """# INVARIANT RULES
- You are an AI assistant with tool calling capabilities.
- CRITICAL: You must only claim to have used "chat tools", "MCP tools", that you actually called.
Never fabricate such tool results or claim tool actions you did not perform.
- Do not confuse this use of "tool" with any other type of mention of "tool", for example
if the user is doing a search for a type of tool technology or a tool company.
"""

        if self.truth_system_enabled and self.truth_enforcer:
            truth_prompt = self.truth_enforcer.get_active_prompt("general")
            if truth_prompt:
                base_prefix += f"\n\n{truth_prompt.content}"
        
        return base_prefix

    def _inject_system_message(self, vendor: str, messages: List[Dict[str, str]], kwargs: Dict[str, Any]) -> tuple[List[Dict[str, str]], Dict[str, Any]]:
        """Inject system prompt using provider-specific methods."""
        if not self.prompt_prefix_enabled:
            return messages, kwargs
        
        system_prefix = self._build_system_prefix()
        
        if vendor == "openai":
            # OpenAI: System message in messages array
            if messages and messages[0].get("role") == "system":
                messages[0]["content"] = system_prefix + "\n\n" + messages[0]["content"]
            else:
                messages.insert(0, {"role": "system", "content": system_prefix})
        
        elif vendor == "anthropic":
            # Anthropic: System parameter separate from messages
            if "system" in kwargs:
                kwargs["system"] = system_prefix + "\n\n" + kwargs["system"]
            else:
                kwargs["system"] = system_prefix
        
        elif vendor == "google" or vendor == "gemini":
            # Gemini: System instructions parameter
            if "system_instruction" in kwargs:
                kwargs["system_instruction"] = system_prefix + "\n\n" + kwargs["system_instruction"]
            else:
                kwargs["system_instruction"] = system_prefix
        
        else:
            # Fallback: Try system message in messages array
            if messages and messages[0].get("role") == "system":
                messages[0]["content"] = system_prefix + "\n\n" + messages[0]["content"]
            else:
                messages.insert(0, {"role": "system", "content": system_prefix})
        
        return messages, kwargs

    def _log_request(self,
                     model: str,
                     messages: List[Dict[str, str]],
                     **kwargs: Any) -> None:
        """Legacy logging method (kept for compatibility)."""
        logger.info(f"Sending request to model: {model}")
        logger.debug(f"Messages: {messages}")
        logger.debug(f"Additional args: {kwargs}")

    def _log_response(self, response: Any) -> None:
        """Legacy logging method (kept for compatibility)."""
        logger.info("Received response from LLM.")
        logger.debug(f"Response: {response}")

