"""
MessageService - Handles message processing and LLM interactions.
"""

import logging
from typing import Optional, List, Dict, Any, Tuple
from datetime import datetime

from gaia_ceto_v2.core.conversation import Conversation
from gaia_ceto_v2.core.llm_providers import LLMProvider
from gaia_ceto_v2.core.tool_calling_interface import ToolCapableLLM, ToolCall, ToolCallResult, ToolCapableLLMResponse
from gaia_ceto_v2.core.tool_response import handle_tool_response
from gaia_ceto_v2.tools.simple_tools import SimpleToolRegistry

logger = logging.getLogger(__name__)


class MessageService:
    """Focused service for message processing and LLM interactions."""
    
    def __init__(self, llm_provider: LLMProvider, max_context_messages: int = 20):
        self.llm_provider = llm_provider
        self.max_context_messages = max_context_messages
    
    def send_message(self, conversation: Conversation, message: str, request_id: str = None, **llm_kwargs) -> str:
        """Send a message to conversation and get LLM response."""
        if not message.strip():
            raise ValueError("Message cannot be empty")
        
        conversation.add_message('user', message)
        
        context_messages = conversation.get_messages(limit=self.max_context_messages)
        
        logger.debug(f"Generating response with {len(context_messages)} context messages")
        llm_response = self.llm_provider.generate_response(
            prompt=message,
            context=context_messages[:-1],
            request_id=request_id,
            **llm_kwargs
        )
        
        if llm_response.success:
            response = llm_response.content
            logger.debug(f"LLM response successful: {len(response)} characters")
        else:
            error = llm_response.error
            logger.error(f"LLM failed: {error.error_type.value} - {error.message}")
            
            from .llm_errors import ErrorSeverity
            if error.severity == ErrorSeverity.FATAL:
                raise RuntimeError(f"LLM configuration error: {error.message}")
            else:
                response = (f"I apologize, I'm experiencing technical difficulties "
                           f"({error.error_type.value}). Please try again later.")
                logger.warning(f"Using fallback response due to {error.error_type.value}")
        
        conversation.add_message('assistant', response)
        return response
    
    def set_llm_provider(self, llm_provider: LLMProvider) -> None:
        """Change the LLM provider."""
        old_provider = type(self.llm_provider).__name__
        self.llm_provider = llm_provider
        new_provider = type(llm_provider).__name__
        logger.info(f"Changed LLM provider from {old_provider} to {new_provider}")
    
    def get_model_name(self) -> str:
        """Get current LLM model name."""
        return self.llm_provider.get_model_name()
    
    def process_message(self, conversation: Conversation, message: str, 
                       request_id: str, tool_registry: SimpleToolRegistry = None, 
                       **llm_kwargs) -> str:
        """Process a message with optional tool handling.
        
        This method handles both regular LLMs and tool-capable LLMs.
        For tool-capable LLMs, it manages the entire tool execution flow.
        """
        # Check if LLM supports native tool calling
        if isinstance(self.llm_provider, ToolCapableLLM) and self.llm_provider.supports_tools() and tool_registry:
            return self._handle_tool_capable_llm(conversation, message, request_id, tool_registry, **llm_kwargs)
        else:
            # Regular message processing
            return self.send_message(conversation, message, request_id, **llm_kwargs)
    
    def _handle_tool_capable_llm(self, conversation: Conversation, message: str, 
                                request_id: str, tool_registry: SimpleToolRegistry, 
                                **llm_kwargs) -> str:
        """Handle LLM with native tool calling support."""
        try:
            # Add user message to conversation
            conversation.add_message('user', message)
            
            # Get available tools from registry
            available_tools = tool_registry.get_schemas_for_llm()
            
            # Get conversation messages for context
            messages = [{'role': msg.role, 'content': msg.content} for msg in conversation.messages]
            
            # Generate with tools
            llm_response = self.llm_provider.generate_with_tools(
                messages=messages,
                available_tools=available_tools,
                request_id=request_id,
                **llm_kwargs
            )

            # If response from cache is a dict, convert it to ToolCapableLLMResponse
            if isinstance(llm_response, dict):
                # Reconstruct ToolCall objects if they exist
                tool_calls = llm_response.get('tool_calls')
                if tool_calls and isinstance(tool_calls, list):
                    reconstructed_calls = []
                    for call_data in tool_calls:
                        if isinstance(call_data, dict):
                            reconstructed_calls.append(ToolCall(**call_data))
                        else:
                            reconstructed_calls.append(call_data)
                    llm_response['tool_calls'] = reconstructed_calls

                llm_response = ToolCapableLLMResponse(**llm_response)
            
            # Handle tool calls if present
            if llm_response.has_tool_calls:
                tool_results, direct_display_contents = self._execute_tool_calls(
                    llm_response.tool_calls, message, request_id, tool_registry
                )
                
                # Separate tools by synthesis needs
                tools_needing_synthesis = []
                tools_no_synthesis = []
                
                for tr, dc in zip(tool_results, direct_display_contents):
                    if dc:  # needs_synthesis=False
                        tools_no_synthesis.append(tr)
                    else:   # needs_synthesis=True
                        tools_needing_synthesis.append(tr)
                
                # Decide LLM processing strategy
                if not tools_needing_synthesis:
                    # All tools want verbatim - skip LLM entirely
                    logger.info(
                        f"SYNTHESIS_SKIP - {request_id} - All {len(tool_results)} tools have "
                        f"needs_synthesis=False, skipping LLM entirely"
                    )
                    final_response = None
                elif not tools_no_synthesis:
                    # All tools want synthesis - send all to LLM
                    logger.info(
                        f"SYNTHESIS_ALL - {request_id} - All {len(tool_results)} tools have "
                        f"needs_synthesis=True, sending all to LLM"
                    )
                    final_response = self.llm_provider.continue_with_tool_results(
                        messages=messages,
                        tool_results=tool_results,
                        request_id=request_id,
                        **llm_kwargs
                    )
                else:
                    # Mixed case - send only synthesis-needing tools to LLM
                    logger.info(
                        f"SYNTHESIS_PARTIAL - {request_id} - {len(tools_needing_synthesis)} tools need "
                        f"synthesis, {len(tools_no_synthesis)} tools verbatim"
                    )
                    final_response = self.llm_provider.continue_with_tool_results(
                        messages=messages,
                        tool_results=tools_needing_synthesis,  # Only synthesis-needing tools
                        request_id=request_id,
                        **llm_kwargs
                    )
                
                # Combine direct display content with LLM response
                response_parts = []
                
                # Add any direct display content first
                if direct_display_contents:
                    response_parts.extend(direct_display_contents)
                    logger.debug(f"Showing {len(direct_display_contents)} tool results directly")
                
                # Add LLM's response if it has substantive content
                llm_content = final_response.content if final_response else ""
                if llm_content and llm_content.strip() and llm_content != "Tool execution completed.":
                    if response_parts:  # Add separator if we have direct content
                        response_parts.append("---")
                    response_parts.append(llm_content)
                    
                    # Log when we're mixing outputs
                    if direct_display_contents:
                        logger.warning(
                            f"MIXED_OUTPUT - {request_id} - Showing both direct tool results "
                            f"({len(direct_display_contents)} items) AND LLM commentary"
                        )
                
                response_content = "\n\n".join(response_parts) if response_parts else "Tool execution completed."
                
                # Summary log of what was displayed
                logger.info(
                    f"DISPLAY_SUMMARY - {request_id} - "
                    f"direct_tool_results={len(tools_no_synthesis)}, "
                    f"llm_commentary={'yes' if llm_content.strip() else 'no'}, "
                    f"synthesis_tools={len(tools_needing_synthesis)}, "
                    f"verbatim_tools={len(tools_no_synthesis)}"
                )
            else:
                response_content = llm_response.content or "No response generated."
            
            # Add assistant response to conversation
            conversation.add_message('assistant', response_content)
            
            return response_content
            
        except Exception as e:
            logger.error(f"Error in tool-capable LLM handling: {e}")
            # Fallback to regular processing
            return self.send_message(conversation, message, request_id, **llm_kwargs)
    
    def _execute_tool_calls(self, tool_calls: List[ToolCall], user_message: str,
                           request_id: str, tool_registry: SimpleToolRegistry) -> Tuple[List[ToolCallResult], List[str]]:
        """Execute tool calls and return results.
        
        Returns:
            Tuple of (tool_results_for_llm, direct_display_contents)
        """
        tool_results = []
        direct_display_contents = []  # Content to show directly to user
        
        for tool_call in tool_calls:
            # Log what the LLM requested
            logger.debug(f"🤖 LLM requested tool: {tool_call.name}")
            logger.debug(f"   With args: {tool_call.args}")
            
            try:
                # Execute tool directly via simple registry
                raw_result = tool_registry.execute_tool(tool_call.name, **tool_call.args)
                
                # Handle structured tool responses
                processed = handle_tool_response(tool_call.name, raw_result)
                
                # Check if this specific tool result should be shown directly
                show_directly = processed.get('show_to_user', False) and not processed.get('needs_synthesis', False)
                
                if show_directly:
                    # Add to direct display list
                    direct_display_contents.append(processed.get('content', ''))
                    logger.debug(f"Tool {tool_call.name} will display results directly")
                
                # Add to context if tool wants it
                if processed['context_content']:
                    logger.debug(f"Tool {tool_call.name} added to context: {len(processed['context_content'])} chars")
                
                # Use processed content for tool result
                content = processed['content'] or processed['context_content'] or str(raw_result)
                
                # Always add to tool results for LLM context (even if showing directly)
                tool_results.append(ToolCallResult(
                    tool_call_id=tool_call.id,
                    name=tool_call.name,
                    content=content
                ))
            except Exception as e:
                tool_results.append(ToolCallResult(
                    tool_call_id=tool_call.id,
                    name=tool_call.name,
                    content="",
                    error=str(e)
                ))
        
        return tool_results, direct_display_contents