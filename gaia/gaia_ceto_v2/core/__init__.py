"""
Gaia Ceto v2 Core - Pure Business Logic

This package contains the core business logic for chat functionality
with NO external dependencies or framework references.

Core Principles:
- No Django imports or references
- No web framework dependencies  
- No protocol-specific code
- Pure Python business logic only
- Framework-agnostic and testable

The core can be used by any interface layer (Django, MCP, HTTP, CLI, etc.)
without modification.
"""

__version__ = "2.0.0-core"

# Core business logic exports
from .conversation import Conversation, Message, conversation_summary, create_conversation
from .llm_providers import (
    LLMProvider, MockLLM, OpenAILLM, AnthropicLLM, LiteLLM,
    create_llm_provider, get_available_providers, provider_requires_api_key
)
# Storage system imports
from .storage_interface import ConversationStorage
from .file_storage import FileConversationRepository
from .memory_storage import MemoryStorage
# Simple absolute imports from tools
try:
    from ..tools import (
        SimpleToolRegistry, 
        default_registry
    )
except ImportError:
    import sys
    sys.path.insert(0, '/usr/local/agfunder/agbase_admin/gaia')
    from gaia_ceto_v2.tools import (
        SimpleToolRegistry, 
        default_registry
    )
from .chat_manager import Chat<PERSON>anager
from .chat_manager_factory import create_chat_manager

__all__ = [
    # Data models
    'Conversation', 'Message', 'conversation_summary',
    
    # LLM providers
    'LLMProvider', 'MockLLM', 'OpenAILLM', 'AnthropicLLM', 'LiteLLM',
    'create_llm_provider', 'get_available_providers', 'provider_requires_api_key',
    
    # Storage backends
    'ConversationStorage', 'FileConversationRepository', 'MemoryStorage',
    
    # Tools
    'Tool', 'FunctionTool', 'ToolRegistry',
    'default_registry', 'create_tool_registry',
    'echostring', 'get_time', 'add_numbers',
    
    # Core orchestration
    'ChatManager', 'create_chat_manager'
]