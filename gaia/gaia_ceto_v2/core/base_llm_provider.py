"""
Base LLM Provider - Template method pattern for LLM implementations.

Extracted from llm_providers.py to reduce code duplication and provide
common functionality. Uses template method pattern so concrete providers
only need to implement the actual API call.
"""

import logging
import time
from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional

from gaia_ceto_v2.core.llm_providers import LLMProvider
from gaia_ceto_v2.tools.tools import ToolRegistry

logger = logging.getLogger(__name__)


class BaseLLMProvider(LLMProvider):
    """Base implementation providing common LLM functionality.
    
    Uses template method pattern to eliminate code duplication between
    different LLM providers. Concrete providers only need to implement
    the actual API call method.
    """
    
    def __init__(self, 
                 tool_registry: Optional[CetoToolRegistryInterface] = None,
                 max_retries: int = 3,
                 timeout_seconds: int = 30):
        """Initialize base LLM provider.
        
        Args:
            tool_registry: Optional tool registry for tool calling
            max_retries: Maximum number of API call retries
            timeout_seconds: Request timeout in seconds
        """
        super().__init__(tool_registry)
        self.max_retries = max_retries
        self.timeout_seconds = timeout_seconds
        
        logger.info(f"BaseLLMProvider initialized: retries={max_retries}, timeout={timeout_seconds}s")
    
    def generate_response(self, prompt: str, context: List[Dict[str, Any]], request_id: str = None, **kwargs) -> str:
        """Generate response using template method pattern.
        
        This method orchestrates the entire response generation process:
        1. Validate inputs
        2. Prepare request with context and tools
        3. Call LLM API (implemented by subclasses)
        4. Process response and handle tool calls
        5. Return final response
        
        Args:
            prompt: User's input prompt
            context: Previous conversation messages
            **kwargs: Provider-specific parameters
            
        Returns:
            Generated response string
        """
        start_time = time.time()
        
        try:
            # 1. Validate inputs
            if not prompt or not prompt.strip():
                raise ValueError("Prompt cannot be empty")
            
            # 2. Prepare request
            prepared_request = self._prepare_request(prompt, context, **kwargs)
            
            # 3. Call LLM API with retries
            response = self._call_with_retries(prepared_request)
            
            # 4. Process response
            final_response = self._process_response(response, **kwargs)
            
            # 5. Log metrics
            duration = time.time() - start_time
            logger.info(f"Generated response in {duration:.2f}s using {self.get_model_name()}")
            
            return final_response
            
        except Exception as e:
            duration = time.time() - start_time
            logger.error(f"Error generating response after {duration:.2f}s: {e}")
            return self._get_error_response(str(e))
    
    @abstractmethod
    def call_llm_api(self, prepared_request: Dict[str, Any]) -> Dict[str, Any]:
        """Call the actual LLM API.
        
        This is the only method concrete providers need to implement.
        
        Args:
            prepared_request: Prepared request dictionary
            
        Returns:
            Raw API response dictionary
        """
        pass
    
    def _prepare_request(self, prompt: str, context: List[Dict[str, Any]], **kwargs) -> Dict[str, Any]:
        """Prepare request for LLM API.
        
        Common preparation logic including:
        - Message formatting
        - Tool definitions (if tools available)
        - System prompts
        - Parameter validation
        """
        # Build message history
        messages = []
        
        # Add system message if specified
        system_prompt = kwargs.get('system_prompt')
        if system_prompt:
            messages.append({
                'role': 'system',
                'content': system_prompt
            })
        
        # Add context messages
        for msg in context:
            if isinstance(msg, dict) and 'role' in msg and 'content' in msg:
                messages.append({
                    'role': msg['role'],
                    'content': msg['content']
                })
        
        # Add current prompt
        messages.append({
            'role': 'user',
            'content': prompt
        })
        
        # Prepare base request
        request = {
            'messages': messages,
            'max_tokens': kwargs.get('max_tokens', 4000),
            'temperature': kwargs.get('temperature', 0.7),
            'timeout': self.timeout_seconds
        }
        
        # Add tool definitions if tools available
        if self.tool_registry:
            try:
                tools = self.tool_registry.list_tools()
                if tools:
                    request['tools'] = self._format_tools_for_api(tools)
                    logger.debug(f"Added {len(tools)} tools to request")
            except Exception as e:
                logger.warning(f"Error adding tools to request: {e}")
        
        return request
    
    def _call_with_retries(self, prepared_request: Dict[str, Any]) -> Dict[str, Any]:
        """Call LLM API with retry logic."""
        last_error = None
        
        for attempt in range(self.max_retries):
            try:
                logger.debug(f"LLM API call attempt {attempt + 1}/{self.max_retries}")
                response = self.call_llm_api(prepared_request)
                
                if response:
                    return response
                else:
                    raise ValueError("Empty response from LLM API")
                    
            except Exception as e:
                last_error = e
                logger.warning(f"LLM API call attempt {attempt + 1} failed: {e}")
                
                # Don't retry on certain errors
                if self._is_non_retryable_error(e):
                    break
                
                # Wait before retry (exponential backoff)
                if attempt < self.max_retries - 1:
                    wait_time = 2 ** attempt
                    logger.debug(f"Waiting {wait_time}s before retry")
                    time.sleep(wait_time)
        
        # All retries failed
        raise last_error or Exception("All LLM API retries failed")
    
    def _process_response(self, response: Dict[str, Any], **kwargs) -> str:
        """Process LLM API response.
        
        Common response processing including:
        - Extract text content
        - Handle tool calls
        - Validate response format
        """
        try:
            # Extract main response text
            response_text = self._extract_response_text(response)
            
            # Handle tool calls if present
            if self.tool_registry and self._has_tool_calls(response):
                response_text = self._handle_tool_calls(response, response_text)
            
            # Validate and clean response
            if not response_text or not response_text.strip():
                return self._get_fallback_response()
            
            return response_text.strip()
            
        except Exception as e:
            logger.error(f"Error processing LLM response: {e}")
            return self._get_error_response(str(e))
    
    def _extract_response_text(self, response: Dict[str, Any]) -> str:
        """Extract text content from API response.
        
        Default implementation for common response formats.
        Subclasses can override for provider-specific formats.
        """
        # Try common response formats
        if 'content' in response:
            return str(response['content'])
        elif 'text' in response:
            return str(response['text'])
        elif 'message' in response and isinstance(response['message'], dict):
            return str(response['message'].get('content', ''))
        elif 'choices' in response and response['choices']:
            # OpenAI-style format
            choice = response['choices'][0]
            if 'message' in choice:
                return str(choice['message'].get('content', ''))
        
        # Fallback: convert entire response to string
        logger.warning("Could not extract text from response, using string conversion")
        return str(response)
    
    def _format_tools_for_api(self, tools: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Format tools for API request.
        
        Default implementation. Subclasses can override for provider-specific formats.
        """
        formatted_tools = []
        
        for tool in tools:
            formatted_tool = {
                'type': 'function',
                'function': {
                    'name': tool.get('name', ''),
                    'description': tool.get('description', ''),
                    'parameters': tool.get('schema', {})
                }
            }
            formatted_tools.append(formatted_tool)
        
        return formatted_tools
    
    def _has_tool_calls(self, response: Dict[str, Any]) -> bool:
        """Check if response contains tool calls."""
        # Default implementation for common formats
        return (
            'tool_calls' in response or
            ('choices' in response and response['choices'] and 
             'tool_calls' in response['choices'][0].get('message', {}))
        )
    
    def _handle_tool_calls(self, response: Dict[str, Any], response_text: str) -> str:
        """Handle tool calls in response."""
        try:
            # Extract tool calls (provider-specific implementation needed)
            tool_calls = self._extract_tool_calls(response)
            
            tool_results = []
            for tool_call in tool_calls:
                try:
                    tool_name = tool_call.get('name')
                    tool_args = tool_call.get('arguments', {})
                    
                    if tool_name and self.tool_registry.has_tool(tool_name):
                        result = self.tool_registry.execute_tool(tool_name, **tool_args)
                        tool_results.append(f"Tool {tool_name}: {result}")
                        logger.debug(f"Executed tool {tool_name} successfully")
                    
                except Exception as e:
                    logger.error(f"Error executing tool call: {e}")
                    tool_results.append(f"Tool error: {e}")
            
            # Combine response text with tool results
            if tool_results:
                return f"{response_text}\n\nTool Results:\n" + "\n".join(tool_results)
            
            return response_text
            
        except Exception as e:
            logger.error(f"Error handling tool calls: {e}")
            return response_text
    
    def _extract_tool_calls(self, response: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Extract tool calls from response.
        
        Default implementation. Subclasses should override for provider-specific formats.
        """
        # This is a placeholder - actual implementation depends on provider format
        return []
    
    def _is_non_retryable_error(self, error: Exception) -> bool:
        """Check if error should not be retried."""
        error_str = str(error).lower()
        
        # Don't retry on authentication/authorization errors
        non_retryable_keywords = [
            'authentication', 'authorization', 'forbidden', 'unauthorized',
            'invalid api key', 'quota exceeded', 'invalid request'
        ]
        
        return any(keyword in error_str for keyword in non_retryable_keywords)
    
    def _get_error_response(self, error_message: str) -> str:
        """Get appropriate error response for user."""
        return f"I apologize, but I encountered an error while generating a response: {error_message}. Please try again."
    
    def _get_fallback_response(self) -> str:
        """Get fallback response when API returns empty result."""
        return "I apologize, but I couldn't generate a proper response. Please try rephrasing your question."
    
    def get_provider_stats(self) -> Dict[str, Any]:
        """Get provider-specific statistics."""
        return {
            'provider_type': self.__class__.__name__,
            'model_name': self.get_model_name(),
            'max_retries': self.max_retries,
            'timeout_seconds': self.timeout_seconds,
            'tools_available': self.tool_registry is not None,
            'tool_count': len(self.tool_registry.list_tools()) if self.tool_registry else 0
        }