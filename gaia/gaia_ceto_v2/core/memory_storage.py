"""
Memory Storage Implementation - Simple in-memory storage for testing.

LEVELS.md compliant: Simple, focused, no complex abstractions.
Used for testing and demonstration purposes only.
"""

import logging
from typing import Dict, List, Any, Optional

from .storage_interface import ConversationStorage
from .conversation import Conversation
from .conversation_serializer import ConversationSerializer

logger = logging.getLogger(__name__)


class MemoryStorage(ConversationStorage):
    """Simple in-memory storage for conversations.
    
    LEVELS.md compliant: Minimal implementation for testing.
    No persistence, no complex logic - just a dictionary.
    """
    
    def __init__(self):
        """Initialize empty memory storage."""
        self._conversations: Dict[str, Conversation] = {}
        logger.debug("MemoryStorage initialized")
    
    def save(self, conversation: Conversation) -> bool:
        """Save conversation to memory."""
        if not conversation or not conversation.conversation_id:
            return False
        
        # Store a copy to avoid reference issues
        self._conversations[conversation.conversation_id] = conversation
        logger.debug(f"Saved conversation {conversation.conversation_id} to memory")
        return True
    
    def load(self, conversation_id: str) -> Optional[Conversation]:
        """Load conversation from memory."""
        if not conversation_id:
            return None
        
        conversation = self._conversations.get(conversation_id)
        if conversation:
            logger.debug(f"Loaded conversation {conversation_id} from memory")
        return conversation
    
    def delete(self, conversation_id: str) -> bool:
        """Delete conversation from memory."""
        if not conversation_id:
            return False
        
        if conversation_id in self._conversations:
            del self._conversations[conversation_id]
            logger.debug(f"Deleted conversation {conversation_id} from memory")
            return True
        return False
    
    def exists(self, conversation_id: str) -> bool:
        """Check if conversation exists in memory."""
        return conversation_id in self._conversations if conversation_id else False
    
    def list_conversations(self, user_id: str = None) -> List[Dict[str, Any]]:
        """List conversations with optional user filter."""
        conversations = []
        
        for conv in self._conversations.values():
            if user_id is None or conv.user_id == user_id:
                summary = ConversationSerializer.to_summary_dict(conv)
                conversations.append(summary)
        
        # Sort by updated_at (most recent first)
        conversations.sort(
            key=lambda x: x.get('updated_at', ''),
            reverse=True
        )
        
        logger.debug(f"Listed {len(conversations)} conversations for user {user_id or 'all'}")
        return conversations
    
    def clear(self) -> None:
        """Clear all conversations from memory."""
        count = len(self._conversations)
        self._conversations.clear()
        logger.debug(f"Cleared {count} conversations from memory")
    
    def get_stats(self) -> Dict[str, Any]:
        """Get memory storage statistics."""
        return {
            'total_conversations': len(self._conversations),
            'storage_type': 'memory',
            'memory_usage_approx_mb': len(str(self._conversations)) / (1024 * 1024)
        }