"""
Simplified Storage Interface - Single interface for conversation persistence.

Eliminates over-abstraction while maintaining clean separation between
business logic and storage implementation. LEVELS.md compliant.
"""

from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional

from .conversation import Conversation


class ConversationStorage(ABC):
    """Simple, focused interface for conversation persistence.
    
    Single interface that business logic depends on. Avoids interface
    segregation overhead while remaining testable and swappable.
    """
    
    @abstractmethod
    def save(self, conversation: Conversation) -> bool:
        """Save a conversation."""
        pass
    
    @abstractmethod
    def load(self, conversation_id: str) -> Optional[Conversation]:
        """Load a conversation by ID."""
        pass
    
    @abstractmethod
    def delete(self, conversation_id: str) -> bool:
        """Delete a conversation."""
        pass
    
    @abstractmethod
    def list_conversations(self, user_id: str = None) -> List[Dict[str, Any]]:
        """List conversations with optional user filter."""
        pass
    
    @abstractmethod
    def exists(self, conversation_id: str) -> bool:
        """Check if a conversation exists."""
        pass