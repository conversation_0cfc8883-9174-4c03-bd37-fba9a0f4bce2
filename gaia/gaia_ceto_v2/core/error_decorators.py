"""
Error handling decorators for standardizing error patterns across core services.

Eliminates repeated error handling code while maintaining consistent user experience
and debugging capabilities. Follows LEVELS.md requirement for easy-to-debug code.
"""

import logging
import functools
from typing import Any, Callable, Optional, Dict, Union
from datetime import datetime

from .llm_errors import LLMResponse, LLMContentError, LLMErrorType, ErrorSeverity
from .level_0004_simple import log_existing_timing

logger = logging.getLogger(__name__)


def llm_error_handler(operation_name: str = None, 
                     fallback_response: str = None,
                     log_errors: bool = True):
    """Decorator for standardizing LLM call error handling.
    
    Automatically converts exceptions to LLMResponse.error_response and provides
    consistent error logging and fallback responses.
    
    Args:
        operation_name: Name of operation for logging (defaults to function name)
        fallback_response: Fallback response text for user-facing errors
        log_errors: Whether to log errors (defaults to True)
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            op_name = operation_name or func.__name__
            start_time = datetime.now()
            
            try:
                result = func(*args, **kwargs)
                
                # Handle LLMResponse objects
                if isinstance(result, LLMResponse):
                    if not result.success and log_errors:
                        error = result.error
                        duration_ms = (datetime.now() - start_time).total_seconds() * 1000 
                        logger.error(f"{op_name} failed: {error.error_type.value} - {error.message} ({duration_ms:.0f}ms)")
                        
                        # Provide fallback for non-fatal errors
                        if error.severity != ErrorSeverity.FATAL and fallback_response:
                            logger.info(f"Using fallback response for {op_name}")
                            return LLMResponse.success_response(
                                content=fallback_response,
                                metadata={'fallback': True, 'original_error': error.message}
                            )
                
                return result
                
            except Exception as e:
                duration_ms = (datetime.now() - start_time).total_seconds() * 1000
                
                if log_errors:
                    logger.error(f"{op_name} exception: {type(e).__name__}: {str(e)} ({duration_ms:.0f}ms)", exc_info=True)
                
                # Level 0004: Log timing for decorator error
                log_existing_timing("decorator", f"{op_name}_error", start_time, {
                    "operation": op_name, "error": True, "error_type": type(e).__name__
                })
                
                # Create structured error response
                error = LLMContentError(
                    message=f"{op_name} failed: {str(e)}",
                    error_type=LLMErrorType.UNKNOWN,
                    details={
                        'operation': op_name,
                        'exception_type': type(e).__name__,
                        'duration_ms': duration_ms
                    }
                )
                
                return LLMResponse.error_response(error)
        
        return wrapper
    return decorator


def service_error_handler(service_name: str = None,
                         return_value: Any = None,
                         log_errors: bool = True,
                         record_stats: bool = False,
                         stats_recorder: Callable = None):
    """Decorator for standardizing service-level error handling.
    
    Provides consistent error logging, statistics recording, and return values
    for service methods that don't use LLMResponse.
    
    Args:
        service_name: Name of service for logging (defaults to class name)
        return_value: Value to return on error (defaults to None)
        log_errors: Whether to log errors
        record_stats: Whether to record error statistics
        stats_recorder: Function to call for stats recording
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(self, *args, **kwargs):
            svc_name = service_name or getattr(self, '__class__', type(self)).__name__
            method_name = func.__name__
            full_name = f"{svc_name}.{method_name}"
            start_time = datetime.now()
            
            try:
                return func(self, *args, **kwargs)
                
            except Exception as e:
                duration_ms = (datetime.now() - start_time).total_seconds() * 1000
                
                if log_errors:
                    logger.error(f"{full_name} failed: {type(e).__name__}: {str(e)} ({duration_ms:.0f}ms)", exc_info=True)
                
                # Record statistics if requested
                if record_stats and hasattr(self, 'statistics') and self.statistics:
                    try:
                        self.statistics.record_error(f"{svc_name.lower()}_{method_name}", str(e))
                    except Exception as stats_error:
                        logger.warning(f"Failed to record error stats: {stats_error}")
                
                # Call custom stats recorder if provided
                if stats_recorder:
                    try:
                        stats_recorder(self, full_name, e, duration_ms)
                    except Exception as stats_error:
                        logger.warning(f"Custom stats recorder failed: {stats_error}")
                
                return return_value
        
        return wrapper
    return decorator


def conversation_error_handler(operation_name: str = None):
    """Decorator specifically for conversation operations.
    
    Handles conversation-specific error patterns and ensures consistent
    error responses for conversation management operations.
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(self, conversation_id: str, *args, **kwargs):
            op_name = operation_name or func.__name__
            
            try:
                return func(self, conversation_id, *args, **kwargs)
                
            except Exception as e:
                logger.error(f"Conversation {op_name} failed for {conversation_id}: {e}", exc_info=True)
                
                # Record error statistics if available
                if hasattr(self, 'statistics') and self.statistics:
                    try:
                        self.statistics.record_error(f"conversation_{op_name}", str(e))
                    except Exception:
                        pass  # Don't fail operation for stats errors
                
                # Return appropriate default for operation type
                if op_name in ['delete_conversation', 'update_conversation_title', 
                              'clear_conversation_history']:
                    return False
                elif op_name in ['list_conversations']:
                    return []
                elif op_name in ['get_conversation', 'get_conversation_stats']:
                    return None
                else:
                    # Re-raise for operations that should propagate errors
                    raise
        
        return wrapper
    return decorator


def tool_error_handler(tool_name: str = None,
                      error_response_format: str = "text"):
    """Decorator for tool execution error handling.
    
    Standardizes error handling for tool execution with consistent formatting
    for both text and structured responses.
    
    Args:
        tool_name: Name of tool for logging (defaults to function name)
        error_response_format: Format for error response ("text" or "structured")
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            t_name = tool_name or func.__name__
            
            try:
                return func(*args, **kwargs)
                
            except Exception as e:
                logger.error(f"Tool {t_name} execution failed: {e}", exc_info=True)
                
                if error_response_format == "structured":
                    # Return structured error response
                    return {
                        'success': False,
                        'error': str(e),
                        'tool_name': t_name,
                        'error_type': type(e).__name__
                    }
                else:
                    # Return text error response
                    return f"Tool '{t_name}' failed: {str(e)}"
        
        return wrapper
    return decorator


def cache_error_handler(cache_operation: str = None,
                       fallback_value: Any = None):
    """Decorator for cache operation error handling.
    
    Ensures cache failures don't break main functionality by providing
    graceful fallbacks and logging.
    
    Args:
        cache_operation: Name of cache operation for logging
        fallback_value: Value to return on cache error
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            op_name = cache_operation or func.__name__
            
            try:
                return func(*args, **kwargs)
                
            except Exception as e:
                logger.warning(f"Cache {op_name} failed: {e} - continuing without cache")
                return fallback_value
        
        return wrapper
    return decorator


# Utility functions for common error scenarios
def create_fallback_llm_response(message: str, 
                                error_type: LLMErrorType = LLMErrorType.UNKNOWN) -> LLMResponse:
    """Create a fallback LLM response for user-facing errors."""
    return LLMResponse.success_response(
        content=(f"I apologize, I'm experiencing technical difficulties "
                f"({error_type.value}). Please try again later."),
        metadata={'fallback': True, 'original_message': message}
    )


def log_service_error(service_name: str, 
                     method_name: str, 
                     error: Exception, 
                     duration_ms: float,
                     context: Dict[str, Any] = None):
    """Standardized service error logging."""
    context_str = f" Context: {context}" if context else ""
    logger.error(
        f"{service_name}.{method_name} failed: {type(error).__name__}: "
        f"{str(error)} ({duration_ms:.0f}ms){context_str}",
        exc_info=True
    )


def should_use_fallback_response(error: Exception) -> bool:
    """Determine if an error should use a fallback response vs propagate."""
    # Don't use fallback for configuration or fatal errors
    fatal_error_types = (
        ValueError,  # Usually configuration errors
        TypeError,   # Programming errors
        ImportError, # Missing dependencies
    )
    
    return not isinstance(error, fatal_error_types)