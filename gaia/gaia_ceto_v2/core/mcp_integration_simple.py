"""
Simplified MCP Integration using decorators instead of inheritance.

Replaces complex wrapper hierarchy with clean, decorated methods.
"""

import logging
import json
from typing import List, Dict, Any, Optional
import uuid

from .logging_decorators import log_mcp_operation, log_tool_operation
from tools.tools import echostring as core_echostring
from tools.exa import ExaTools

logger = logging.getLogger(__name__)


class SimpleMCPToolHandler:
    """Clean MCP tool handler using decorators for logging."""
    
    def __init__(self, server_url: str = "http://localhost:9000/mcp/", 
                 simple_cache=None, accounting_system=None):
        """
        Initialize MCP handler.
        
        Args:
            server_url: MCP server URL
            simple_cache: Optional cache for tool calls
            accounting_system: Cost tracking system
        """
        self.server_url = server_url
        self.simple_cache = simple_cache
        self.accounting = accounting_system
        self._tools_cache = []
        
        # Initialize Exa tools
        self.exa_tools = ExaTools(simple_cache=simple_cache)
        
        logger.info(f"MCP handler initialized for {server_url}")
    
    @log_mcp_operation("get_tools")
    def get_available_tools(self, request_id: str = None) -> List[str]:
        """Get list of available tools with automatic logging."""
        # In production, this would query the MCP server
        return ["echostring", "get_time", "weather", "exa_search", "exa_research"]
    
    @log_mcp_operation("execute_tool")
    def execute_single_tool(self, tool_name: str, args: Dict[str, Any], 
                          request_id: str = None) -> Dict[str, Any]:
        """
        Execute a single MCP tool with automatic logging.
        
        Args:
            tool_name: Name of tool to execute
            args: Tool arguments
            request_id: Optional request ID
            
        Returns:
            Tool execution result
        """
        # Check cache if available
        if self.simple_cache:
            cache_key = self._generate_cache_key(tool_name, args)
            cached = self.simple_cache.get(cache_key)
            if cached:
                logger.info(f"🗃️ Cache hit for {tool_name}")
                return cached
        
        try:
            # Route to tool implementation
            result = self._execute_tool_internal(tool_name, args)
            
            # Cache result if available
            if self.simple_cache and result.get('success'):
                self.simple_cache.set(cache_key, result, ttl=300)  # 5 min TTL
            
            # Track costs if available
            if self.accounting and request_id:
                self._track_api_usage(tool_name, request_id)
            
            return result
            
        except Exception as e:
            logger.error(f"Tool execution failed: {e}")
            return {
                'success': False,
                'error': str(e),
                'tool': tool_name
            }
    
    def _execute_tool_internal(self, tool_name: str, args: Dict[str, Any]) -> Dict[str, Any]:
        """Internal tool execution logic."""
        if tool_name == "echostring":
            return self._execute_echostring(args)
        elif tool_name == "get_time":
            return self._execute_get_time(args)
        elif tool_name == "weather":
            return self._execute_weather(args)
        elif tool_name in ["exa_search", "exa_research"]:
            return self._execute_exa_tool(tool_name, args)
        else:
            raise ValueError(f"Unknown tool: {tool_name}")
    
    @log_tool_operation("echostring")
    def _execute_echostring(self, args: Dict[str, Any], request_id: str = None) -> Dict[str, Any]:
        """Execute echostring tool."""
        message = args.get('message', '')
        result = core_echostring(message)
        return {
            'success': True,
            'result': result
        }
    
    @log_tool_operation("get_time")
    def _execute_get_time(self, args: Dict[str, Any], request_id: str = None) -> Dict[str, Any]:
        """Execute get_time tool."""
        import datetime
        return {
            'success': True,
            'result': datetime.datetime.now().isoformat()
        }
    
    @log_tool_operation("weather")
    def _execute_weather(self, args: Dict[str, Any], request_id: str = None) -> Dict[str, Any]:
        """Execute weather tool."""
        location = args.get('location', 'Unknown')
        # Mock implementation
        return {
            'success': True,
            'result': f"Weather in {location}: Sunny, 72°F"
        }
    
    @log_tool_operation("exa")
    def _execute_exa_tool(self, tool_name: str, args: Dict[str, Any], 
                         request_id: str = None) -> Dict[str, Any]:
        """Execute Exa search/research tools."""
        if tool_name == "exa_search":
            results = self.exa_tools.search(**args)
        else:  # exa_research
            results = self.exa_tools.research(**args)
            
        return {
            'success': True,
            'result': results
        }
    
    def _generate_cache_key(self, tool_name: str, args: Dict[str, Any]) -> str:
        """Generate cache key for tool calls."""
        import hashlib
        key_data = f"{tool_name}:{json.dumps(args, sort_keys=True)}"
        return hashlib.md5(key_data.encode()).hexdigest()
    
    def _track_api_usage(self, tool_name: str, request_id: str):
        """Track API usage for accounting."""
        if not self.accounting:
            return
            
        try:
            # Simple tracking - could be enhanced
            self.accounting.record_transaction(
                request_id=request_id,
                service='mcp',
                operation=tool_name,
                api_calls=1
            )
        except Exception as e:
            logger.warning(f"API tracking failed: {e}")
    
    def get_tool_schemas(self) -> List[Dict[str, Any]]:
        """Get tool schemas for LLM integration."""
        # Simplified schema generation
        return [
            {
                "name": "echostring",
                "description": "Echo back a message",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "message": {"type": "string", "description": "Message to echo"}
                    },
                    "required": ["message"]
                }
            },
            {
                "name": "get_time",
                "description": "Get current time",
                "parameters": {"type": "object", "properties": {}}
            },
            {
                "name": "weather",
                "description": "Get weather for a location",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "location": {"type": "string", "description": "Location name"}
                    },
                    "required": ["location"]
                }
            },
            {
                "name": "exa_search",
                "description": "Search the web using Exa",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "query": {"type": "string", "description": "Search query"}
                    },
                    "required": ["query"]
                }
            }
        ]


# Factory function
def create_mcp_handler(**kwargs) -> SimpleMCPToolHandler:
    """Create an MCP handler with the specified configuration."""
    return SimpleMCPToolHandler(**kwargs)