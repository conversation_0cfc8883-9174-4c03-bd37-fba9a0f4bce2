"""
Conversation Serializer - Clean separation of domain and persistence concerns.

Extracted serialization logic from Conversation domain model to follow
Single Responsibility Principle. Handles conversion between domain objects
and storage formats without polluting the domain model.
"""

import logging
from typing import Dict, Any, Optional, List
from datetime import datetime

from .conversation import Conversation, Message

logger = logging.getLogger(__name__)


class ConversationSerializer:
    """Handles serialization/deserialization of Conversation objects.
    
    Separates persistence concerns from domain logic, allowing the
    Conversation class to focus purely on business rules while this
    class handles data format transformations.
    """
    
    @staticmethod
    def to_dict(conversation: Conversation) -> Dict[str, Any]:
        """Convert Conversation object to dictionary format.
        
        Args:
            conversation: Conversation object to serialize
            
        Returns:
            Dictionary representation suitable for storage
        """
        if not conversation:
            raise ValueError("Cannot serialize None conversation")
        
        try:
            # Convert messages to dictionaries
            messages_data = []
            for message in conversation.messages:
                message_dict = {
                    'role': message.role,
                    'content': message.content,
                    'timestamp': message.timestamp
                }
                
                # Include metadata if present
                if hasattr(message, 'metadata') and message.metadata:
                    message_dict['metadata'] = message.metadata
                
                messages_data.append(message_dict)
            
            # Build conversation dictionary
            conversation_dict = {
                'conversation_id': conversation.conversation_id,
                'user_id': conversation.user_id,
                'title': conversation.title,
                'created_at': conversation.created_at,
                'updated_at': conversation.updated_at,
                'messages': messages_data,
                'message_count': len(messages_data)
            }
            
            # Include metadata if present
            if hasattr(conversation, 'metadata') and conversation.metadata:
                conversation_dict['metadata'] = conversation.metadata
            
            logger.debug(f"Serialized conversation {conversation.conversation_id} to dict")
            return conversation_dict
            
        except Exception as e:
            logger.error(f"Error serializing conversation {getattr(conversation, 'conversation_id', 'unknown')}: {e}")
            raise
    
    @staticmethod
    def from_dict(data: Dict[str, Any]) -> Optional[Conversation]:
        """Convert dictionary to Conversation object.
        
        Args:
            data: Dictionary representation of conversation
            
        Returns:
            Conversation object or None if invalid data
        """
        if not data or not isinstance(data, dict):
            logger.warning("Cannot deserialize invalid conversation data")
            return None
        
        try:
            # Validate required fields
            required_fields = ['conversation_id', 'user_id', 'created_at']
            missing_fields = [field for field in required_fields if field not in data]
            if missing_fields:
                logger.error(f"Missing required fields in conversation data: {missing_fields}")
                return None
            
            # Create conversation with basic fields
            conversation = Conversation(
                user_id=data['user_id'],
                title=data.get('title', 'Untitled Conversation'),
                conversation_id=data['conversation_id'],
                **data.get('metadata', {})
            )
            
            # Set timestamps if present (after construction)
            if 'created_at' in data:
                conversation.created_at = data['created_at']
            if 'updated_at' in data:
                conversation.updated_at = data['updated_at']
            
            # Add messages if present
            messages_data = data.get('messages', [])
            if messages_data:
                # Clear any default messages that might have been added
                conversation.messages = []
                
                for msg_data in messages_data:
                    if not isinstance(msg_data, dict):
                        logger.warning(f"Skipping invalid message data in conversation {conversation.conversation_id}")
                        continue
                    
                    # Validate message fields
                    if 'role' not in msg_data or 'content' not in msg_data:
                        logger.warning(f"Skipping message with missing role/content in conversation {conversation.conversation_id}")
                        continue
                    
                    # Create message object
                    message = Message(
                        role=msg_data['role'],
                        content=msg_data['content'],
                        timestamp=msg_data.get('timestamp', datetime.now().isoformat())
                    )
                    
                    # Add metadata if present (as attribute, not constructor param)
                    if 'metadata' in msg_data and msg_data['metadata']:
                        message.metadata = msg_data['metadata']
                    
                    conversation.messages.append(message)
            
            logger.debug(f"Deserialized conversation {conversation.conversation_id} from dict")
            return conversation
            
        except Exception as e:
            logger.error(f"Error deserializing conversation data: {e}")
            return None
    
    @staticmethod
    def to_summary_dict(conversation: Conversation) -> Dict[str, Any]:
        """Convert Conversation to summary format (lightweight).
        
        Args:
            conversation: Conversation object to summarize
            
        Returns:
            Dictionary with conversation summary data
        """
        if not conversation:
            raise ValueError("Cannot summarize None conversation")
        
        try:
            return {
                'conversation_id': conversation.conversation_id,
                'user_id': conversation.user_id,
                'title': conversation.title,
                'created_at': conversation.created_at,
                'updated_at': conversation.updated_at,
                'message_count': len(conversation.messages),
                'last_message_preview': (
                    conversation.messages[-1].content[:100] + '...'
                    if conversation.messages and len(conversation.messages[-1].content) > 100
                    else conversation.messages[-1].content
                    if conversation.messages else None
                )
            }
            
        except Exception as e:
            logger.error(f"Error creating conversation summary: {e}")
            raise
    
    @staticmethod
    def validate_conversation_data(data: Dict[str, Any]) -> List[str]:
        """Validate conversation dictionary for required fields and format.
        
        Args:
            data: Dictionary to validate
            
        Returns:
            List of validation errors (empty if valid)
        """
        errors = []
        
        if not isinstance(data, dict):
            errors.append("Data must be a dictionary")
            return errors
        
        # Check required fields
        required_fields = {
            'conversation_id': str,
            'user_id': str,
            'created_at': str
        }
        
        for field, expected_type in required_fields.items():
            if field not in data:
                errors.append(f"Missing required field: {field}")
            elif not isinstance(data[field], expected_type):
                errors.append(f"Field {field} must be of type {expected_type.__name__}")
        
        # Validate messages if present
        if 'messages' in data:
            if not isinstance(data['messages'], list):
                errors.append("Messages field must be a list")
            else:
                for i, msg in enumerate(data['messages']):
                    if not isinstance(msg, dict):
                        errors.append(f"Message {i} must be a dictionary")
                        continue
                    
                    if 'role' not in msg:
                        errors.append(f"Message {i} missing required field: role")
                    elif not isinstance(msg['role'], str):
                        errors.append(f"Message {i} role must be a string")
                    
                    if 'content' not in msg:
                        errors.append(f"Message {i} missing required field: content")
                    elif not isinstance(msg['content'], str):
                        errors.append(f"Message {i} content must be a string")
        
        return errors
    
    @staticmethod
    def migrate_conversation_format(data: Dict[str, Any], from_version: str = "1.0") -> Dict[str, Any]:
        """Migrate conversation data from older formats.
        
        Args:
            data: Conversation data in old format
            from_version: Version to migrate from
            
        Returns:
            Migrated conversation data
        """
        # This is a placeholder for future format migrations
        # Currently just validates and returns the same data
        
        logger.info(f"Migration requested from version {from_version} (no changes needed)")
        
        # Ensure message_count is present
        if 'messages' in data and 'message_count' not in data:
            data['message_count'] = len(data['messages'])
        
        return data


# Convenience functions for common operations
def conversation_to_json_dict(conversation: Conversation) -> Dict[str, Any]:
    """Convert conversation to JSON-serializable dictionary.
    
    Args:
        conversation: Conversation to convert
        
    Returns:
        JSON-serializable dictionary
    """
    return ConversationSerializer.to_dict(conversation)


def conversation_from_json_dict(data: Dict[str, Any]) -> Optional[Conversation]:
    """Create conversation from JSON dictionary.
    
    Args:
        data: JSON dictionary data
        
    Returns:
        Conversation object or None if invalid
    """
    return ConversationSerializer.from_dict(data)


def validate_json_conversation(data: Dict[str, Any]) -> bool:
    """Validate JSON conversation data.
    
    Args:
        data: Data to validate
        
    Returns:
        True if valid, False otherwise
    """
    errors = ConversationSerializer.validate_conversation_data(data)
    if errors:
        logger.warning(f"Conversation validation errors: {', '.join(errors)}")
        return False
    return True