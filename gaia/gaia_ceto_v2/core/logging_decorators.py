"""
Simple logging decorators to replace 267-line inheritance hierarchy.

Usage:
    @log_operation("my_operation")
    def my_function(x, y):
        return x + y
"""

import time
import logging
import json
from functools import wraps
from typing import Any, Callable, Optional, Dict
from datetime import datetime

logger = logging.getLogger(__name__)


def log_operation(operation_name: str = None, service: str = None):
    """
    Decorator for logging and timing operations.
    
    Args:
        operation_name: Name of operation (defaults to function name)
        service: Service name for grouping (e.g. "llm", "mcp")
    
    Example:
        @log_operation("generate", service="llm")
        def generate_text(prompt: str) -> str:
            return "response"
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, request_id: Optional[str] = None, **kwargs) -> Any:
            # Setup
            op_name = operation_name or func.__name__
            req_id = request_id or f"{service or 'op'}_{int(time.time()*1000)}"
            start_time = time.time()
            
            # Build context
            context = {
                "operation": op_name,
                "service": service,
                "request_id": req_id,
                "timestamp": datetime.utcnow().isoformat()
            }
            
            try:
                # Log start
                logger.info(f"→ {service or 'Service'}: {op_name} [req_id={req_id}]")
                
                # Execute - check if function accepts request_id
                import inspect
                sig = inspect.signature(func)
                if 'request_id' in sig.parameters:
                    result = func(*args, request_id=req_id, **kwargs)
                else:
                    result = func(*args, **kwargs)
                
                # Calculate duration
                duration_ms = (time.time() - start_time) * 1000
                
                # Log success
                logger.info(f"✅ {service or 'Service'}: {op_name} completed ({duration_ms:.0f}ms) [req_id={req_id}]")
                
                # Detailed logging if debug enabled
                if logger.isEnabledFor(logging.DEBUG):
                    logger.debug(json.dumps({
                        **context,
                        "duration_ms": duration_ms,
                        "success": True,
                        "args_count": len(args),
                        "kwargs_keys": list(kwargs.keys())
                    }))
                
                return result
                
            except Exception as e:
                # Calculate duration
                duration_ms = (time.time() - start_time) * 1000
                
                # Log error
                logger.error(f"❌ {service or 'Service'}: {op_name} failed - {type(e).__name__}: {str(e)} ({duration_ms:.0f}ms) [req_id={req_id}]")
                
                # Detailed error logging
                if logger.isEnabledFor(logging.DEBUG):
                    logger.debug(json.dumps({
                        **context,
                        "duration_ms": duration_ms,
                        "success": False,
                        "error_type": type(e).__name__,
                        "error_message": str(e)
                    }))
                
                raise
                
        return wrapper
    return decorator


def log_llm_operation(operation_name: str = None):
    """Specialized decorator for LLM operations."""
    return log_operation(operation_name, service="llm")


def log_mcp_operation(operation_name: str = None):
    """Specialized decorator for MCP operations."""
    return log_operation(operation_name, service="mcp")


def log_tool_operation(tool_name: str):
    """Specialized decorator for tool operations."""
    return log_operation(f"tool_{tool_name}", service="tools")


# Cache-aware decorator
def log_cached_operation(operation_name: str = None, service: str = None, cache_key_func: Callable = None):
    """
    Decorator for operations with caching support.
    
    Args:
        operation_name: Name of operation
        service: Service name
        cache_key_func: Function to extract cache key from args/kwargs
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs) -> Any:
            op_name = operation_name or func.__name__
            
            # Check if result indicates cache hit
            result = func(*args, **kwargs)
            
            # Simple heuristic: if function returns tuple (result, from_cache)
            if isinstance(result, tuple) and len(result) == 2:
                actual_result, from_cache = result
                if from_cache:
                    logger.info(f"🗃️ {service or 'Service'}: {op_name} (cached)")
                    return actual_result
            
            # Otherwise use normal logging
            return log_operation(op_name, service)(func)(*args, **kwargs)
            
        return wrapper
    return decorator


# Terminal output decorator (for important user-facing operations)
def log_with_terminal(operation_name: str = None, service: str = None):
    """
    Decorator that also prints to terminal for user visibility.
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)  
        def wrapper(*args, **kwargs) -> Any:
            op_name = operation_name or func.__name__
            start_time = time.time()
            
            try:
                result = func(*args, **kwargs)
                duration_ms = (time.time() - start_time) * 1000
                
                # Terminal output
                print(f"✅ {service or 'Service'}: {op_name} ({duration_ms:.0f}ms)")
                
                # Regular logging
                logger.info(f"{service or 'Service'}: {op_name} completed ({duration_ms:.0f}ms)")
                
                return result
                
            except Exception as e:
                duration_ms = (time.time() - start_time) * 1000
                
                # Terminal output
                print(f"❌ {service or 'Service'}: {op_name} failed - {type(e).__name__} ({duration_ms:.0f}ms)")
                
                # Regular logging
                logger.error(f"{service or 'Service'}: {op_name} failed - {e}")
                
                raise
                
        return wrapper
    return decorator


# Timing-only decorator (when you just need performance metrics)
def timed(name: str = None):
    """Simple timing decorator."""
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs) -> Any:
            start = time.time()
            try:
                return func(*args, **kwargs)
            finally:
                duration_ms = (time.time() - start) * 1000
                logger.debug(f"⏱️ {name or func.__name__}: {duration_ms:.0f}ms")
        return wrapper
    return decorator