#!/usr/bin/env python3
"""Simple docstore management CLI."""

import argparse
import sys
import shutil
from pathlib import Path

def clear_docstore():
    """Clear all conversation storage."""
    storage_dirs = [
        "/tmp/gaia_storage",
        "./tests/temp_storage"
    ]
    
    count = 0
    for storage_dir in storage_dirs:
        path = Path(storage_dir)
        if path.exists():
            for item in path.iterdir():
                if item.is_dir():
                    shutil.rmtree(item)
                else:
                    item.unlink()
                count += 1
    
    print(f"Cleared {count} docstore items")
    return count

def main():
    parser = argparse.ArgumentParser(description='Docstore Management CLI')
    parser.add_argument('command', choices=['clear'], help='Command to execute')
    
    args = parser.parse_args()
    
    if args.command == 'clear':
        clear_docstore()
    else:
        parser.print_help()
        sys.exit(1)

if __name__ == '__main__':
    main()