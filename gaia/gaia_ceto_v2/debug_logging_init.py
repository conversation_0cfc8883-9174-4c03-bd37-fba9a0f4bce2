#!/usr/bin/env python3
"""
Debug script to check if logging initialization is working.
"""

import sys
from pathlib import Path

# Add current directory to path for imports
sys.path.insert(0, str(Path(__file__).parent))

def test_logging_initialization():
    """Test if the logging systems initialize correctly."""
    print("🔍 Testing logging initialization...")
    
    # Clear existing logs
    log_dir = Path('/tmp/gaia_logs/ceto')
    if log_dir.exists():
        for log_file in log_dir.glob('*.log'):
            log_file.unlink()
    
    print("\n1. Testing logging_factory directly...")
    from core.logging_factory import create_llm_loggers
    
    wrap_logger, send_logger, rcv_logger = create_llm_loggers()
    print(f"   Wrap logger: {wrap_logger}")
    print(f"   Send logger: {send_logger}")
    print(f"   Rcv logger: {rcv_logger}")
    
    # Test basic logging
    test_msg = "direct_factory_test_456"
    wrap_logger.info(f"Direct factory test: {test_msg}")
    send_logger.info(f"SEND - direct - {test_msg}")
    rcv_logger.info(f"RECEIVE - direct - {test_msg}")
    
    # Check immediately
    wrap_file = log_dir / 'llmwrap.log'
    if wrap_file.exists():
        content = wrap_file.read_text()
        if test_msg in content:
            print("   ✅ Direct factory logging works!")
        else:
            print(f"   ❌ Message not in wrap log: {content}")
    else:
        print("   ❌ Wrap log file not created")
    
    print("\n2. Testing llm_wrapper module import...")
    
    # Import should trigger module-level logger creation
    from core.llm_wrapper import LiteLLMWrapper
    
    # Check if the module-level loggers were created
    import core.llm_wrapper as wrapper_module
    print(f"   Module has _wrap_logger: {hasattr(wrapper_module, '_wrap_logger')}")
    print(f"   Module has _send_logger: {hasattr(wrapper_module, '_send_logger')}")
    print(f"   Module has _rcv_logger: {hasattr(wrapper_module, '_rcv_logger')}")
    
    if hasattr(wrapper_module, '_wrap_logger'):
        module_wrap = wrapper_module._wrap_logger
        module_send = wrapper_module._send_logger
        module_rcv = wrapper_module._rcv_logger
        
        print(f"   Module _wrap_logger: {module_wrap}")
        print(f"   Module _send_logger: {module_send}")
        print(f"   Module _rcv_logger: {module_rcv}")
        
        # Test module loggers
        module_test_msg = "module_logger_test_789"
        module_wrap.info(f"Module test: {module_test_msg}")
        module_send.info(f"SEND - module - {module_test_msg}")
        module_rcv.info(f"RECEIVE - module - {module_test_msg}")
        
        # Check if module loggers write to files
        if wrap_file.exists():
            content = wrap_file.read_text()
            if module_test_msg in content:
                print("   ✅ Module-level loggers work!")
            else:
                print(f"   ❌ Module message not in log: {content}")
        else:
            print("   ❌ Module loggers didn't create file")
    
    print("\n3. Testing LiteLLMWrapper instance...")
    wrapper = LiteLLMWrapper()
    print(f"   Wrapper created: {wrapper}")
    
    # Force a direct test call to internal methods
    try:
        # Access the module loggers directly and test
        test_call_id = "wrapper_test_call_999"
        wrapper_module._wrap_logger.info(f"WRAPPER_TEST - {test_call_id} - Direct wrapper test")
        wrapper_module._send_logger.info(f"SEND - {test_call_id} - Direct wrapper send test")
        wrapper_module._rcv_logger.info(f"RECEIVE - {test_call_id} - Direct wrapper receive test")
        
        print("   ✅ Direct wrapper logger calls made")
        
        # Check if they appear in files
        if wrap_file.exists():
            content = wrap_file.read_text()
            if test_call_id in content:
                print("   ✅ Wrapper logger calls work!")
                print(f"   Content preview: {content[-200:]}")
            else:
                print(f"   ❌ Wrapper call ID not found: {content}")
        
    except Exception as e:
        print(f"   ❌ Wrapper logging test failed: {e}")
    
    print("\n4. Final log directory status...")
    if log_dir.exists():
        log_files = list(log_dir.glob('*.log'))
        print(f"   📂 Directory: {log_dir}")
        print(f"   📄 Total files: {len(log_files)}")
        for log_file in sorted(log_files):
            size = log_file.stat().st_size
            print(f"      • {log_file.name} ({size} bytes)")
    
    print("\n🎯 Logging initialization test complete!")

if __name__ == "__main__":
    test_logging_initialization()