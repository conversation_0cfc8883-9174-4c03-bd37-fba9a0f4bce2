{"mcpServers": {"firecrawl-mcp": {"enabled": false, "command": "npx", "args": ["firecrawl-mcp"], "env": {"FIRECRAWL_API_KEY": "{FIRECRAWL_API_KEY}"}, "namespace": "fc", "description": "Firecrawl MCP server spawned via npx (disabled - requires Node.js 18+, current: v12.22.9)"}, "dummy-mcp-server": {"enabled": false, "command": "python", "args": ["tests/mcp_interface/dummy_mcp_server.py", "9999"], "description": "A dummy server for E2E testing (disabled)."}}}