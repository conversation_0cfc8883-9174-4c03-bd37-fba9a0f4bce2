"""
Minimal MCP Server - A simplified version to test basic functionality.
"""

import logging
from fastmcp import FastMCP

logger = logging.getLogger(__name__)

# Create a minimal MCP server
mcp = FastMCP("gaia_ceto_v2_minimal")

@mcp.tool()
def echostring(text: str) -> str:
    """
    Echoes the given text.
    """
    logger.info(f"Echo called with: {text}")
    return f"Echo: {text}"

@mcp.tool()
def add_numbers(a: int, b: int) -> int:
    """
    Adds two numbers together.
    """
    result = a + b
    logger.info(f"Add called: {a} + {b} = {result}")
    return result

def run_server():
    """
    Runs the minimal MCP server over stdio.
    """
    logger.info("Starting minimal MCP server over stdio")
    # FastMCP runs over stdio by default
    mcp.run()

if __name__ == '__main__':
    logging.basicConfig(level=logging.INFO)
    try:
        run_server()
    except KeyboardInterrupt:
        logger.info("Shutting down minimal server.")
