#!/usr/bin/env python3
"""
Core functionality demo - Shows clean usage of the core business logic
with Mock LLM, completely isolated from any web frameworks.
"""

import sys
from pathlib import Path

# Add parent directories to path
parent_dir = Path(__file__).parent.parent.parent
sys.path.insert(0, str(parent_dir.parent))

# Import only from core - no interface dependencies
from gaia_ceto_v2.core import (
    ChatManager, MockLLM, 
    create_chat_manager, create_llm_provider
)


def demo_basic_chat():
    """Demonstrate basic chat functionality with Mock LLM."""
    print("🤖 Gaia Ceto v2 Core Demo - Mock LLM\n")
    
    # Create components using factory functions
    print("📦 Creating components...")
    llm = create_llm_provider('mock')
    manager = create_chat_manager(llm_provider=llm)
    print(f"✅ Created ChatManager with {llm.get_model_name()}")
    
    # Create conversation
    print("\n💬 Creating conversation...")
    conv_id = manager.create_conversation(
        user_id="demo_user", 
        title="Mock LLM Demo"
    )
    print(f"✅ Created conversation: {conv_id}")
    
    # Have a conversation
    print("\n🗣️  Having a conversation...")
    
    messages = [
        "Hello! How are you today?",
        "Can you help me understand how you work?",
        "What's the capital of France?",
        "Thanks for the help!"
    ]
    
    for i, message in enumerate(messages, 1):
        print(f"\n👤 User: {message}")
        response = manager.send_message(conv_id, message)
        print(f"🤖 Assistant: {response}")
    
    # Show conversation stats
    print("\n📊 Conversation Statistics:")
    stats = manager.get_conversation_stats(conv_id)
    if stats:
        print(f"   Total messages: {stats['total_messages']}")
        print(f"   User messages: {stats['user_messages']}")  
        print(f"   Assistant messages: {stats['assistant_messages']}")
        print(f"   Created: {stats['created_at']}")
    
    # Show system stats
    print("\n🖥️  System Statistics:")
    sys_stats = manager.get_system_stats()
    print(f"   Conversations: {sys_stats['total_conversations']}")
    print(f"   Total messages: {sys_stats['total_messages']}")
    print(f"   LLM provider: {sys_stats['llm_provider']}")
    if 'storage_backend' in sys_stats:
        print(f"   Storage backend: {sys_stats['storage_backend']}")
    else:
        print(f"   Storage: File-based")
    
    return manager, conv_id


def demo_custom_mock_response():
    """Demonstrate custom Mock LLM response function."""
    print("\n\n🎭 Custom Mock LLM Demo\n")
    
    # Create custom response function
    def funny_response(prompt, context, kwargs):
        responses = [
            f"Haha, you said: '{prompt}' - that's hilarious!",
            f"Oh my, '{prompt}'? How fascinating!",
            f"Interesting question about '{prompt}'. Let me think... 42!",
            f"'{prompt}' - classic! My circuits are tingling with joy!"
        ]
        import random
        return random.choice(responses)
    
    # Create components with custom mock
    custom_llm = MockLLM(response_func=funny_response)
    manager = create_chat_manager(llm_provider=custom_llm)
    
    print(f"✅ Created ChatManager with custom MockLLM")
    
    # Test conversation
    conv_id = manager.create_conversation(
        user_id="demo_user",
        title="Funny Mock Demo"
    )
    
    test_messages = [
        "What's your favorite color?",
        "Do you dream of electric sheep?",
        "How many roads must a man walk down?"
    ]
    
    for message in test_messages:
        print(f"\n👤 User: {message}")
        response = manager.send_message(conv_id, message)
        print(f"🎭 Funny Bot: {response}")


def demo_factory_functions():
    """Demonstrate the factory functions for easy setup."""
    print("\n\n🏭 Factory Functions Demo\n")
    
    # Show available providers
    from gaia_ceto_v2.core.llm_providers import get_available_providers
    
    print(f"📋 Available LLM providers: {get_available_providers()}")
    
    # Create manager with defaults
    print("\n🔧 Creating manager with defaults...")
    manager = create_chat_manager()  # Uses default storage dir and MockLLM
    
    print(f"✅ Default setup complete!")
    print(f"   LLM: {type(manager.message_service.llm_provider).__name__}")
    print(f"   Model: {manager.message_service.llm_provider.get_model_name()}")


def main():
    """Run all demos."""
    try:
        demo_basic_chat()
        demo_custom_mock_response()
        demo_factory_functions()
        
        print("\n\n🎉 All core demos completed successfully!")
        print("✅ The core business logic is working perfectly in isolation!")
        
    except Exception as e:
        print(f"\n❌ Demo failed: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0


if __name__ == '__main__':
    exit(main())