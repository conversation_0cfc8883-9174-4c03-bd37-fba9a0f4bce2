#!/usr/bin/env python3
"""
Unit tests for Conversation and Message classes - pure data model testing.

Tests the conversation data structures in isolation without any
storage, LLM, or tool dependencies.
"""

import sys
from pathlib import Path
import json
from datetime import datetime

# Add parent directories to path
parent_dir = Path(__file__).parent.parent.parent
sys.path.insert(0, str(parent_dir.parent))

# Import core conversation module
from gaia_ceto_v2.core.conversation import (
    Conversation, Message, create_conversation, conversation_summary
)


def test_message_creation():
    """Test Message class basic functionality."""
    print("💬 Testing Message class...")
    
    # Test basic message creation
    msg = Message(role="user", content="Hello world!")
    
    assert msg.role == "user"
    assert msg.content == "Hello world!"
    assert isinstance(msg.timestamp, str)
    assert "T" in msg.timestamp  # Should be ISO format
    # Message doesn't have message_id in the actual implementation
    
    # Test message serialization
    msg_dict = msg.to_dict()
    assert isinstance(msg_dict, dict)
    assert msg_dict["role"] == "user"
    assert msg_dict["content"] == "Hello world!"
    assert "timestamp" in msg_dict
    
    # Test message deserialization
    restored_msg = Message.from_dict(msg_dict)
    assert restored_msg.role == msg.role
    assert restored_msg.content == msg.content
    assert restored_msg.timestamp == msg.timestamp
    
    print("✅ Message class tests passed!")


def test_conversation_creation():
    """Test Conversation class basic functionality."""
    print("💭 Testing Conversation class...")
    
    # Test basic conversation creation
    conv = Conversation(
        user_id="test_user",
        title="Test Conversation"
    )
    
    assert conv.user_id == "test_user"
    assert conv.title == "Test Conversation"
    assert isinstance(conv.conversation_id, str)
    assert len(conv.conversation_id) > 0
    assert isinstance(conv.created_at, str)
    assert "T" in conv.created_at  # Should be ISO format
    assert isinstance(conv.updated_at, str)
    assert "T" in conv.updated_at  # Should be ISO format
    assert len(conv.messages) == 0
    assert isinstance(conv.metadata, dict)
    
    # Test auto-generated conversation ID
    conv2 = Conversation(user_id="user2")
    assert conv2.conversation_id != conv.conversation_id
    
    print("✅ Conversation creation tests passed!")


def test_conversation_message_operations():
    """Test adding and managing messages in conversation."""
    print("📝 Testing conversation message operations...")
    
    conv = Conversation(user_id="test_user", title="Message Test")
    
    # Test adding messages
    conv.add_message("user", "Hello!")
    assert len(conv.messages) == 1
    assert conv.messages[0].role == "user"
    assert conv.messages[0].content == "Hello!"
    
    conv.add_message("assistant", "Hi there!")
    assert len(conv.messages) == 2
    assert conv.messages[1].role == "assistant"
    assert conv.messages[1].content == "Hi there!"
    
    # Test updated_at changes (comparing ISO strings)
    original_updated = conv.updated_at
    import time
    time.sleep(0.001)  # Small delay to ensure timestamp difference
    conv.add_message("user", "How are you?")
    assert conv.updated_at > original_updated  # ISO string comparison works chronologically
    
    # Test message ordering (should be chronological - timestamps are ISO strings)
    timestamps = [msg.timestamp for msg in conv.messages]
    assert timestamps == sorted(timestamps), "Messages should be in chronological order"
    
    print("✅ Message operations tests passed!")


def test_conversation_serialization():
    """Test conversation serialization and deserialization."""
    print("💾 Testing conversation serialization...")
    
    # Create conversation with messages
    conv = Conversation(
        conversation_id="test-conv-123",
        user_id="test_user",
        title="Serialization Test",
        metadata={"test": True, "version": 1}
    )
    
    conv.add_message("user", "First message")
    conv.add_message("assistant", "First response")
    conv.add_message("user", "Second message")
    
    # Test to_dict
    conv_dict = conv.to_dict()
    assert isinstance(conv_dict, dict)
    assert conv_dict["conversation_id"] == "test-conv-123"
    assert conv_dict["user_id"] == "test_user"
    assert conv_dict["title"] == "Serialization Test"
    assert conv_dict["metadata"]["test"] is True
    assert len(conv_dict["messages"]) == 3
    
    # Test JSON serialization (should not crash)
    json_str = json.dumps(conv_dict, default=str)
    assert isinstance(json_str, str)
    assert "test-conv-123" in json_str
    
    # Test from_dict
    restored_conv = Conversation.from_dict(conv_dict)
    assert restored_conv.conversation_id == conv.conversation_id
    assert restored_conv.user_id == conv.user_id
    assert restored_conv.title == conv.title
    assert restored_conv.metadata == conv.metadata
    assert len(restored_conv.messages) == len(conv.messages)
    
    # Test message restoration
    for orig, restored in zip(conv.messages, restored_conv.messages):
        assert orig.role == restored.role
        assert orig.content == restored.content
        assert orig.timestamp == restored.timestamp
    
    print("✅ Conversation serialization tests passed!")


def test_factory_functions():
    """Test utility factory functions."""
    print("🏭 Testing factory functions...")
    
    # Test create_conversation
    conv = create_conversation(
        user_id="factory_user",
        title="Factory Test",
        custom_meta="test_value"
    )
    
    assert conv.user_id == "factory_user"
    assert conv.title == "Factory Test"
    assert len(conv.messages) == 0  # create_conversation doesn't add initial message
    assert conv.metadata["custom_meta"] == "test_value"
    
    # Test manually adding a message
    conv.add_message("user", "Hello from factory!")
    assert len(conv.messages) == 1
    assert conv.messages[0].role == "user"
    assert conv.messages[0].content == "Hello from factory!"
    
    # Test create_conversation without title/metadata
    conv2 = create_conversation(user_id="factory_user2")
    assert conv2.user_id == "factory_user2"
    assert len(conv2.messages) == 0
    
    # Test conversation_summary
    conv.add_message("assistant", "Factory response")
    conv.add_message("user", "Another message")
    
    summary = conversation_summary(conv)
    assert isinstance(summary, dict)
    assert summary["conversation_id"] == conv.conversation_id
    assert summary["message_count"] == 3  # user + assistant + user
    assert summary["user_id"] == "factory_user"
    assert summary["title"] == "Factory Test"
    
    print("✅ Factory function tests passed!")


def test_edge_cases():
    """Test edge cases and error conditions."""
    print("🔍 Testing edge cases...")
    
    # Test empty conversation
    conv = Conversation(user_id="edge_user")
    summary = conversation_summary(conv)
    assert summary["message_count"] == 0
    
    # Test conversation with metadata
    conv_with_meta = Conversation(
        user_id="meta_user",
        metadata={"custom": "value", "number": 42}
    )
    assert conv_with_meta.metadata["custom"] == "value"
    assert conv_with_meta.metadata["number"] == 42
    
    # Test serialization roundtrip preserves everything
    conv_with_meta.add_message("system", "System message")
    dict_form = conv_with_meta.to_dict()
    restored = Conversation.from_dict(dict_form)
    
    assert restored.metadata == conv_with_meta.metadata
    assert len(restored.messages) == 1
    assert restored.messages[0].role == "system"
    
    print("✅ Edge case tests passed!")


def main():
    """Run all conversation unit tests."""
    print("🧪 Testing Conversation Data Models - Unit Tests\n")
    
    try:
        test_message_creation()
        test_conversation_creation()
        test_conversation_message_operations()
        test_conversation_serialization()
        test_factory_functions()
        test_edge_cases()
        
        print("\n🎉 All conversation unit tests passed!")
        print("✅ Conversation data models working correctly in isolation!")
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0


if __name__ == '__main__':
    exit(main())