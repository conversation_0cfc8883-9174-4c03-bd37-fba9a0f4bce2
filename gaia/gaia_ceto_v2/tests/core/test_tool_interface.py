#!/usr/bin/env python3
"""
Test suite for tool interface protocols.

Tests the core tool interface definitions and protocol compliance.
"""

import sys
from pathlib import Path
from typing import Dict, Any, List, Optional

# Add parent directories to path for imports
parent_dir = Path(__file__).parent.parent.parent
sys.path.insert(0, str(parent_dir.parent))

from gaia_ceto_v2.core.tool_interface import CetoToolInterface, CetoToolRegistryInterface


class MockTool:
    """Mock tool implementation for testing protocol compliance."""
    
    def __init__(self, name: str = "mock_tool", description: str = "Mock tool for testing"):
        self.name = name
        self.description = description
    
    def execute(self, **kwargs) -> str:
        """Execute the mock tool."""
        return f"Mock tool executed with: {kwargs}"
    
    def get_schema(self) -> Dict[str, Any]:
        """Get mock tool schema."""
        return {
            "type": "object",
            "properties": {
                "input": {"type": "string", "description": "Test input"}
            }
        }


class MockToolRegistry:
    """Mock tool registry implementation for testing protocol compliance."""
    
    def __init__(self):
        self._tools = {}
        # Add a default mock tool
        mock_tool = MockTool()
        self._tools[mock_tool.name] = mock_tool
    
    def has_tool(self, name: str) -> bool:
        """Check if tool exists."""
        return name in self._tools
    
    def execute_tool(self, name: str, **kwargs) -> str:
        """Execute tool by name."""
        if name not in self._tools:
            raise ValueError(f"Tool '{name}' not found")
        return self._tools[name].execute(**kwargs)
    
    def list_tools(self) -> List[Dict[str, Any]]:
        """List all tools."""
        return [
            {
                "name": tool.name,
                "description": tool.description,
                "schema": tool.get_schema()
            }
            for tool in self._tools.values()
        ]
    
    def get_tool(self, name: str) -> Optional[CetoToolInterface]:
        """Get tool by name."""
        return self._tools.get(name)


def test_tool_protocol_compliance():
    """Test that mock tool implements the tool protocol correctly."""
    tool = MockTool("test_tool", "Test tool")
    
    # Test protocol attributes
    assert hasattr(tool, 'name')
    assert hasattr(tool, 'description')
    assert tool.name == "test_tool"
    assert tool.description == "Test tool"
    
    # Test protocol methods
    assert hasattr(tool, 'execute')
    assert hasattr(tool, 'get_schema')
    
    # Test method execution
    result = tool.execute(test_param="value")
    assert "test_param" in result
    assert "value" in result
    
    schema = tool.get_schema()
    assert isinstance(schema, dict)
    assert "type" in schema


def test_registry_protocol_compliance():
    """Test that mock registry implements the registry protocol correctly."""
    registry = MockToolRegistry()
    
    # Test protocol methods exist
    assert hasattr(registry, 'has_tool')
    assert hasattr(registry, 'execute_tool')
    assert hasattr(registry, 'list_tools')
    assert hasattr(registry, 'get_tool')
    
    # Test has_tool functionality
    assert registry.has_tool("mock_tool") is True
    assert registry.has_tool("nonexistent") is False
    
    # Test tool execution
    result = registry.execute_tool("mock_tool", test="data")
    assert "test" in result
    assert "data" in result
    
    # Test tool listing
    tools = registry.list_tools()
    assert isinstance(tools, list)
    assert len(tools) == 1
    assert tools[0]["name"] == "mock_tool"
    
    # Test tool retrieval
    tool = registry.get_tool("mock_tool")
    assert tool is not None
    assert tool.name == "mock_tool"
    
    missing_tool = registry.get_tool("nonexistent")
    assert missing_tool is None


def test_registry_error_handling():
    """Test registry error handling for missing tools."""
    registry = MockToolRegistry()
    
    try:
        registry.execute_tool("nonexistent_tool")
        assert False, "Should raise ValueError for missing tool"
    except ValueError as e:
        assert "not found" in str(e).lower()


def test_tool_interface_type_annotations():
    """Test that protocol type annotations are correct."""
    # This test verifies the protocols are properly defined
    # by checking they can be used as type hints
    
    def uses_tool(tool: CetoToolInterface) -> str:
        return tool.execute()
    
    def uses_registry(registry: CetoToolRegistryInterface) -> bool:
        return registry.has_tool("test")
    
    # Test with mock implementations
    mock_tool = MockTool()
    mock_registry = MockToolRegistry()
    
    result = uses_tool(mock_tool)
    assert isinstance(result, str)
    
    has_tool = uses_registry(mock_registry)
    assert isinstance(has_tool, bool)


def test_multiple_tools_in_registry():
    """Test registry with multiple tools."""
    registry = MockToolRegistry()
    
    # Add additional tools
    tool2 = MockTool("second_tool", "Second test tool")
    tool3 = MockTool("third_tool", "Third test tool")
    registry._tools["second_tool"] = tool2
    registry._tools["third_tool"] = tool3
    
    # Test listing multiple tools
    tools = registry.list_tools()
    assert len(tools) == 3
    
    tool_names = [tool["name"] for tool in tools]
    assert "mock_tool" in tool_names
    assert "second_tool" in tool_names
    assert "third_tool" in tool_names
    
    # Test each tool can be executed
    for name in tool_names:
        result = registry.execute_tool(name, test="param")
        assert "test" in result
        assert "param" in result


def run_tests():
    """Run all tool interface tests."""
    test_functions = [
        test_tool_protocol_compliance,
        test_registry_protocol_compliance,
        test_registry_error_handling,
        test_tool_interface_type_annotations,
        test_multiple_tools_in_registry
    ]
    
    print("🧪 Testing Tool Interface Protocols")
    print("="*40)
    
    for test_func in test_functions:
        try:
            print(f"  Running {test_func.__name__}...")
            test_func()
            print(f"  ✅ {test_func.__name__} PASSED")
        except Exception as e:
            print(f"  ❌ {test_func.__name__} FAILED: {e}")
            return False
    
    print("\n🎉 All tool interface tests passed!")
    print("✅ Protocol compliance validated")
    print("✅ Type annotations working correctly")
    print("✅ Error handling tested")
    
    return True


if __name__ == '__main__':
    success = run_tests()
    exit(0 if success else 1)