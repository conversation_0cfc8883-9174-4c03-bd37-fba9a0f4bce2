"""
Test suite for the LiteLLMWrapper.
"""

import unittest
from unittest.mock import patch, MagicMock
import sys
from pathlib import Path

# Add parent directories to path for imports
parent_dir = Path(__file__).parent.parent.parent
sys.path.insert(0, str(parent_dir.parent))

from gaia_ceto_v2.core.llm_wrapper import LiteLLMWrapper


class TestLiteLLMWrapper(unittest.TestCase):

    def setUp(self):
        """Set up for the tests."""
        self.wrapper = LiteLLMWrapper()

    @patch('litellm.completion')
    def test_completion_success(self, mock_completion):
        """Test a successful completion call."""
        # Mock the litellm.completion function
        mock_response = MagicMock()
        mock_response.choices[0].message.content = "Test response"
        mock_completion.return_value = mock_response

        # Call the completion method
        model = "gpt-3.5-turbo"
        messages = [{"role": "user", "content": "Hello"}]
        response = self.wrapper.completion(model, messages)

        # Assertions
        self.assertEqual(response, "Test response")
        mock_completion.assert_called_once_with(model=model, messages=messages)

    @patch('litellm.completion')
    def test_completion_failure(self, mock_completion):
        """Test a failing completion call."""
        # Mock the litellm.completion function to raise an exception
        mock_completion.side_effect = Exception("Test error")

        # Call the completion method
        model = "gpt-3.5-turbo"
        messages = [{"role": "user", "content": "Hello"}]
        response = self.wrapper.completion(model, messages)

        # Assertions
        self.assertIsNone(response)

    def test_caching(self):
        """Test the caching functionality."""
        cache = {}
        wrapper = LiteLLMWrapper(cache=cache)
        key = wrapper._generate_cache_key("test_model", [{"role": "user", "content": "test"}])
        cache[key] = "cached_response"

        response = wrapper.completion("test_model", [{"role": "user", "content": "test"}])
        self.assertEqual(response, "cached_response")


if __name__ == '__main__':
    unittest.main()
