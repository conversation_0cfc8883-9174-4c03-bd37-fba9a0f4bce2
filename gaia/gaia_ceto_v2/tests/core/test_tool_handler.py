"""
Test suite for tool handler functionality.
"""

import unittest
from unittest.mock import Mock
import sys
from pathlib import Path

# Add parent directories to path for imports
parent_dir = Path(__file__).parent.parent.parent
sys.path.insert(0, str(parent_dir.parent))

from gaia_ceto_v2.core.tool_handler import <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>all, <PERSON><PERSON><PERSON><PERSON><PERSON>
from gaia_ceto_v2.core.chat_manager import create_chat_manager


class MockToolHandler(ToolHandler):
    """Mock tool handler for testing."""
    
    def __init__(self):
        self.tools = {"echo": "Mock echo tool"}
    
    def detect_tools(self, message: str):
        if "call echo" in message.lower():
            return [ToolCall(name="echo", args={"text": "test"}, raw_text="call echo test")]
        return []
    
    def execute_tools(self, calls):
        results = []
        for call in calls:
            if call.name == "echo":
                results.append(ToolResult(
                    tool_name="echo",
                    success=True,
                    result="Echo: " + call.args.get("text", ""),
                    error=None
                ))
        return results
    
    def get_available_tools(self):
        return list(self.tools.keys())


class TestToolHandler(unittest.TestCase):
    
    def test_no_tool_handler(self):
        """Test NoToolHandler does nothing."""
        handler = NoToolHandler()
        
        self.assertEqual(handler.detect_tools("call something"), [])
        self.assertEqual(handler.execute_tools([]), [])
        self.assertEqual(handler.get_available_tools(), [])
    
    def test_mock_tool_handler(self):
        """Test MockToolHandler basic functionality."""
        handler = MockToolHandler()
        
        # Test tool detection
        calls = handler.detect_tools("call echo test")
        self.assertEqual(len(calls), 1)
        self.assertEqual(calls[0].name, "echo")
        
        # Test tool execution
        results = handler.execute_tools(calls)
        self.assertEqual(len(results), 1)
        self.assertTrue(results[0].success)
        self.assertIn("Echo:", results[0].result)
        
        # Test available tools
        tools = handler.get_available_tools()
        self.assertEqual(tools, ["echo"])
    
    def test_chat_manager_with_no_tools(self):
        """Test ChatManager with NoToolHandler."""
        chat_manager = create_chat_manager(with_mcp=False)
        
        # Should have no tools
        self.assertEqual(chat_manager.get_available_tools(), [])
        
        # Should process messages normally
        conv_id = chat_manager.create_conversation("test_user")
        response = chat_manager.send_message(conv_id, "Hello")
        self.assertIn("Mock LLM response", response)
    
    def test_chat_manager_with_mock_tools(self):
        """Test ChatManager with mock tools."""
        mock_handler = MockToolHandler()
        
        # Create chat manager with mock tool handler
        from gaia_ceto_v2.core import create_chat_manager
        from gaia_ceto_v2.core.file_storage import FileConversationRepository
        from gaia_ceto_v2.core.conversation_cache import ConversationCache
        from gaia_ceto_v2.core.chat_statistics import ChatStatistics
        from gaia_ceto_v2.core.llm_providers import MockLLM
        from gaia_ceto_v2.core.chat_manager import ChatManager
        
        chat_manager = ChatManager(
            repository=FileConversationRepository('/tmp/test_conversations'),
            llm_provider=MockLLM(),
            cache=ConversationCache(),
            statistics=ChatStatistics(),
            tool_handler=mock_handler
        )
        
        # Should have tools
        self.assertEqual(chat_manager.get_available_tools(), ["echo"])
        
        # Should execute tools
        conv_id = chat_manager.create_conversation("test_user")
        response = chat_manager.send_message(conv_id, "call echo hello")
        
        # Response should include tool results
        self.assertIn("Tool Results:", response)
        self.assertIn("Echo:", response)


if __name__ == '__main__':
    unittest.main()