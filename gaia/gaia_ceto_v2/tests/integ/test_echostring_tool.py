#!/usr/bin/env python3
"""
Test script for the echostring tool integration.

This demonstrates how the echostring tool works with the MockLLM
in the new clean architecture.
"""

import sys
from pathlib import Path

# Add parent directories to path
parent_dir = Path(__file__).parent.parent.parent
sys.path.insert(0, str(parent_dir.parent))

# Import from core
from gaia_ceto_v2.core import (
    create_chat_manager, MockLLM, MemoryStorage,
    default_registry, create_tool_registry,
    echostring
)


def test_echostring_tool_direct():
    """Test the echostring tool directly."""
    print("🔧 Testing echostring tool directly...")
    
    # Test the function directly
    result = echostring("hello world")
    expected = "hello world, hello world, hello world fer SURE!"
    
    print(f"📝 Input: 'hello world'")
    print(f"🤖 Output: '{result}'")
    
    assert result == expected, f"Expected '{expected}', got '{result}'"
    print("✅ Direct tool test passed!")


def test_tool_registry():
    """Test the tool registry functionality."""
    print("\n🗂️  Testing tool registry...")
    
    # Test default registry
    assert default_registry.has_tool("echostring"), "echostring tool not found in default registry"
    
    # Test tool listing
    tools = default_registry.list_tools()
    tool_names = [tool['name'] for tool in tools]
    
    print(f"📋 Available tools: {tool_names}")
    assert "echostring" in tool_names, "echostring not in tool list"
    
    # Test tool execution via registry
    result = default_registry.execute_tool("echostring", text="test phrase")
    expected = "test phrase, test phrase, test phrase fer SURE!"
    
    print(f"📝 Registry execution result: '{result}'")
    assert result == expected, f"Registry execution failed"
    
    print("✅ Tool registry test passed!")


def test_mock_llm_with_tools():
    """Test MockLLM with tool calling capability."""
    print("\n🤖 Testing MockLLM with tools...")
    
    # Create MockLLM with tool registry
    llm = MockLLM(tool_registry=default_registry)
    
    # Use the factory function with temporary storage
    manager = create_chat_manager(
        storage_dir="/tmp/test_echostring",
        llm_provider=llm
    )
    
    # Create conversation
    conv_id = manager.create_conversation(
        user_id="test_user",
        title="Tool Test Chat"
    )
    
    # Test direct tool call format: "echostring hello"
    print("\n👤 User: echostring hello from tool test")
    response = manager.send_message(conv_id, "echostring hello from tool test")
    print(f"🤖 Assistant: {response}")
    
    # Should contain the tool execution result
    assert "Tool 'echostring' executed:" in response, f"Tool execution not detected in: {response}"
    assert "fer SURE" in response, f"Expected tool output not found in: {response}"
    
    # Test quoted input: 'echostring "quoted input"'
    print("\n👤 User: echostring \"quoted input\"")
    response = manager.send_message(conv_id, 'echostring "quoted input"')
    print(f"🤖 Assistant: {response}")
    
    assert "Tool 'echostring' executed:" in response, f"Quoted tool execution not detected"
    assert "quoted input" in response, f"Quoted input not processed correctly"
    
    # Test regular chat (should not trigger tool)
    print("\n👤 User: Hello, how are you?")
    response = manager.send_message(conv_id, "Hello, how are you?")
    print(f"🤖 Assistant: {response}")
    
    # Should get regular mock response, not tool execution
    assert "Mock LLM response to:" in response, f"Regular chat not working: {response}"
    assert "Tool" not in response, f"Tool wrongly triggered for regular chat: {response}"
    
    print("✅ MockLLM tool integration test passed!")


def test_other_tools():
    """Test other built-in tools."""
    print("\n⏰ Testing other built-in tools...")
    
    # Test get_time tool
    llm = MockLLM(tool_registry=default_registry)
    manager = create_chat_manager(
        storage_dir="/tmp/test_other_tools",
        llm_provider=llm
    )
    
    conv_id = manager.create_conversation(user_id="test_user", title="Other Tools Test")
    
    # Test get_time
    print("\n👤 User: get_time")
    response = manager.send_message(conv_id, "get_time")
    print(f"🤖 Assistant: {response}")
    
    assert "Tool 'get_time' executed:" in response, "get_time tool not executed"
    # Should contain an ISO timestamp
    assert "T" in response, "Time format doesn't look like ISO"
    
    # Test add_numbers
    print("\n👤 User: add_numbers 5 3")
    response = manager.send_message(conv_id, "add_numbers 5 3")
    print(f"🤖 Assistant: {response}")
    
    # This won't work perfectly because add_numbers expects named parameters
    # But it shows the tool system is extensible
    
    print("✅ Other tools test completed!")


def test_conversation_with_tools():
    """Test full conversation flow with mixed tool calls and chat."""
    print("\n💬 Testing conversation flow with tools...")
    
    llm = MockLLM(tool_registry=default_registry)
    manager = create_chat_manager(
        storage_dir="/tmp/test_conversation_flow",
        llm_provider=llm
    )
    
    conv_id = manager.create_conversation(
        user_id="test_user",
        title="Mixed Tool Chat"
    )
    
    # Simulate a mixed conversation
    test_messages = [
        ("Hello! Can you help me test some tools?", "regular"),
        ("echostring testing tools is fun", "tool"),
        ("That's great! What time is it?", "regular"),
        ("get_time", "tool"),
        ("Thanks! The tools are working well.", "regular")
    ]
    
    for message, expected_type in test_messages:
        print(f"\n👤 User: {message}")
        response = manager.send_message(conv_id, message)
        print(f"🤖 Assistant: {response}")
        
        if expected_type == "tool":
            assert "Tool" in response, f"Expected tool execution for: {message}"
        else:
            assert "Mock LLM response to:" in response, f"Expected regular response for: {message}"
    
    # Check conversation stats
    stats = manager.get_conversation_stats(conv_id)
    assert stats['total_messages'] == 10, f"Expected 10 messages, got {stats['total_messages']}"
    
    print("✅ Conversation flow test passed!")


def main():
    """Run all echostring tool tests."""
    print("🚀 Testing Echostring Tool Integration\n")
    
    try:
        test_echostring_tool_direct()
        test_tool_registry()
        test_mock_llm_with_tools()
        test_other_tools()
        test_conversation_with_tools()
        
        print("\n🎉 All echostring tool tests passed!")
        print("✅ The tool system is working correctly with MockLLM!")
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0


if __name__ == '__main__':
    exit(main())