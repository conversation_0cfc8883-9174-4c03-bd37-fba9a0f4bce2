#!/usr/bin/env python3
"""
Integration test to verify the factored modules work together correctly.

This test demonstrates the clean interfaces and confirms that the factored
chatobj functionality is working as expected.
"""

import sys
import os
from pathlib import Path

# Add parent directories to path so we can import our modules
parent_dir = Path(__file__).parent.parent.parent
sys.path.insert(0, str(parent_dir.parent))

# Import from core using the same pattern as working tests
from gaia_ceto_v2.core import (
    Conversation, create_conversation, MockLLM, create_llm_provider,
    ChatManager, create_chat_manager
)


def test_basic_conversation_flow():
    """Test basic conversation creation and messaging."""
    print("🧪 Testing basic conversation flow...")
    
    # Create components using factory function
    manager = create_chat_manager()
    
    # Create conversation
    conv_id = manager.create_conversation(user_id="test_user", title="Test Chat")
    print(f"✅ Created conversation: {conv_id}")
    
    # Send message
    response = manager.send_message(conv_id, "Hello, how are you?")
    print(f"✅ Got response: {response}")
    
    # Get conversation
    conv_data = manager.get_conversation(conv_id)
    assert conv_data is not None
    assert len(conv_data['messages']) == 2  # user + assistant
    print(f"✅ Conversation has {len(conv_data['messages'])} messages")
    
    # List conversations
    conversations = manager.list_conversations()
    assert len(conversations) >= 1  # Should have at least the one we created
    print(f"✅ Found {len(conversations)} conversations total")
    
    print("✅ Basic conversation flow test passed!\n")


def test_multiple_llm_providers():
    """Test switching between different LLM providers."""
    print("🧪 Testing multiple LLM providers...")
    
    # Test MockLLM
    mock_llm = MockLLM()
    manager = create_chat_manager(llm_provider=mock_llm)
    
    conv_id = manager.create_conversation(user_id="test_user", title="Mock Test")
    response1 = manager.send_message(conv_id, "Test message")
    assert "Mock LLM response" in response1
    print(f"✅ Mock LLM responded: {response1[:50]}...")
    
    # Switch to different mock with custom response
    def custom_response(prompt, context, kwargs):
        return f"Custom response to: {prompt}"
    
    custom_llm = MockLLM(response_func=custom_response)
    manager.set_llm_provider(custom_llm)
    
    response2 = manager.send_message(conv_id, "Another test")
    assert "Custom response to:" in response2
    print(f"✅ Custom LLM responded: {response2[:50]}...")
    
    print("✅ Multiple LLM providers test passed!\n")


def test_storage_backends():
    """Test different storage backends."""
    print("🧪 Testing storage backends...")
    
    # Test memory storage with factory function
    manager1 = create_chat_manager()  # Uses memory by default
    conv_id = manager1.create_conversation(user_id="user1", title="Memory Test")
    manager1.send_message(conv_id, "Hello memory!")
    
    # Verify conversation exists via manager
    conv_data = manager1.get_conversation(conv_id)
    assert conv_data is not None
    conversations = manager1.list_conversations()
    assert len(conversations) >= 1
    print("✅ Memory storage working")
    
    # Test file storage with temporary directory
    import tempfile
    with tempfile.TemporaryDirectory() as temp_dir:
        manager2 = create_chat_manager(storage_dir=temp_dir)
        conv_id2 = manager2.create_conversation(user_id="user2", title="File Test")
        manager2.send_message(conv_id2, "Hello files!")
        
        # Verify conversation exists via manager
        conv_data2 = manager2.get_conversation(conv_id2)
        assert conv_data2 is not None
        conversations = manager2.list_conversations()
        assert len(conversations) >= 1
        print("✅ File storage working")
    
    print("✅ Storage backends test passed!\n")


def test_conversation_data_model():
    """Test the pure conversation data model."""
    print("🧪 Testing conversation data model...")
    
    # Create conversation
    conv = create_conversation(user_id="test_user", title="Data Model Test")
    
    # Add messages
    conv.add_message("user", "Hello")
    conv.add_message("assistant", "Hi there!")
    conv.add_message("user", "How are you?")
    
    # Test data access
    assert conv.get_message_count() == 3
    messages = conv.get_messages(limit=2)
    assert len(messages) == 2  # Should get last 2 messages
    
    last_msg = conv.get_last_message()
    assert last_msg['content'] == "How are you?"
    assert last_msg['role'] == "user"
    
    # Test serialization
    data = conv.to_dict()
    assert data['title'] == "Data Model Test"
    assert len(data['messages']) == 3
    
    # Test deserialization
    conv2 = Conversation.from_dict(data)
    assert conv2.conversation_id == conv.conversation_id
    assert conv2.get_message_count() == 3
    
    print("✅ Conversation data model test passed!\n")


def test_factory_functions():
    """Test the factory functions for easy object creation.""" 
    print("🧪 Testing factory functions...")
    
    # Test LLM provider factory
    mock_llm = create_llm_provider('mock')
    assert isinstance(mock_llm, MockLLM)
    print("✅ LLM provider factory working")
    
    # Test storage factory (via ChatManager)
    # Storage is now handled via ChatManager factory, not direct storage creation
    import tempfile
    with tempfile.TemporaryDirectory() as temp_dir:
        manager = create_chat_manager(storage_dir=temp_dir)
        assert manager is not None
        # Test that storage works by creating a conversation
        conv_id = manager.create_conversation(user_id="storage_test", title="Storage Test")
        assert conv_id is not None
    print("✅ Storage factory working")
    
    # Test chat manager factory
    manager = create_chat_manager(storage_dir='/tmp/test_conversations')
    assert isinstance(manager, ChatManager)
    print("✅ Chat manager factory working")
    
    print("✅ Factory functions test passed!\n")


def test_system_stats():
    """Test system statistics and monitoring."""
    print("🧪 Testing system statistics...")
    
    manager = create_chat_manager()
    
    # Create some test data
    for i in range(3):
        conv_id = manager.create_conversation(user_id=f"user{i}", title=f"Chat {i}")
        manager.send_message(conv_id, f"Hello from user {i}")
        manager.send_message(conv_id, f"Another message from user {i}")
    
    # Get stats
    stats = manager.get_system_stats()
    assert stats['total_conversations'] == 3
    # Each conversation has: user message + assistant response + user message + assistant response = 4 messages
    assert stats['total_messages'] == 12  # 4 messages per conversation * 3 conversations
    assert stats['llm_provider'] == 'MockLLM'
    # unique_users might not be implemented, so check if it exists
    if 'unique_users' in stats:
        assert stats['unique_users'] == 3
    
    print(f"✅ System stats: {stats}")
    print("✅ System statistics test passed!\n")


def main():
    """Run all integration tests."""
    print("🚀 Running Gaia Ceto v2 Integration Tests\n")
    
    try:
        test_basic_conversation_flow()
        test_multiple_llm_providers()
        test_storage_backends()
        test_conversation_data_model()
        test_factory_functions()
        test_system_stats()
        
        print("🎉 All integration tests passed!")
        print("✅ The factored chatobj functionality is working correctly!")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0


if __name__ == '__main__':
    exit(main())