#!/usr/bin/env python3
"""
Unit tests for CetoTool system - tools in isolation.

Tests the tool implementations and registry without any core integration.
Focuses on tool functionality, schemas, and registry operations.
"""

import sys
from pathlib import Path
from datetime import datetime

# Add parent directories to path
parent_dir = Path(__file__).parent.parent.parent
sys.path.insert(0, str(parent_dir.parent))

# Import tools directly
from gaia_ceto_v2.tools import (
    CetoTool, CetoFunctionTool, CetoToolRegistry,
    echostring, get_time, add_numbers,
    default_registry, create_tool_registry
)


def test_echostring_function():
    """Test echostring function directly."""
    print("🔧 Testing echostring function...")
    
    # Test basic functionality
    result = echostring("hello")
    expected = "hello, hello, hello fer SURE!"
    assert result == expected, f"Expected '{expected}', got '{result}'"
    
    # Test with spaces
    result = echostring("hello world")
    expected = "hello world, hello world, hello world fer SURE!"
    assert result == expected, f"Expected '{expected}', got '{result}'"
    
    # Test empty string
    result = echostring("")
    expected = ", ,  fer SURE!"
    assert result == expected, f"Expected '{expected}', got '{result}'"
    
    print("✅ echostring function tests passed!")


def test_get_time_function():
    """Test get_time function directly."""
    print("🕐 Testing get_time function...")
    
    result = get_time()
    
    # Should be an ISO format timestamp
    assert isinstance(result, str), "get_time should return string"
    assert "T" in result, "Should contain ISO datetime separator"
    assert len(result) > 10, "Should be a full timestamp"
    
    # Should be parseable as datetime
    try:
        parsed = datetime.fromisoformat(result)
        assert parsed is not None, "Should be valid ISO datetime"
    except ValueError:
        assert False, f"Could not parse timestamp: {result}"
    
    print(f"✅ get_time returned valid timestamp: {result}")


def test_add_numbers_function():
    """Test add_numbers function directly."""
    print("➕ Testing add_numbers function...")
    
    # Test basic addition (note: add_numbers takes strings and returns string)
    result = add_numbers("5", "3")
    assert result == "8.0", f"Expected '8.0', got {result}"
    
    # Test with floats
    result = add_numbers("2.5", "1.5")
    assert result == "4.0", f"Expected '4.0', got {result}"
    
    # Test with negative numbers
    result = add_numbers("-5", "3")
    assert result == "-2.0", f"Expected '-2.0', got {result}"
    
    print("✅ add_numbers function tests passed!")


def test_function_tool_wrapper():
    """Test CetoFunctionTool wrapper functionality."""
    print("🛠️  Testing CetoFunctionTool wrapper...")
    
    # Create tool from function
    echo_tool = CetoFunctionTool(
        name="test_echo",
        description="Test echo tool",
        func=echostring
    )
    
    # Test basic properties
    assert echo_tool.name == "test_echo"
    assert echo_tool.description == "Test echo tool"
    
    # Test execution
    result = echo_tool.execute(text="wrapper test")
    expected = "wrapper test, wrapper test, wrapper test fer SURE!"
    assert result == expected, f"Wrapper execution failed: {result}"
    
    # Test schema generation
    schema = echo_tool.get_schema()
    assert isinstance(schema, dict), "Schema should be dict"
    assert "type" in schema, "Schema should have type"
    assert "properties" in schema, "Schema should have properties"
    assert "text" in schema["properties"], "Schema should have text parameter"
    
    print("✅ CetoFunctionTool wrapper tests passed!")


def test_tool_registry_operations():
    """Test tool registry basic operations."""
    print("📋 Testing CetoToolRegistry operations...")
    
    # Create fresh registry
    registry = CetoToolRegistry()
    
    # Test empty registry
    assert not registry.has_tool("nonexistent"), "Empty registry should not have tools"
    assert len(registry.list_tools()) == 0, "Empty registry should list no tools"
    
    # Add a tool
    echo_tool = CetoFunctionTool("echo", "Echo tool", echostring)
    registry.register_tool(echo_tool)
    
    # Test tool presence
    assert registry.has_tool("echo"), "Registry should have registered tool"
    assert not registry.has_tool("nonexistent"), "Registry should not have unregistered tool"
    
    # Test tool listing
    tools = registry.list_tools()
    assert len(tools) == 1, f"Expected 1 tool, got {len(tools)}"
    assert tools[0]["name"] == "echo", "Tool name should match"
    
    # Test tool execution
    result = registry.execute_tool("echo", text="registry test")
    expected = "registry test, registry test, registry test fer SURE!"
    assert result == expected, f"Registry execution failed: {result}"
    
    # Test tool retrieval
    retrieved_tool = registry.get_tool("echo")
    assert retrieved_tool is not None, "Should retrieve registered tool"
    assert retrieved_tool.name == "echo", "Retrieved tool should have correct name"
    
    print("✅ CetoToolRegistry operations tests passed!")


def test_default_registry():
    """Test the default registry with built-in tools."""
    print("🏪 Testing default registry...")
    
    # Test that default registry has expected tools
    expected_tools = ["echostring", "get_time", "add_numbers"]
    
    for tool_name in expected_tools:
        assert default_registry.has_tool(tool_name), f"Default registry missing {tool_name}"
    
    # Test tool listing
    tools = default_registry.list_tools()
    tool_names = [tool["name"] for tool in tools]
    
    for expected in expected_tools:
        assert expected in tool_names, f"Default registry missing {expected} in list"
    
    # Test echostring execution
    result = default_registry.execute_tool("echostring", text="default test")
    assert "fer SURE" in result, "echostring should work via default registry"
    
    # Test get_time execution
    result = default_registry.execute_tool("get_time")
    assert "T" in result, "get_time should return ISO timestamp via registry"
    
    # Test add_numbers execution (takes string params, returns string)
    result = default_registry.execute_tool("add_numbers", a="10", b="5")
    assert result == "15.0", f"add_numbers via registry should work, got {result}"
    
    print("✅ Default registry tests passed!")


def test_registry_factory():
    """Test create_tool_registry factory function."""
    print("🏭 Testing registry factory...")
    
    # Test default creation
    registry1 = create_tool_registry()
    assert registry1.has_tool("echostring"), "Factory should create registry with default tools"
    
    # Test empty creation
    registry2 = CetoToolRegistry()  # Create empty registry directly
    assert not registry2.has_tool("echostring"), "Empty registry should not have default tools"
    
    print("✅ Registry factory tests passed!")


def test_error_handling():
    """Test error handling in tool operations."""
    print("⚠️  Testing error handling...")
    
    registry = CetoToolRegistry()
    
    # Test executing non-existent tool
    try:
        result = registry.execute_tool("nonexistent")
        assert False, "Should raise exception for non-existent tool"
    except Exception as e:
        assert "not found" in str(e).lower(), f"Expected 'not found' error, got: {e}"
    
    # Test tool with invalid parameters
    registry.register_tool(CetoFunctionTool("add", "Add tool", add_numbers))
    
    try:
        result = registry.execute_tool("add", wrong_param="5")
        assert "Error executing tool" in result, f"Expected error message, got: {result}"
    except Exception:
        # Some error handling approaches may raise exceptions
        pass
    
    print("✅ Error handling tests passed!")


def main():
    """Run all tool unit tests."""
    print("🧪 Testing CetoTool System - Unit Tests\n")
    
    try:
        test_echostring_function()
        test_get_time_function() 
        test_add_numbers_function()
        test_function_tool_wrapper()
        test_tool_registry_operations()
        test_default_registry()
        test_registry_factory()
        test_error_handling()
        
        print("\n🎉 All CetoTool unit tests passed!")
        print("✅ Tool system is working correctly in isolation!")
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0


if __name__ == '__main__':
    exit(main())