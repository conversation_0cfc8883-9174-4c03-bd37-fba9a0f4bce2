"""
Simple MCP server test - focuses on basic functionality.
"""

import unittest
import socket
import requests
import time
import sys
from pathlib import Path

parent_dir = Path(__file__).parent.parent.parent
sys.path.insert(0, str(parent_dir.parent))

from gaia_ceto_v2.mcp_interface.mcp_server import MCPServer

def find_free_port():
    """Find an available port."""
    with socket.socket() as s:
        s.bind(('', 0))
        return s.getsockname()[1]

def wait_for_server(url, timeout=10):
    """Wait for server to respond."""
    for _ in range(timeout):
        try:
            response = requests.get(url, timeout=1)
            # MCP server returns error for basic HTTP, but that means it's working
            return response.status_code in [200, 400, 406]  # Any response is good
        except:
            time.sleep(1)
    return False

class TestSimpleMCP(unittest.TestCase):
    
    @classmethod
    def setUpClass(cls):
        """Start a test server."""
        config_path = parent_dir / "mcp_interface" / "server_config.json"
        cls.server_manager = MCPServer(config_path)
        cls.port = find_free_port()
        
        # Update config with dynamic port
        if "dummy-mcp-server" in cls.server_manager.config["mcpServers"]:
            cls.server_manager.config["mcpServers"]["dummy-mcp-server"]["args"] = [
                "tests/mcp_interface/dummy_mcp_server.py", str(cls.port)
            ]
        
        print(f"\nStarting test server on port {cls.port}...")
        cls.server_manager.start_external_server("dummy-mcp-server", python_executable=sys.executable)
        
        cls.server_url = f"http://localhost:{cls.port}/mcp/"
        
        # Wait for server
        print(f"Waiting for server at {cls.server_url}...")
        if not wait_for_server(cls.server_url, timeout=10):
            # Debug: check if process is running
            process = cls.server_manager.processes.get("dummy-mcp-server")
            if process:
                print(f"Process poll result: {process.poll()}")
                if process.poll() is not None:
                    stdout, stderr = process.communicate()
                    print(f"Process stderr: {stderr.decode()}")
            raise AssertionError("Test server failed to start")
    
    @classmethod 
    def tearDownClass(cls):
        """Clean up."""
        if hasattr(cls, 'server_manager'):
            print("Cleaning up test server...")
            cls.server_manager.stop_all_external_servers()
    
    def test_server_responds(self):
        """Test that server responds to HTTP requests."""
        response = requests.get(self.server_url, timeout=5)
        self.assertIn(response.status_code, [200, 400, 406])  # MCP protocol responses
    
    def test_server_process_running(self):
        """Test that server process is actually running."""
        process = self.server_manager.processes.get("dummy-mcp-server")
        self.assertIsNotNone(process)
        self.assertIsNone(process.poll())  # None means still running

if __name__ == '__main__':
    unittest.main()