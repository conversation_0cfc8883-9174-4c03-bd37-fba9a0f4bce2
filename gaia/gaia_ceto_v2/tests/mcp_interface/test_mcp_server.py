"""
Test suite for the MCPServer.
"""

import unittest
from unittest.mock import patch, MagicMock
from pathlib import Path
import sys

# Add parent directories to path for imports
parent_dir = Path(__file__).parent.parent.parent
sys.path.insert(0, str(parent_dir.parent))

from gaia_ceto_v2.mcp_interface.mcp_server import MCPServer


class TestMCPServer(unittest.TestCase):

    def setUp(self):
        """Set up for the tests."""
        self.config_path = Path(__file__).parent.parent.parent / "mcp_interface" / "server_config.json"
        self.server = MCPServer(self.config_path)

    def test_load_config(self):
        """Test that the configuration is loaded correctly."""
        self.assertIn("mcpServers", self.server.config)
        self.assertIn("firecrawl-mcp", self.server.config["mcpServers"])

    @patch('subprocess.Popen')
    def test_start_external_server(self, mock_popen):
        """Test starting an external server."""
        self.server.start_external_server("firecrawl-mcp")
        mock_popen.assert_called_once()


if __name__ == '__main__':
    unittest.main()