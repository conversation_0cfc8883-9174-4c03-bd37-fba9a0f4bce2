"""
A dummy MCP server for End-to-End testing.

This script runs a minimal, predictable MCP server that can be launched
as an external process by our integration tests.
"""

import sys
import logging
from pathlib import Path
from fastmcp import FastMCP

# Add tools to path
sys.path.insert(0, str(Path(__file__).parent.parent.parent))
from tools.tools import echostring as core_echostring

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create an MCP server instance
mcp = FastMCP("dummy-server")

@mcp.tool()
def dummy_tool() -> str:
    """A simple tool that always returns a success message."""
    return "SUCCESS"

@mcp.tool()
def echostring(text: str) -> str:
    """Echo back the input text with confirmation."""
    return core_echostring(text)

if __name__ == "__main__":
    # Default port
    port = 9999
    
    # Allow overriding port via command-line argument
    if len(sys.argv) > 1:
        try:
            port = int(sys.argv[1])
        except ValueError:
            logger.error(f"Invalid port specified: {sys.argv[1]}. Using default {port}.")
            
    logger.info(f"Starting dummy MCP server on port {port}...")
    
    # Run the server
    mcp.run(transport="http", port=port)
