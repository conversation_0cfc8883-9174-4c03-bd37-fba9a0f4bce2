"""
A simple MCP client for testing.
"""

import asyncio
from mcp.client.session import ClientSession
from mcp.client.streamable_http import streamablehttp_client

DUMMY_SERVER_URL = "http://localhost:9998/mcp/"

async def main():
    """
    Connects to the dummy server and calls the dummy_tool.
    """
    async with streamablehttp_client(DUMMY_SERVER_URL) as (read, write, _):
        async with ClientSession(read, write) as session:
            await session.initialize()
            tool_list = await session.list_tools()
            print(f"Available tools: {[t.name for t in tool_list.tools]}")
            result = await session.call_tool("dummy_tool")
            print(f"Result: {result.content[0].text}")

if __name__ == "__main__":
    asyncio.run(main())
