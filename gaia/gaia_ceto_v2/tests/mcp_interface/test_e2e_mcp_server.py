
"""
End-to-End test suite for the MCPServerManager.

This test verifies that the MCPServerManager can successfully launch and
manage a real, external MCP server process.
"""

import unittest
import time
import sys
import socket
import requests
from pathlib import Path
import asyncio

# Add parent directories to path for imports
parent_dir = Path(__file__).parent.parent.parent
sys.path.insert(0, str(parent_dir.parent))

from gaia_ceto_v2.mcp_interface.mcp_server import MCPServer
from mcp.client.session import ClientSession
from mcp.client.streamable_http import streamablehttp_client

def find_free_port():
    """Find an available port."""
    with socket.socket() as s:
        s.bind(('', 0))
        return s.getsockname()[1]

def wait_for_server(url, timeout=10):
    """Wait for server to respond."""
    for _ in range(timeout):
        try:
            requests.get(url, timeout=1)
            return True
        except:
            time.sleep(1)
    return False

# Dynamic port allocation
DUMMY_SERVER_HOST = "localhost"
DUMMY_SERVER_PORT = find_free_port()
DUMMY_SERVER_URL = f"http://{DUMMY_SERVER_HOST}:{DUMMY_SERVER_PORT}"


class TestE2EMCPServer(unittest.TestCase):

    @classmethod
    def setUpClass(cls):
        """Set up the test environment for the entire class."""
        config_path = parent_dir / "mcp_interface" / "server_config.json"
        cls.server_manager = MCPServer(config_path)
        
        # Update the config with dynamic port
        if "dummy-mcp-server" in cls.server_manager.config["mcpServers"]:
            cls.server_manager.config["mcpServers"]["dummy-mcp-server"]["args"] = [
                "tests/mcp_interface/dummy_mcp_server.py", str(DUMMY_SERVER_PORT)
            ]
        
        print(f"\nStarting dummy MCP server on port {DUMMY_SERVER_PORT}...")
        cls.server_manager.start_external_server("dummy-mcp-server", python_executable=sys.executable)
        
        # Wait for server to be ready
        if not wait_for_server(DUMMY_SERVER_URL + "/mcp/", timeout=10):
            process = cls.server_manager.processes.get("dummy-mcp-server")
            if process:
                stdout, stderr = process.communicate()
                print(f"Dummy server stdout: {stdout.decode()}")
                print(f"Dummy server stderr: {stderr.decode()}")
            raise AssertionError("Dummy server failed to start or respond.")

    @classmethod
    def tearDownClass(cls):
        """Clean up after tests."""
        if hasattr(cls, 'server_manager'):
            print("Stopping dummy MCP server...")
            cls.server_manager.stop_all_external_servers()

    def test_connect_and_call_tool(self):
        """
        Verify we can connect to the spawned server and call its tool.
        """
        async def run_test():
            # Use the official MCP client to connect to our dummy server
            async with streamablehttp_client(DUMMY_SERVER_URL) as (read, write, _):
                async with ClientSession(read, write) as session:
                    await session.initialize()
                    # List tools to ensure connection is working
                    tool_list = await session.list_tools()
                    self.assertIn("dummy_tool", [t.name for t in tool_list.tools])
                    
                    # Call the dummy tool
                    result = await session.call_tool("dummy_tool")
                    
                    # Verify the result
                    self.assertEqual(result.content[0].text, "SUCCESS")
                    print(f"Successfully called 'dummy_tool' and received: {result.content[0].text}")

        # Run the async test
        asyncio.run(run_test())


if __name__ == '__main__':
    unittest.main()
