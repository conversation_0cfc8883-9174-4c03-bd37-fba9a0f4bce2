#!/usr/bin/env python3
"""Simple conversations management CLI."""

import argparse
import sys
import shutil
from pathlib import Path
from gaia_ceto_v2.settings import GAIA_SETTINGS

def clear_conversations():
    """Clear all conversation storage."""
    storage_dirs = [
        GAIA_SETTINGS.GAIA_CONVERSATIONS_DIR,
        "./tests/temp_storage"
    ]
    
    count = 0
    for storage_dir in storage_dirs:
        path = Path(storage_dir)
        if path.exists():
            for item in path.iterdir():
                if item.is_dir():
                    shutil.rmtree(item)
                    count += 1
                elif item.is_file():
                    item.unlink()
                    count += 1
    
    print(f"Cleared {count} conversation items")
    return count

def main():
    parser = argparse.ArgumentParser(description='Conversations Management CLI')
    parser.add_argument('command', choices=['clear'], help='Command to execute')
    
    args = parser.parse_args()
    
    if args.command == 'clear':
        clear_conversations()
    else:
        parser.print_help()
        sys.exit(1)

if __name__ == '__main__':
    main()