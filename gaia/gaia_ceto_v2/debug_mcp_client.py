#!/usr/bin/env python3
"""
Debug MCP Client Connection

Test the exact connection issue between the MCP client and server.
"""

import asyncio
import logging
import sys

# Set up logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

async def test_mcp_connection():
    """Test MCP client connection step by step."""
    server_url = "http://localhost:9000/mcp/"
    
    print("🔍 DEBUGGING MCP CLIENT CONNECTION")
    print("=" * 50)
    print(f"Server URL: {server_url}")
    
    try:
        # Import MCP client
        from mcp.client.session import ClientSession
        from mcp.client.streamable_http import streamablehttp_client
        print("✅ MCP client imports successful")
        
        # Test connection
        print("🔌 Attempting to connect to MCP server...")
        
        async with streamablehttp_client(server_url) as (read, write, _):
            print("✅ HTTP connection established")
            
            async with ClientSession(read, write) as session:
                print("✅ Client session created")
                
                # Initialize session 
                print("🤝 Initializing session...")
                await session.initialize()
                print("✅ Session initialized successfully")
                
                # List tools
                print("🔧 Discovering tools...")
                tool_list = await session.list_tools()
                print(f"✅ Found {len(tool_list.tools)} tools:")
                
                for tool in tool_list.tools:
                    print(f"   - {tool.name}: {tool.description}")
                
                # Test a tool call
                if tool_list.tools:
                    test_tool = tool_list.tools[0].name
                    print(f"\n🧪 Testing tool call: {test_tool}")
                    
                    if test_tool == "echostring":
                        result = await session.call_tool(test_tool, {"statement": "test message"})
                        print(f"✅ Tool call result: {result.content[0].text}")
                    elif test_tool == "dummy_tool":
                        result = await session.call_tool(test_tool, {})
                        print(f"✅ Tool call result: {result.content[0].text}")
                
                return True
                
    except Exception as e:
        print(f"❌ Connection failed: {e}")
        logger.exception("Detailed error:")
        return False

async def main():
    """Main test function."""
    success = await test_mcp_connection()
    
    if success:
        print("\n🎉 MCP CLIENT CONNECTION SUCCESSFUL!")
        print("The terminal chat issue must be elsewhere.")
    else:
        print("\n💥 MCP CLIENT CONNECTION FAILED!")
        print("This explains why terminal chat can't discover tools.")
    
    return success

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)